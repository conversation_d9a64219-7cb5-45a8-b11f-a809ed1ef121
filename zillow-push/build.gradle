apply plugin: 'org.springframework.boot'
apply plugin: 'com.gorylenko.gradle-git-properties'

// bootJar {
//     archiveBaseName.value('app')
//     archiveVersion.value('')
// }
// springBoot {
//     buildInfo()
// }

description = 'zillow-push'
configurations.all {
    // check for updates every build
    // resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
dependencies {
    api project(':chime-zillow-service')
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-actuator-autoconfigure'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis:3.1.0'
    implementation 'io.micrometer:micrometer-registry-prometheus:1.12.2'
    implementation 'dev.akkinoc.spring.boot:logback-access-spring-boot-starter:4.1.1'
    runtimeOnly 'mysql:mysql-connector-java'
    api 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.2'
    api 'org.springframework.cloud:spring-cloud-starter-openfeign'
    api 'org.springframework.cloud:spring-cloud-loadbalancer'

    api("com.squareup.okhttp3:okhttp:4.9.1")
    api 'commons-httpclient:commons-httpclient:3.1'
//    api 'org.apache.commons:commons-pool2:2.6.0'

//    api('com.homethy.microservice:microservice-web-mvc-configuration:4.5-spring-SNAPSHOT')
    api 'com.homethy:homethy-configcenter-client:4.5-spring-SNAPSHOT'
    api('com.homethy:homethy-util-global-config:3.3.0-SNAPSHOT')
    api 'com.homethy:homethy-util-redis:4.28-SNAPSHOT'
    api 'com.homethy:homethy-kafka-producer:4.25-SNAPSHOT'
//    api('com.homethy.platform.pii:encrypt-datasource:5.2.0-SNAPSHOT')
    api 'com.homethy:homethy-util-jackson:2.5.4-SNAPSHOT'

    api 'org.apache.commons:commons-dbcp2:2.9.0'
    api('com.homethy.microservice:microservice-config:4.5-spring-SNAPSHOT')
    implementation 'org.jsoup:jsoup:1.16.1'
    api 'org.apache.commons:commons-lang3:3.12.0'
    api 'com.fasterxml.jackson.core:jackson-annotations:2.14.2'
    api 'com.fasterxml.jackson.core:jackson-databind:2.14.2'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.2'
    api 'mysql:mysql-connector-java:8.0.11'
    api 'com.squareup.okhttp3:logging-interceptor:4.9.1'
    api 'com.amazonaws:aws-lambda-java-core:1.2.2'
    api 'com.amazonaws:aws-lambda-java-events:3.11.0'
    api 'io.github.crac:org-crac:0.1.3'
    api 'com.amazonaws.serverless:aws-serverless-java-container-springboot3:2.0.0-M1'

    testImplementation 'mysql:mysql-connector-java:8.0.11'
}
bootJar {
    enabled = false
}
jar {
    enabled = false
}
task buildZip(type: Zip) {
    from compileJava
    from processResources
    into('lib') {
        from(configurations.runtimeClasspath) {
            exclude 'tomcat-embed-*'
            exclude 'lombok-*'
        }
    }
}
build.dependsOn buildZip
tasks.named('buildZip') {
    dependsOn tasks.named('generateGitProperties')
}
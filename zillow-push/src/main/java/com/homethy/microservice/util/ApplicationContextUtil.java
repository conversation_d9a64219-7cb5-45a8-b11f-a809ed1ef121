package com.homethy.microservice.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ApplicationContextUtil implements ApplicationContextAware {

  private static ApplicationContext applicationContext;

  public static ApplicationContext getApplicationContext() {
    return applicationContext;
  }

  public void setApplicationContext(ApplicationContext context) {
    ApplicationContextUtil.applicationContext = context;
  }

  public static void setApplication(ApplicationContext context) {
    ApplicationContextUtil.applicationContext = context;
  }

  public static Object getBean(String name) {
    return applicationContext.getBean(name);
  }

  public static <T> T getBean(Class<T> requiredType) {
    return applicationContext.getBean(requiredType);
  }

  public static <T> Map<String, T> getBeansOfType(Class<T> requiredType) {
    return applicationContext.getBeansOfType(requiredType);
  }
}

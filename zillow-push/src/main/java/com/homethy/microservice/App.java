package com.homethy.microservice;

import com.homethy.microservice.util.ApplicationContextUtil;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.ImportResource;

@EnableFeignClients({
    "com.homethy.persistence.service",
    "com.homethy.microservice",
    "com.homethy.zillow.client"
})
@SpringBootApplication(
    exclude = {
        DataSourceAutoConfiguration.class,
        MongoAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        TransactionAutoConfiguration.class,
        JooqAutoConfiguration.class
    })
@ImportResource(locations = "classpath*:applicationContext.xml")
@ComponentScans({
    @ComponentScan("com.homethy"),
    @ComponentScan("com.homethy.platform.pii"),
    @ComponentScan("com.homethy.microservice"),
    @ComponentScan("com.homethy.persistence")})
public class App {

  public static void main(String[] args) {
    init();
    ApplicationContext application = new SpringApplicationBuilder(App.class)
        .web(WebApplicationType.NONE)
        .run(args);
    ApplicationContextUtil.setApplication(application);
  }

  public static void init() {
    //    System.setProperty("spring.profiles.active", "test");
    System.setProperty("spring.main.allow-bean-definition-overriding", "true");
    System.setProperty("spring.main.allow-circular-references", "true");
    System.setProperty("configcenter.redis.name", "thirdparty");
  }
}

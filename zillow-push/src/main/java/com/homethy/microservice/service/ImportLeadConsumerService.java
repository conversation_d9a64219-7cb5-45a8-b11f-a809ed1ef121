package com.homethy.microservice.service;

import com.homethy.microservice.client.model.OwnershipScopeEnumBo;
import com.homethy.microservice.client.model.zillow.ZillowImportLeadSetting;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.client.ZillowClient;
import com.homethy.zillow.model.constant.ImportEnum;
import com.homethy.zillow.model.constant.InitMessage;
import com.homethy.zillow.model.constant.KafkaTopic;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.constant.ZillowStateInfo;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.model.constant.zillow.ZillowInitResult;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.KafkaService;
import com.homethy.zillow.service.ZillowImportService;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowOauth2TokenService;
import com.homethy.zillow.util.ThreadUtil;

import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ImportLeadConsumerService {

  @Autowired
  private ZillowLeadService zillowLeadService;
  @Autowired
  private ZillowImportService zillowImportService;
  @Autowired
  private ZillowClient zillowClient;
  @Autowired
  private KafkaService kafkaService;
  @Autowired
  private ZillowOauth2TokenService zillowOauth2TokenService;
  @Autowired
  private com.homethy.microservice.client.thirdparty.integration.api.ZillowClient thirdpartyZillowClient;

  public void consume(List<KafkaRecord> records) {
    records.stream()
        .map(record -> ThreadUtil.submit(() -> doConsumer(record)))
        .toList()
        .forEach(future -> {
          ThreadUtil.getResult(future, false);
        });
    processFinishedLeadImports(records);
  }

  private boolean doConsumer(KafkaRecord record) {
    if (KafkaTopic.ZILLOW_IMPORT_LEAD_TOPIC.equals(record.getTopic())) {
      importLead(record);
    } else if (KafkaTopic.ZILLOW_INIT_ZILLOW_LEAD.equals(record.getTopic())) {
      initZillowLead(record);
    }
    return true;
  }

  private void initZillowLead(KafkaRecord record) {
    try {
      log.info("key: {}, value: {}", record.getKey(), record.getValue());
      InitMessage initMessage = JacksonUtils.fromJson(record.getValue(), InitMessage.class);
      init(initMessage);
    } catch (Exception e) {
      log.warn("init zillow lead error, key is: {}, value is: {}, exception: ", record.getKey(),
          record.getValue(), e);
    }
  }

  private void init(InitMessage initMessage) {
    ZillowStateInfo stateInfo = initMessage.getZillowStateInfo();
    ZillowImportLeadSetting zillowImportSetting =
        thirdpartyZillowClient.getZillowByOwnership(stateInfo.getOwnershipId(),
            OwnershipScopeEnumBo.valueOf(stateInfo.getOwnershipScope().name()),
            stateInfo.getOperUserId());
    if (zillowImportSetting == null) {
      log.info("zillowImportSetting is null, ownership is: {}", initMessage);
      return;
    }
    ZillowOauth2Token token = zillowOauth2TokenService.getToken(stateInfo.toOwnership());
    if (token == null) {
      log.info("can't find binding, stateInfo: {}", stateInfo);
      return;
    }
//    if (!whiteListService.isZillowOauth2WhiteList(user.getTeamId())) {
//      log.info("user is not in white list, userId: {}", userId);
//      return;
//    }
    LocalDate now = LocalDate.now();
    LocalDate localDate = now.minusMonths(24);
    ZoneId zoneId = ZoneId.systemDefault();
    long time = Date.from(localDate.atStartOfDay().atZone(zoneId).toInstant()).getTime();
//    String createdAfter = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    ZillowInitResult zillowInitResult = null;
    if (StringUtils.isEmpty(initMessage.getNextPage())) {
      zillowInitResult = zillowClient.pullContacts(token.getCallToken(), 50, null);
    } else {
      zillowInitResult = zillowClient.pullContacts(token.getCallToken(), 50, initMessage.getNextPage());
    }
    log.info("zillow init result: {}", JacksonUtils.toJson(zillowInitResult));
    if (zillowInitResult.getData() == null || CollectionUtils.isEmpty(zillowInitResult.getData().getContacts())) {
      return;
    }
    judgeAndImport(zillowInitResult.getData().getContacts(), time, stateInfo);
    initMessage.setNextPage(zillowInitResult.getPagination().getNextCursor());
    if (zillowInitResult.getPagination() != null && StringUtils.isNotEmpty(zillowInitResult.getPagination().getNextCursor())) {
      kafkaService.sendMessage(KafkaTopic.ZILLOW_INIT_ZILLOW_LEAD, stateInfo.getOwnershipId(),
          JacksonUtils.toJson(initMessage), true);
      log.info("has next, stateInfo: {}", stateInfo);
    }else {
      log.info("no more lead sync,send notification");
      zillowLeadService.markLeadImportFinished(stateInfo.getOperUserId());
    }
  }

  private void judgeAndImport(List<ZillowContactInfo> contactInfos, long modifyTime,
                              ZillowStateInfo stateInfo) {
    for (ZillowContactInfo zillowContactInfo : contactInfos) {
      try {
        if (zillowContactInfo.getLastModifiedAt() < modifyTime) {
          continue;
        }
        ZillowImportLog zillowImportLog = new ZillowImportLog();
        zillowImportLog.setImportContact(JacksonUtils.toJson(zillowContactInfo));
        zillowImportLog.setContactInfo(JacksonUtils.toJson(zillowContactInfo));
        zillowImportLog.setAssignEmail(zillowContactInfo.getAssignedAgent().getEmail());
        zillowImportLog.setBindingAgentId(stateInfo.getOwnershipId());
        zillowImportLog.setContactId(zillowContactInfo.getContactId());
        long importId = zillowImportService.saveImport(zillowImportLog);
        log.info("init zillow lead import id: {}", importId);
        zillowLeadService.importZillowLead(zillowContactInfo, stateInfo.toOwnership(), importId,
            true, stateInfo.getOperUserId());
      } catch (Exception e) {
        log.info("init import lead error, zillowContactInfo: {}, exception: ", zillowContactInfo, e);
      }
    }
  }

  void processFinishedLeadImports(List<KafkaRecord> records) {
    log.info("Processing finished lead imports, total records: {}", records.size());
    records.stream()
        .filter(record -> KafkaTopic.ZILLOW_INIT_ZILLOW_LEAD.equals(record.getTopic()))
        .map(record -> {
          try {
            InitMessage initMessage = JacksonUtils.fromJson(record.getValue(), InitMessage.class);
            return initMessage.getZillowStateInfo();
          } catch (Exception e) {
            log.warn("Parse init message failed for record: {}", record.getValue(), e);
            return null;
          }
        })
        .filter(Objects::nonNull)
        .map(ZillowStateInfo::getOperUserId)
        .filter(userId -> {
          boolean isFinished = zillowLeadService.isLeadImportFinished(userId);
          if (isFinished) {
            log.info("Lead import finished for userId: {}", userId);
          }
          return isFinished;
        })
        .forEach(userId -> {
          try {
            log.info("Sending notification for finished lead import, userId: {}", userId);
            zillowLeadService.handleNotification(userId);
          } catch (Exception e) {
            log.warn("Failed to send notification for userId: {}", userId, e);
          }
        });
  }

  private void importLead(KafkaRecord record) {
    try {
      Long key = record.getKey();
      log.info("topic:{} {}-{},key: {}", record.getTopic(), record.getPartition(), record.getOffset(),
          key);
      zillowLeadService.importLead(key, false);
    } catch (Exception e) {
      log.warn("exception: ", e);
      zillowImportService.saveImportResult(record.getKey(), ImportEnum.HAVE_EXCEPTION,
          e.getMessage());
    }
  }
}

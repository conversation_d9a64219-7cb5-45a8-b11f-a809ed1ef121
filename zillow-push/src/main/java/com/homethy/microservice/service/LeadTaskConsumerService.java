package com.homethy.microservice.service;

import com.homethy.microservice.client.model.LeadTask;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.impl.SyncCreateToFubTaskService;
import com.homethy.zillow.service.impl.SyncDeleteToFubTaskService;
import com.homethy.zillow.service.impl.SyncUpdateToFubTaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LeadTaskConsumerService {
  public static final String TOPIC_UPDATE_TASK = "smart_plan.task_update";
  private static final String TOPIC_CREATE_TASK = "smart_plan.task_create";
  private static final String TOPIC_DELETE_TASK = "smart_plan.task_delete";

  @Autowired
  private SyncCreateToFubTaskService syncCreateToFubTaskService;
  @Autowired
  private SyncUpdateToFubTaskService syncUpdateToFubTaskService;
  @Autowired
  private SyncDeleteToFubTaskService syncDeleteToFubTaskService;

  public void consume(List<KafkaRecord> records) {
    for (KafkaRecord record : records) {
      try {
        LeadTask leadTask = getLeadTask(record);
        switch (record.getTopic()) {
          case TOPIC_CREATE_TASK:
            syncCreateToFubTaskService.handle(leadTask);
            break;
          case TOPIC_UPDATE_TASK:
            syncUpdateToFubTaskService.handle(leadTask);
            break;
          case TOPIC_DELETE_TASK:
            syncDeleteToFubTaskService.handle(leadTask);
        }
      } catch (Exception e) {
        log.warn("consume error, topic: {}, record: {}", record.getTopic(), record.getValue(), e);
      }
    }
  }

  private LeadTask getLeadTask(KafkaRecord record) throws IOException {
    return JacksonUtils.fromJson(record.getValue(), LeadTask.class);
  }
}

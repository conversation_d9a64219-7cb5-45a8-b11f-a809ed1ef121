package com.homethy.microservice.service;


import com.homethy.microservice.client.model.Appointment;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.FubAppointmentService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AppointmentConsumerService {
  public static final String TOPIC_UPDATE_APPOINTMENT = "topic.smart.plan.appointment.update";
  private static final String TOPIC_CREATE_APPOINTMENT = "topic.smart.plan.appointment.create";
  private static final String TOPIC_DELETE_APPOINTMENT = "topic.smart.plan.appointment.delete";
  @Autowired
  private FubAppointmentService fubAppointmentService;

  public void consume(List<KafkaRecord> records) {
    for (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> record : records) {
      try {
        Appointment appointment = getAppointment(record.getValue());
        switch (record.getTopic()) {
          case TOPIC_CREATE_APPOINTMENT:
            fubAppointmentService.handCreateAppointment(appointment);
            break;
          case TOPIC_UPDATE_APPOINTMENT:
            fubAppointmentService.handUpdateAppointment(appointment);
            break;
          case TOPIC_DELETE_APPOINTMENT:
            fubAppointmentService.handDeleteAppointment(appointment);
        }
      } catch (Exception e) {
        log.warn("Error processing record: {}", record, e);
      }
    }
  }

  private Appointment getAppointment(String value) throws IOException {
    return JacksonUtils.fromJson(value, Appointment.class);
  }

}

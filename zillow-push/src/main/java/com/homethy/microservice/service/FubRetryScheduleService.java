package com.homethy.microservice.service;

import com.homethy.fub.dao.FubImportLogDao;
import com.homethy.fub.dao.FubNotifyLogDao;
import com.homethy.fub.service.FubImportLogService;
import com.homethy.fub.service.FubRetryNotifyService;
import com.homethy.fub.service.FubRetryService;
import com.homethy.i18n.util.MsgException;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FubRetryScheduleService {

  @Autowired
  private FubImportLogDao fubImportLogDao;
  @Autowired
  private FubNotifyLogDao fubNotifyLogDao;
  @Autowired
  private List<FubRetryService> fubRetryServices;
  @Autowired
  private List<FubRetryNotifyService> fubRetryNotifyServices;
  @Autowired
  private FubImportLogService fubImportLogService;

  public boolean retryScheduleImport() {
    List<FubImportLog> fubImportLogs = fubImportLogDao.queryFailImport();
    Map<String, List<FubImportLog>> typeMap = fubImportLogs.stream()
        .collect(Collectors.groupingBy(l -> l.getType().name()));
    typeMap.forEach((k, v) -> {
      FubRetryService retryService = getRetryService(k);
      if (retryService == null) {
        log.error("not find retryService handle, type: {}, size: {}", k, v.size());
        v.forEach( e -> {
          fubImportLogService.saveFailLog(e.getId(), ZillowErrorCodeEnum.FUB_HANDLER_NO_FOUND);
        });
        return;
      }
      v.forEach(e -> {
        try {
          fubImportLogService.retryAddOne(e.getId());
          retryService.handle(retryService.getFubContext(e), retryService.getInput(e));
        } catch (MsgException ex) {
          if (ex.getKey() == ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED) {
            return;
          }
          log.warn("Retry failed for logId: {}, error: {}", e.getId(), ex.getMessage());
        } catch (Exception ex) {
          log.warn("Unexpected error during retry for logId: {}", e.getId(), ex);
        }
      });
    });
    return true;
  }

  FubRetryService getRetryService(String type) {
    if (fubRetryServices == null || fubRetryServices.isEmpty()) {
      log.error("No FubRetryService implementations found");
      return null;
    }
    
    for (FubRetryService fubRetryService : fubRetryServices) {
      if (type.equals(fubRetryService.getImportType().name())) {
        return fubRetryService;
      }
    }
    
    log.debug("No retry service found for type: {}", type);
    return null;
  }

  public boolean retryScheduleNotify() {
    List<FubNotifyLog> notifyLogs = fubNotifyLogDao.queryRetryLogs();
    Map<String, List<FubNotifyLog>> notifyMap = notifyLogs.stream().collect(Collectors.groupingBy(FubNotifyLog::getType));
    notifyMap.forEach((k, v) -> {
      FubRetryNotifyService retryNotifyService = getRetryNotifyService(k);
      if (retryNotifyService == null) {
        log.error("not find retryNotifyService handle, type: {}", k);
        return;
      }
      v.forEach(e -> {
        try {
          fubImportLogService.retryAddOne(e.getId());
          retryNotifyService.handle(retryNotifyService.getInput(e), e);
        } catch (MsgException ex) {
          if (ex.getKey() == ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED) {
            return;
          }
          log.warn("Retry failed for logId: {}, error: {}", e.getId(), ex.getMessage());
        } catch (Exception ex) {
          log.warn("Unexpected error during retry for logId: {}", e.getId(), ex);
        }
      });
    });
    log.info("retryScheduleNotify called - implementation pending");
    return true;
  }

  FubRetryNotifyService getRetryNotifyService(String type) {
    if (fubRetryNotifyServices == null || fubRetryNotifyServices.isEmpty()) {
      log.error("No FubRetryNotifyService implementations found");
      return null;
    }

    for (FubRetryNotifyService fubRetryNotifyService : fubRetryNotifyServices) {
      if (type.equals(fubRetryNotifyService.getImportType().name())) {
        return fubRetryNotifyService;
      }
    }

    log.debug("No retry service found for type: {}", type);
    return null;
  }
}

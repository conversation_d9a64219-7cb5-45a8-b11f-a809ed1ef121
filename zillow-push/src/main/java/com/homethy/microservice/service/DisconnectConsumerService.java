package com.homethy.microservice.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.user.UserService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.constant.FunctionChangeKafka;
import com.homethy.zillow.model.constant.KafkaTopic;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.model.po.UserSeatStatusKafka;
import com.homethy.zillow.service.WhiteListService;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowOauth2TokenService;
import com.homethy.zillow.util.ThreadUtil;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DisconnectConsumerService {
  @Autowired
  private UserService userService;
  @Autowired
  private ZillowOauth2TokenService zillowOauth2TokenService;
  @Autowired
  private WhiteListService whiteListService;
  @Autowired
  private ZillowLeadService zillowLeadService;

  public static String zillowOauth2Key = "Zilliow";

  public void consume(List<KafkaRecord> records) {
    records.stream()
        .map(record -> ThreadUtil.submit(() -> doConsumer(record)))
        .toList()
        .forEach(future -> {
          ThreadUtil.getResult(future, false);
        });
  }

  private boolean doConsumer(KafkaRecord record) {
    if (KafkaTopic.TOPIC_FUNCTION_CHANGE_USER.equals(record.getTopic())) {
      whiteListRemove(record);
    } else if (KafkaTopic.TOPIC_USER_DISPLAY_CHANGE.equals(record.getTopic())) {
      inactiveUser(record);
    }
    return true;
  }

  private void inactiveUser(KafkaRecord record) {
    try {
      String value = record.getValue();
      Long operatorId = record.getKey();
      log.info("kafka key: {}, value: {}", operatorId, value);

      UserSeatStatusKafka kafka = com.homethy.util.jackson.JacksonUtils.fromJson(record.getValue(),
          UserSeatStatusKafka.class);
      if (kafka == null) {
        return;
      }
      if (kafka.getSeatStatus() == 0) {
        inactiveUser(kafka.getAgentId());
      } else {
        activeUser(kafka.getAgentId());
      }
    } catch (Exception e) {
      log.warn("inactiveUser fail, exception: ", e);
    }
  }

  void inactiveUser(long userId) {
    log.info("userId:{} is change to inactive,disconnect zillow", userId);
    zillowOauth2TokenService.disconnect(userId);
  }

  void activeUser(long userId) {
    log.info("userId:{} is change to active", userId);
  }

  private void whiteListRemove(KafkaRecord record) {
    try {
      String value = record.getValue();
      log.info("topic:{},offset={},partition={},key:{},value:{}", record.getTopic(), record.getOffset()
          , record.getPartition(), record.getKey(), record.getValue());
      FunctionChangeKafka kafka = JacksonUtils.fromJson(value, FunctionChangeKafka.class);
      if (!kafka.getFunctions().contains(zillowOauth2Key) || kafka.getType() != FunctionChangeKafka.Type.REMOVE) {
        return;
      }
      List<User> userList = userService.getAgentUserListByTeamId(kafka.getChangeId());
      List<User> bindingUsers =
          userList.stream().filter(e -> zillowOauth2TokenService.getToken(e.getBaseId()) != null).collect(Collectors.toList());
      for (User user : bindingUsers) {
        disconnectZillow(user);
      }
    } catch (Exception e) {
      log.warn("delete dialer error", e);
    }
  }

  private void disconnectZillow(User user) {
    log.info("disconnect zillow, userId: {}", user.getId());
    List<Long> userIds = userService.getUserIds(user.getBaseId());
    if (CollectionUtils.isNotEmpty(userIds) && userIds.size() > 1) {
      Boolean inWhiteList =
          userIds.stream().map(userId -> whiteListService.isZillowOauth2WhiteList(userId)).filter(e -> e).findAny().orElse(false);
      if (inWhiteList) {
        return;
      }
    }
    ZillowOauth2Token token = zillowOauth2TokenService.getToken(user.getBaseId());
    if (token == null) {
      return;
    }
    zillowOauth2TokenService.disconnect(user.getId());
    zillowLeadService.removeZillowLead(token, true);
  }
}

package com.homethy.microservice.service;

import com.google.common.collect.Lists;
import com.homethy.fub.service.FubLeadSyncService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.po.KafkaMergePair;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.LeadPushService;
import com.homethy.zillow.service.ZillowTransactionService;
import com.homethy.zillow.util.ThreadPoolUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LeadMergeConsumerService {

  @Resource
  LeadPushService leadPushService;
  @Resource
  FubLeadSyncService fubLeadSyncService;

  @Autowired
  private ZillowTransactionService zillowTransactionService;

  public void consume(List<KafkaRecord> records) {
    try {
      List<Long> mergedLeadIds = Lists.newArrayListWithCapacity(records.size());
      List<Long> leadIds = Lists.newArrayListWithCapacity(records.size());
      for (KafkaRecord r : records) {
        log.info("{}|{}|{}-{}", r.getTopic(), r.getKey(), r.getPartition(), r.getOffset());
        KafkaMergePair kafkaMergePair = Optional.ofNullable(r.getValue()).map(this::getLeadId)
            .orElse(null);
        if (kafkaMergePair == null) {
          continue;
        }
        leadIds.add(kafkaMergePair.getPrimaryId());
        mergedLeadIds.add(kafkaMergePair.getSecondaryId());
      }

      // delete transaction
      zillowTransactionService.deleteTrForLeadMerge(mergedLeadIds);
      // push lead detail
      Future<Boolean> mergeTask = ThreadPoolUtil.submitTask(
          () -> leadPushService.mergeLead(leadIds));
      Future<Boolean> fubMergeTask = ThreadPoolUtil.submitTask(
          () -> fubLeadSyncService.mergeLeads(leadIds));
      ThreadPoolUtil.getResult(mergeTask);
      ThreadPoolUtil.getResult(fubMergeTask);
    } catch (Exception e) {
      log.warn("lead merge, exception:", e);
    }
  }

  KafkaMergePair getLeadId(String value) {
    try {
      return JacksonUtils.fromJson(value, KafkaMergePair.class);
    } catch (Exception e) {
      log.warn("getLeadFailed,value:{}", value);
    }
    return null;
  }
}

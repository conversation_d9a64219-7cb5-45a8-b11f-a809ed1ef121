package com.homethy.microservice.service;

import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.dto.SyncTransactionZillowDTO;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.ZillowTransactionService;
import com.homethy.zillow.util.ThreadUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TransactionConsumerService {

  @Autowired
  private ZillowTransactionService zillowTransactionService;

  public void consume(List<KafkaRecord> records) {
    records.stream().map(record -> ThreadUtil.submit(() -> doConsumer(record)))
        .forEach(future -> ThreadUtil.getResult(future, false));
  }

  private boolean doConsumer(KafkaRecord r) {
    try {
      log.info("{}|{}|{}-{}", r.getTopic(), r.get<PERSON>ey(), r.getPartition(), r.getOffset());
      SyncTransactionZillowDTO dto = JacksonUtils.fromJson(r.getValue(),
          SyncTransactionZillowDTO.class);
      zillowTransactionService.saveOrUpdate(dto.getTransactonId(), dto.getOptUserId());
      return true;
    } catch (Exception e) {
      log.warn("failed push trId: {}", r.getKey(), e);
    }
    return false;
  }
}

package com.homethy.microservice.service;


import com.homethy.fub.dao.FubTrRefDao;
import com.homethy.fub.service.FubLeadService;
import com.homethy.fub.service.FubPushTransactionService;
import com.homethy.microservice.BO.LeadTransactionDetailBO;
import com.homethy.microservice.client.transaction.LeadTransactionDetailClient;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubTrRef;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class TransactionToFubConsumerService {

  @Autowired
  FubPushTransactionService fubPushTransactionService;
  @Autowired
  LeadTransactionDetailClient leadTransactionDetailClient;
  @Autowired
  FubTrRefDao fubTrRefDao;
  @Autowired
  FubLeadService fubLeadService;
  @Autowired
  private ZillowLeadService zillowLeadService;

  public void consume(List<KafkaRecord> records) {
    for (KafkaRecord record : records) {
      try {
        handle(record);
      } catch (Exception e) {
        log.warn("Error processing record: {}", record, e);
      }
    }
  }

  private void handle(KafkaRecord record) {
    Optional<LeadTransactionDetailBO> transaction = getTransaction(record.getKey());
    if (transaction.isEmpty()) {
      log.info("lead transaction is null, transactionId: {}", record.getKey());
      return;
    }
    LeadTransactionDetailBO leadTransaction = transaction.get();

    FubLead fubLead = fubLeadService.getLatestFubLead(leadTransaction.getLeadId());
    if (Objects.isNull(fubLead)) {
      log.info("fub lead is null, leadId: {}", leadTransaction.getLeadId());
      return;
    }

    //no need to check the transaction is created by lofty or zillow. all need to judge if zillow lead
    long leadId = leadTransaction.getLeadId();
    if(zillowLeadService.checkZillowLead(leadId)){
      log.info("lead Id: {} is a Zillow lead, skipping FUB push to avoid conflicts", leadId);
      return;
    }

    if (!needToSync(leadTransaction.getType())) {
      log.info("transaction type is not 1 or 2, transactionId: {}, type: {}",
          record.getKey(), leadTransaction.getType());
      return;
    }

    FubTrRef fubTrRef = fubTrRefDao.getByTrId(leadTransaction.getId(), leadTransaction.getTeamId());
    if (Objects.isNull(fubTrRef)) {
      fubPushTransactionService.handCreateTransaction(leadTransaction, fubLead);
    } else if (!leadTransaction.isDeleteFlag()) {
      fubPushTransactionService.handUpdateTransaction(fubTrRef, leadTransaction, fubLead);
    }
  }

  private boolean needToSync(int transactionType) {
    return transactionType == 1 || transactionType == 2;
  }

  private Optional<LeadTransactionDetailBO> getTransaction(long transactionId) {
    try {
      LeadTransactionDetailBO transactionDetailBO =
          this.leadTransactionDetailClient.getLeadCurrentDetailByPKID(transactionId);
      if (Objects.nonNull(transactionDetailBO)) {
        return Optional.of(transactionDetailBO);
      }
    } catch (Exception e) {
      log.warn("failed get transaction info, transactionId: {}", transactionId, e);
    }
    return Optional.empty();
  }

}

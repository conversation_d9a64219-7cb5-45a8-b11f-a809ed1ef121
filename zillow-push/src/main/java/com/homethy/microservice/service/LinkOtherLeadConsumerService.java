package com.homethy.microservice.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.InitMessage;
import com.homethy.zillow.model.constant.KafkaTopic;
import com.homethy.zillow.model.constant.LinkOtherKafkaMsg;
import com.homethy.zillow.model.constant.ZillowInitSetting;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.constant.ZillowStateInfo;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.KafkaService;
import com.homethy.zillow.service.TimelineService;
import com.homethy.zillow.service.ZillowInitService;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowOauth2TokenService;
import com.homethy.zillow.util.ThreadUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LinkOtherLeadConsumerService {

  @Autowired
  private ZillowLeadService zillowLeadService;
  @Autowired
  private ZillowOauth2TokenService zillowOauth2TokenService;
  @Autowired
  private UserManager userManager;
  @Autowired
  private ZillowInitService zillowInitService;
  @Autowired
  private KafkaService kafkaService;
  @Autowired
  private TimelineService timelineService;

  public void consume(List<KafkaRecord> records) {
    records.stream()
        .map(record -> ThreadUtil.submit(() -> doConsumer(record)))
        .toList()
        .forEach(future -> {
          ThreadUtil.getResult(future, false);
        });
  }

  private boolean doConsumer(KafkaRecord record) {
    log.info("key: {}, value: {}", record.getKey(), record.getValue());
    try {
      LinkOtherKafkaMsg linkOtherKafkaMsg = JacksonUtils.fromJson(record.getValue(), LinkOtherKafkaMsg.class);
      doChange(linkOtherKafkaMsg);
    } catch (IOException e) {
      log.warn("value is not a json, value: {}, exception: ", record.getValue(), e);
      return false;
    }
    return true;
  }

  private void doChange(LinkOtherKafkaMsg linkOtherKafkaMsg) {
    ZillowStateInfo zillowStateInfo = linkOtherKafkaMsg.getInfo();
    ZillowInitSetting initSetting = zillowInitService.getInitSetting(zillowStateInfo.toOwnership());

    ZillowOauth2Token token = zillowOauth2TokenService.getToken(zillowStateInfo.toOwnership());
    if (token == null) {
      log.info("token is null");
      return;
    }
    User user = userManager.getUserById(zillowStateInfo.getOperUserId());
    if (user == null) {
      log.info("operUser is null");
      return;
    }

    if (linkOtherKafkaMsg.getDisconnectToken() != null) {
      timelineService.sendZillowDisconnectTimeline(linkOtherKafkaMsg.getDisconnectToken().toOwnership(),
          zillowStateInfo.toOwnership(), zillowStateInfo.getOperUserId(), false);
    }

    if (initSetting.isLinkOtherLead()) {
      log.info("link other zillow lead");
      try {
        zillowLeadService.changeOtherZillowLead(token, user.getTeamId());
      } catch (Exception e) {
        log.warn("link other zillowLead fail, exception: ", e);
      }
    } else {
      timelineService.sendOldZillowLeadConnectTimeline(token);
    }

    if (initSetting.isInitSwitch()) {
      log.info("import zillow lead switch: true");
      kafkaService.sendMessage(KafkaTopic.ZILLOW_INIT_ZILLOW_LEAD, zillowStateInfo.getOwnershipId(),
          JacksonUtils.toJson(InitMessage.builder().zillowStateInfo(zillowStateInfo).build()), true);
    }
  }
}

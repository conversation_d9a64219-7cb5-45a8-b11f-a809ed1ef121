package com.homethy.microservice.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.homethy.fub.dao.FubTrRefDao;
import com.homethy.fub.service.FubFamilyMemberService;
import com.homethy.fub.service.FubImportTimelineService;
import com.homethy.fub.service.FubLeadService;
import com.homethy.fub.service.FubTagService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.fub.service.FubTransactionService;
import com.homethy.fub.service.impl.FubFamilyMemberCreateImpl;
import com.homethy.fub.service.impl.FubFamilyMemberUpdateImpl;
import com.homethy.microservice.BO.LeadTransactionDetailBO;
import com.homethy.microservice.BO.TransactionPipelineBo;
import com.homethy.microservice.client.Tag.LeadTagService;
import com.homethy.microservice.client.bo.p.PermissionEnum;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.p.UserLeadTag;
import com.homethy.microservice.client.bo.persistence.UserTag;
import com.homethy.microservice.client.lead.apiV2.LeadService;
import com.homethy.microservice.client.model.EditLeadModel;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadFamilyMemberVoBo;
import com.homethy.microservice.client.model.RealEditSource;
import com.homethy.microservice.client.model.UserEmailBo;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.microservice.client.transaction.LeadTransactionDetailClient;
import com.homethy.microservice.client.transaction.TransactionPipelineClient;
import com.homethy.microservice.client.transaction.TransactionStatusClient;
import com.homethy.microservice.client.user.UserService;
import com.homethy.microservice.permission.client.service.offline.PermissionOfflineClientService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.client.LeadManager;
import com.homethy.zillow.configuration.ZillowConfig;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.fub.FubDeal;
import com.homethy.zillow.model.constant.fub.FubEmail;
import com.homethy.zillow.model.constant.fub.FubFamilyMember;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.constant.fub.FubTrRef;
import com.homethy.zillow.model.constant.fub.FubUserPhone;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.service.impl.FubAppointmentWebhookServiceFactory;
import com.homethy.zillow.service.impl.FubNotesWebhookServiceFactory;
import com.homethy.zillow.service.impl.FubTaskWebhookServiceFactory;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FubToLoftyConsumerService {
  @Autowired
  LeadService leadService;
  @Autowired
  LeadOfflineService offlineService;
  @Autowired
  UserService userService;
  @Autowired
  LeadTagService leadTagService;
  @Autowired
  PermissionOfflineClientService permissionOfflineClientService;
  @Autowired
  FubClient fubClient;
  @Autowired
  FubTokenService tokenService;
  @Autowired
  FubTrRefDao fubTrRefDao;
  @Autowired
  LeadTransactionDetailClient leadTransactionDetailClient;
  @Autowired
  private FubAppointmentWebhookServiceFactory fubAppointmentWebhookServiceFactory;
  @Autowired
  TransactionStatusClient transactionStatusClient;
  @Autowired
  TransactionPipelineClient transactionPipelineClient;
  @Autowired
  FubTaskWebhookServiceFactory fubTaskWebhookServiceFactory;
  @Autowired
  FubTagService tagService;
  @Autowired
  FubTransactionService transactionService;
  @Autowired
  FubFamilyMemberService familyMemberService;
  @Autowired
  FubFamilyMemberCreateImpl familyMemberCreateService;
  @Autowired
  FubFamilyMemberUpdateImpl familyMemberUpdateService;
  @Autowired
  FubNotesWebhookServiceFactory fubNotesWebhookServiceFactory;
  @Autowired
  FubImportTimelineService fubImportTimelineService;
  @Autowired
  FubLeadService fubLeadService;
  @Autowired
  LeadManager leadManager;

  public void consume(List<KafkaRecord> records) {
    for (KafkaRecord record : records) {
      log.info("fub to lofty comsume,{}|{}|{}-{}", record.getTopic(), record.getKey(),
          record.getPartition(),
          record.getOffset());

      if (JacksonUtils.isJson(record.getValue())) {
        try {
          FubImportMsg fubRecord = JacksonUtils.fromJson(record.getValue(), FubImportMsg.class);
          if (fubRecord.getType().equals(FubImportTypeEnum.PEOPLE_CREATED.getValue()) || fubRecord.getType().equals(FubImportTypeEnum.PEOPLE_UPDATED.getValue())) {
            fubLeadService.handleLead(fubRecord);
          }
          if (fubRecord.getType().equals(FubImportTypeEnum.PERPLE_TAGS_CREATED.getValue())) {
            tagService.handleTag(fubRecord);
          }
          if (fubRecord.getType().equals(FubImportTypeEnum.PEOPLE_RELATIONSHIP_CREATED.getValue())) {
            familyMemberCreateService.handle(fubRecord,null);
          }
          if ( fubRecord.getType().equals(FubImportTypeEnum.PEOPLE_RELATIONSHIP_UPDATED.getValue())) {
            familyMemberUpdateService.handle(fubRecord,null);
          }
          if (fubRecord.getType().equals(FubImportTypeEnum.DEALS_CREATED.getValue()) || fubRecord.getType().equals(FubImportTypeEnum.DEALS_UPDATED.getValue())) {
            transactionService.handelTransaction(fubRecord);
          }
          if (FubImportTypeEnum.isAppointmentEvent(fubRecord.getType())) {
            fubAppointmentWebhookServiceFactory.handWebhookEvent(fubRecord);
          }
          if (FubImportTypeEnum.isTaskEvent(fubRecord.getType())) {
            fubTaskWebhookServiceFactory.handWebhookEvent(fubRecord);
          }
          if (FubImportTypeEnum.isNotesEvent(fubRecord.getType())) {
            fubNotesWebhookServiceFactory.handWebhookEvent(fubRecord);
          }
          if (FubImportTypeEnum.isCallEmailText(fubRecord.getType())) {
            fubImportTimelineService.importFubTimeline(fubRecord);
          }
        } catch (IOException e) {
          throw new RuntimeException(e);
        }
      }
    }
  }

  private void handleTransaction(FubImportMsg fubRecord) {
    //deals logic
    String data = fubRecord.getData();
    if (StringUtils.isBlank(data) || !JacksonUtils.isJson(data) || null == fubRecord.getStateInfo()) {
      return;
    }
    String callToken = tokenService.getCallToken(fubRecord.getStateInfo().toOwnership());
    try {
      Map<String, Object> dataMap = JacksonUtils.json2Map(data);
      if (!dataMap.containsKey("resourceIds")) {
        return;
      }
      List<Integer> dealIds = (List<Integer>) dataMap.get("resourceIds");
      handleDeals(dealIds, callToken, fubRecord.getStateInfo(), fubRecord.getType());


    } catch (IOException e) {
      log.warn("handle peopleTagsCreated logic for fub failed,e:{}", e);
    }
  }

  private void handleDeals(List<Integer> dealIds, String callToken, FubStateInfo stateInfo,
      String importType) {
    //handle single deal
    if (stateInfo == null || stateInfo.getOwnershipScope() == null) {
      return;
    }
    for (Integer dealId : dealIds) {
      FubDeal deal = fubClient.getDeal(callToken, dealId);
      long transactionId = fubTrRefDao.getByIdAndOwnership(deal.getId(),
          stateInfo.getFubTeamId());
      List<LeadTransactionDetailBO> transactionList =
          leadTransactionDetailClient.listByTransactionIds(Lists.newArrayList(transactionId));
      if (transactionList.size() > 1) {
        return;
      }
      LeadTransactionDetailBO insertBo = new LeadTransactionDetailBO();
      if (FubImportTypeEnum.DEALS_CREATED.getValue().equals(importType)) {
        if (transactionList.size() > 0) {
          return;
        }
        formatvalues(stateInfo, deal, insertBo);
        LeadTransactionDetailBO insertResult = leadTransactionDetailClient.insert(insertBo,
            stateInfo.getBindingAgentId());
        FubTrRef trRef = FubTrRef.builder()
            .trId(insertResult.getId())
            .dealId(deal.getId())
            .leadId(insertResult.getLeadId())
            .teamId(stateInfo.getTeamId())
            .ownershipScope(stateInfo.getOwnershipScope().name())
            .ownershipId(stateInfo.getOwnershipId())
            .fubTeamId(stateInfo.getFubTeamId())
            .fubStatus(2)
            .build();
        fubTrRefDao.insert(trRef);
      }
      if (FubImportTypeEnum.DEALS_UPDATED.getValue().equals(importType)) {
        formatvalues(stateInfo, deal, insertBo);
        leadTransactionDetailClient.update(insertBo, stateInfo.getBindingAgentId());
      }

    }

  }

  private void formatvalues(FubStateInfo stateInfo, FubDeal deal,
      LeadTransactionDetailBO insertBo) {
    insertBo.setTransactionName(deal.getName());
    setTypeAndStatus(stateInfo, deal, insertBo);

    insertBo.setHomePrice(deal.getPrice());
    insertBo.setGci(deal.getCommissionValue());
    insertBo.setTeamRevenue(deal.getTeamCommission());
    insertBo.setAgentRevenue(deal.getAgentCommission());
    insertBo.setCreateTime(deal.getCreatedAt());
    insertBo.setCloseTime(deal.getProjectedCloseDate());
    //todo handle team
  }

  private void setTypeAndStatus(FubStateInfo stateInfo, FubDeal deal,
      LeadTransactionDetailBO insertBo) {
    List<TransactionPipelineBo> purchase;
    if (deal.getPipelineName().equals("Buyers")) {
      insertBo.setType(1);
      purchase = transactionPipelineClient.getPipelinesWithType(stateInfo.getTeamId(),
          "Purchase");
    }
    if (deal.getPipelineName().equals("Sellers")) {
      insertBo.setType(2);
      purchase = transactionPipelineClient.getPipelinesWithType(stateInfo.getTeamId(),
          "Listing");
    } else {
      insertBo.setType(0);
      purchase = transactionPipelineClient.getPipelinesWithType(stateInfo.getTeamId(),
          "Other");
    }
    long closeId = -1;
    long trashId = -1;
    long minId = Integer.MAX_VALUE;
    for (TransactionPipelineBo transactionPipelineBo : purchase) {
      if (transactionPipelineBo.getName().equalsIgnoreCase("closed")) {
        closeId = transactionPipelineBo.getId();
      }
      if (transactionPipelineBo.getName().equalsIgnoreCase("canceled")) {
        trashId = transactionPipelineBo.getId();
      }
      if (transactionPipelineBo.getId() < minId) {
        minId = transactionPipelineBo.getId();
      }
    }

  }


  private void handleFamilyMember(FubImportMsg fubRecord) {
    String data = fubRecord.getData();
    if (StringUtils.isBlank(data) || !JacksonUtils.isJson(data) || null == fubRecord.getStateInfo()) {
      return;
    }
    String callToken = tokenService.getCallToken(fubRecord.getStateInfo().toOwnership());

    try {
      Map<String, Object> dataMap = JacksonUtils.json2Map(data);
      if (!dataMap.containsKey("resourceIds")) {
        return;
      }
      List<Long> peopleIds = (List<Long>) dataMap.get("resourceIds");
      handleFamilyMemberWithToken(peopleIds, callToken);


    } catch (IOException e) {
      log.warn("handle peopleTagsCreated logic for fub failed,e:{}", e);
    }
  }

  private void handleFamilyMemberWithToken(List<Long> peopleIds, String callToken) {
    for (long peopleId : peopleIds) {
      FubFamilyMember newMember = fubClient.getPeopleRelationship(callToken,
          peopleId);
      //mock leadId
      int leadId = 1;
      LeadBo leadBo = leadService.getLeadById(leadId);
      List<LeadFamilyMemberVoBo> oldMembers =
          offlineService.getFamilyMembers(leadBo.getLeadUserId(),
              false);
      List<FubFamilyMember> createList = new ArrayList<>();
      handleOldAndNewMember(oldMembers, newMember, createList);
      addNewFamilyMember(createList, oldMembers);
      String familyJson = JacksonUtils.toJson(oldMembers);
      EditLeadModel editLeadModel = new EditLeadModel();
      editLeadModel.setFamilyMemberVos(familyJson);
      leadManager.toValidate(newMember.getEmails());
      //todo switch peopleid
      offlineService.editLead(leadId, editLeadModel, RealEditSource.FUB_IMPORT);
    }
  }

  private static void addNewFamilyMember(List<FubFamilyMember> createList,
      List<LeadFamilyMemberVoBo> oldMembers) {
    for (FubFamilyMember fubFamilyMember : createList) {
      LeadFamilyMemberVoBo familyMember = new LeadFamilyMemberVoBo();
      familyMember.setFirstName(fubFamilyMember.getFirstName());
      familyMember.setLastName(fubFamilyMember.getLastName());
      familyMember.setRelation(fubFamilyMember.getType());
      List<FubEmail> emails = fubFamilyMember.getEmails();
      List<UserEmailBo> addEmailList = new ArrayList<>();
      for (FubEmail newEmail : emails) {
        UserEmailBo addBo = new UserEmailBo();
        addBo.setEmail(newEmail.getValue());
        addBo.setIsPrimary(newEmail.getIsPrimary() != 0);
        addEmailList.add(addBo);
      }
      familyMember.setUserEmailList(addEmailList);
      List<FubUserPhone> phones = fubFamilyMember.getPhones();
      List<UserPhoneBo> addPhoneList = new ArrayList<>();
      for (FubUserPhone newphone : phones) {
        UserPhoneBo addPhoneBo = new UserPhoneBo();
        addPhoneBo.setPhone(newphone.getValue());
        addPhoneBo.setIsPrimary(newphone.getIsPrimary() != 0);
        addPhoneList.add(addPhoneBo);
      }
      familyMember.setUserPhoneList(addPhoneList);
      oldMembers.add(familyMember);
    }
  }

  private static void handleOldAndNewMember(List<LeadFamilyMemberVoBo> oldMembers,
      FubFamilyMember newMember, List<FubFamilyMember> createList) {
    for (LeadFamilyMemberVoBo oldMember : oldMembers) {
        if (oldMember.getFullName().equals(newMember.getName())) {
          List<FubEmail> emails = newMember.getEmails();
          List<UserEmailBo> userEmailList = oldMember.getUserEmailList();
          handelFamilyEmail(emails, userEmailList);
          oldMember.setUserEmailList(userEmailList);
          List<FubUserPhone> newphones = newMember.getPhones();
          List<UserPhoneBo> oldPhones = oldMember.getUserPhoneList();
          handleFamilyPhone(newphones, oldPhones);
          oldMember.setUserPhoneList(oldPhones);
        } else {
          createList.add(newMember);
        }
      //todo check maximum member num and extract constant
      createList.subList(0, 5 - oldMembers.size());
    }
  }

  private static void handleFamilyPhone(List<FubUserPhone> newphones, List<UserPhoneBo> oldPhones) {
    for (FubUserPhone newphone : newphones) {
      for (UserPhoneBo oldPhone : oldPhones) {
        if (!newphone.getValue().equals(oldPhone.getPhone())) {
          UserPhoneBo addPhoneBo = new UserPhoneBo();
          addPhoneBo.setPhone(newphone.getValue());
          addPhoneBo.setIsPrimary(newphone.getIsPrimary() != 0);
          oldPhones.add(addPhoneBo);
        }
      }
    }
  }

  private static void handelFamilyEmail(List<FubEmail> emails, List<UserEmailBo> userEmailList) {
    for (FubEmail email : emails) {
      for (UserEmailBo userEmailBo : userEmailList) {
        //ignore when equals
        if (!email.getValue().equals(userEmailBo.getEmail())) {
          UserEmailBo addBo = new UserEmailBo();
          addBo.setEmail(email.getValue());
          addBo.setIsPrimary(email.getIsPrimary() != 0);
          userEmailList.add(addBo);
        }
      }
    }
  }

  private void handleTags(FubImportMsg fubRecord) {
    String data = fubRecord.getData();
    if (StringUtils.isBlank(data) || !JacksonUtils.isJson(data) || null == fubRecord.getStateInfo()) {
      return;
    }
    try {
      Map<String, Object> dataMap = JacksonUtils.json2Map(data);
      if (!dataMap.containsKey("resourceIds")) {
        return;
      }
      List<String> peopleIds = (List<String>) dataMap.get("resourceIds");
      if (!dataMap.containsKey("data") && !JacksonUtils.isJson(dataMap.get("data").toString())) {
        return;
      }
      Map<String, Object> tagMap = JacksonUtils.json2Map(dataMap.get("data").toString());
      if (!tagMap.containsKey("tag") && !JacksonUtils.isJson(tagMap.get("tag").toString())) {
        return;
      }
      List<String> tags = (List<String>) tagMap.get("tags");
      long agentId = fubRecord.getStateInfo().getBindingAgentId();
      User operUser = userService.getUserById(agentId);
      if (operUser == null) {
        return;
      }
      for (String peopleId : peopleIds) {
        handleTagsForLeadFub(peopleId, tags, operUser);
      }

    } catch (IOException e) {
      log.warn("handle peopleTagsCreated logic for fub failed,e:{}", e);
    }

  }

  private void handleTagsForLeadFub(String peopleId, List<String> tags, User user) {
    log.info("handle tag logic for fub,leadId:{}", peopleId);
    LeadBo originalLead = leadService.getLeadById(Long.parseLong(peopleId));
    List<Integer> filteredTags = tags.stream()
        .map(Integer::parseInt)
        .filter(num -> num == 1 || num == 2)
        .collect(Collectors.toList());
    //handle leadType
    List<Integer> mergedList = Stream.concat(filteredTags.stream(),
            originalLead.getLeadTypes().stream())
        .distinct()
        .collect(Collectors.toList());
    EditLeadModel editLeadModel = new EditLeadModel();
    editLeadModel.setLeadTypes(mergedList);

    //handle lead tags
    List<Long> updateTags = getTags(tags, user, Long.parseLong(peopleId),
        permissionOfflineClientService.hasAny(user.getId(),
            List.of(PermissionEnum.MANAGE_TAG.name(),
                PermissionEnum.MANAGE_TAG_DEPARTMENT.name())));
    editLeadModel.setTags(updateTags);
    //todo transfer peopleid to leadId and replace manual web
    offlineService.editLead(Long.parseLong(peopleId), editLeadModel, RealEditSource.FUB_IMPORT);
  }

  List<Long> getTags(List<String> tagNameList, User agent, long leadId, boolean agentPermission) {
    if (CollectionUtils.isEmpty(tagNameList)) {
      return null;
    }
    List<UserLeadTag> oldTags = leadTagService.getLeadTagListByLead(leadId);
    Set<Long> oldIds = oldTags == null ? Collections.emptySet() :
        oldTags.stream().map(t -> t.getTagId()).collect(Collectors.toSet());
    List<UserTag> addTags = getNewTags(tagNameList, agent, agentPermission)
        .stream().filter(t -> !oldIds.contains(t.getId())).collect(Collectors.toList());
    if (addTags.isEmpty()) {
      log.info("empty addTags");
      return null;
    }
    List<Long> tagIds = addTags.stream().map(UserTag::getId).collect(Collectors.toList());
    tagIds.addAll(oldIds);
    return tagIds;
  }

  List<UserTag> getNewTags(List<String> tagNameList, User user, boolean userPermission) {
    List<UserTag> newTags = new ArrayList<>();
    if (!tagNameList.isEmpty()) {
      //add
      List<UserTag> newUserTags = addUserTags(tagNameList, user, userPermission);
      if (CollectionUtils.isEmpty(newUserTags)) {
        return newTags;
      }
      newTags.addAll(newUserTags);
      log.info("newTags: {}", newTags);
    }
    return newTags;
  }

  private List<UserTag> addUserTags(List<String> tagNameList, User user, boolean userPermission) {
    if (!tagNameList.isEmpty()) {
      //limit
      int limit = Math.min(tagNameList.size(),
          Integer.parseInt(ZillowConfig.getProperties("fub.import.max.size", "50")));
      tagNameList = tagNameList.subList(0, limit);
      if (!userPermission) {
        Set<String> finalTagNameList = tagNameList.stream()
            .map(StringUtils::lowerCase)
            .collect(Collectors.toSet());
        return leadTagService.getUserVisibleTags(user.getId(), false, -1)
            .stream()
            .filter(userTag -> finalTagNameList.contains(userTag.getTagName().toLowerCase()))
            .collect(Collectors.toList());
      }
      log.info("tagNameList : {}", tagNameList);
      return leadTagService.batchAddUserTags(user.getId(), tagNameList.stream()
          .filter(s -> StringUtils.isNotEmpty(StringUtils.trim(s)))
          .map(s -> {
            UserTag userTag = new UserTag();
            userTag.setTagName(StringUtils.trim(s));
            userTag.setUserId(user.getId());
            userTag.setTeamId(user.getTeamId());
            return userTag;
          }).collect(Collectors.toList()));
    }
    return new ArrayList<>();
  }


}

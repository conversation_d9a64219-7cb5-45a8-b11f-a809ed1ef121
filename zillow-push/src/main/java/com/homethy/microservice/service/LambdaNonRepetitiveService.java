package com.homethy.microservice.service;

import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.configuration.RedisLockService;
import com.homethy.zillow.dao.KafkaConsumerRecordDao;
import com.homethy.zillow.model.po.KafkaRecord;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LambdaNonRepetitiveService {

  @Autowired
  KafkaConsumerRecordDao dao;
  @Resource
  RedisLockService redisLockService;

  private static final String area = "chime-zillow";

  public List<KafkaRecord> filter(List<KafkaRecord> allRecords, boolean msk) {
    try {
      return doFilter(allRecords, msk);
    } catch (Exception e) {
      log.error("failed", e);
      return allRecords;
    }
  }

  private List<KafkaRecord> doFilter(List<KafkaRecord> allRecords, boolean msk) {
    Map<Integer, List<KafkaRecord>> map =
        allRecords.stream().collect(Collectors.groupingBy(KafkaRecord::getPartition));
    List<KafkaRecord> result = new ArrayList<>();
    map.forEach((partition, records) -> {
      result.addAll(doFilter(partition, records, msk));
    });
    return result;
  }

  private List<KafkaRecord> doFilter(int partition, List<KafkaRecord> records, boolean msk) {
    String prefix = msk ? "_msk" : "";
    dao.createTable(partition, prefix);
    if (System.currentTimeMillis() % 4 == 0 && redisLockService.lock(area,
        "clear_event_record" + prefix + partition, 60 * 3)) {
      Long from = dao.toDeleteId(partition, prefix);
      if (from != null && from > 0) {
        int times = 20;
        int step = 20000 / times;
        for (int i = 1; i < times + 1; i++) {
          dao.delete(partition, prefix, from - (times - i) * step);
        }
      }
      redisLockService.unlock(area, "clear_event_record" + prefix + partition);
    }
    List<KafkaRecord> list = new ArrayList<>();
    records.stream().collect(Collectors.groupingBy(KafkaRecord::getTopic)).forEach((topic, toInsert) -> {
      List<KafkaRecord> lockRecords = lock(records);
      toInsert = new ArrayList<>(lockRecords);
      List<Long> dbExists = new ArrayList<>();
      if (CollectionUtils.isNotEmpty(lockRecords)) {
        dbExists = dao.listByTopicPtt(topic, partition, prefix, offsets(lockRecords));
      }
      if (!dbExists.isEmpty()) {
        ArrayList<Long> exists = new ArrayList<>(dbExists);
        toInsert = toInsert.stream().filter(r -> !exists.contains(r.getOffset())).toList();
        log.info("repeat kfk {}-{} {}", topic, partition, JacksonUtils.toJson(exists));
      }
      if (!toInsert.isEmpty()) {
        dao.batchInsert(topic, partition, prefix, offsets(toInsert));
        list.addAll(toInsert);
      }
      unLock(lockRecords);
    });
    return list;
  }

  private List<KafkaRecord> lock(List<KafkaRecord> records) {
    return records.stream()
        .filter(record -> redisLockService.lock(area, record.toLockKey(), 60))
        .collect(Collectors.toList());
  }

  private void unLock(List<KafkaRecord> records) {
    records.forEach(record -> redisLockService.unlock(area, record.toLockKey()));
  }

  private static List<Long> offsets(List<KafkaRecord> toInsert) {
    return toInsert.stream().map(KafkaRecord::getOffset).toList();
  }
}

package com.homethy.microservice.service;

import com.homethy.leadsync.business.FubLeadSyncHandler;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.zillow.service.TimeLineHandler;
import com.homethy.zillow.util.JsonUtil;
import com.homethy.zillow.util.ThreadUtil;

import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LambdaConsumerService {

  @Resource
  TimeLineHandler timeLineHandler;
  @Resource
  FubLeadSyncHandler fubLeadSyncHandler;

  public void consume(List<KafkaRecord> records) {
    List<KafkaTimeline> timelineList = new ArrayList<>();
    List<KafkaTimeline> leadSyncList = new ArrayList<>();
    try {
      push(records, timelineList, leadSyncList);
    } catch (Exception e) {
      log.warn("push fail, exception:", e);
    }
  }

  private void push(List<KafkaRecord> records, List<KafkaTimeline> timelineList, List<KafkaTimeline> leadSyncList) {
    for (KafkaRecord record : records) {
      log.info("consume lead. leadId: {},topic:{}|{}|{}", record.getKey(), record.getTopic(),
          record.getPartition(), record.getOffset());
      KafkaTimeline kafkaTimeline = getKafkaTimeline(record.getValue());
      if (kafkaTimeline == null) {
        log.info("kafka timeline is null");
        continue;
      }

      if (ZillowConstant.LEAD_COMMUNICATION_LOG_TOPIC.equals(record.getTopic())) {
        leadSyncList.add(kafkaTimeline);
      } else {
        timelineList.add(kafkaTimeline);
      }
    }
    Future<Boolean> syncFuture = ThreadUtil.submit(() -> pushLeadSync(leadSyncList));
    Future<Boolean> future = ThreadUtil.submit(() -> pushCommunication(timelineList));
    Boolean syncResult = ThreadUtil.getResult(syncFuture, Boolean.FALSE);
    Boolean result = ThreadUtil.getResult(future, Boolean.FALSE);
    log.debug("result = {}, syncResult = {}", result, syncResult);
  }

  private boolean pushLeadSync(List<KafkaTimeline> timelineList) {
    try {
      fubLeadSyncHandler.batchHandleTimeLine(timelineList);
      timeLineHandler.batchHandleTimeLine(timelineList);
    } catch (Exception e) {
      log.warn("add colum false, exception: ", e);
      return false;
    }
    return true;
  }

  private boolean pushCommunication(List<KafkaTimeline> timelineList) {
    try {
      fubLeadSyncHandler.batchHandleCommunicationTimeLine(timelineList);
      timeLineHandler.batchHandleCommunicationTimeLine(timelineList);
    } catch (Exception e) {
      log.warn("add colum false, exception: ", e);
      return false;
    }
    return true;
  }

  KafkaTimeline getKafkaTimeline(String value) {
    try {
      return JsonUtil.fromJson(value, KafkaTimeline.class);
    } catch (IOException e) {
      log.info("getKafkaTimeline failed", e);
    }
    return null;
  }
}

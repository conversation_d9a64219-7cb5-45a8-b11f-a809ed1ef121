package com.homethy.microservice.lambda;

import com.amazonaws.services.lambda.runtime.events.ScheduledEvent;
import com.homethy.fub.service.FubLeadSyncService;
import com.homethy.fub.service.impl.FubLeadSyncServiceImpl;
import com.homethy.microservice.service.FubRetryScheduleService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.zillow.util.ThreadPoolUtil;

import java.util.Optional;
import java.util.concurrent.Future;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FubRetrySchedule extends AwsHandler<ScheduledEvent> {

  private static final String area = "Scheduled_fub_retry_";

  @Override
  public Object handleRequest(ScheduledEvent input) throws Exception {
    String eventType = Optional.ofNullable(input.getDetail())
        .map(e -> e.get("eventType"))
        .map(Object::toString)
        .orElse("");
    log.info("ScheduledHandler {}", eventType);
    RedisService redisService = ApplicationContextUtil.getBean(RedisService.class);
    long lock = redisService.setnx(area, eventType, "1");
    if (lock == 0) {
      log.info("ScheduledHandler {} isLock", eventType);
      return "OK";
    }
    redisService.expire(area, eventType, 15 * 60);
    try {
      execute(eventType);
    } catch (Exception e) {
      log.error("ScheduledHandler {} error", eventType, e);
    }
    redisService.del(area, eventType);
    return "OK";
  }

  private void execute(String eventType) {
    if ("fubRetrySchedule".equals(eventType)) {
      FubRetryScheduleService fubRetryScheduleService = ApplicationContextUtil.getBean(FubRetryScheduleService.class);
      Future<Boolean> importFuture =
          ThreadPoolUtil.submitTask(fubRetryScheduleService::retryScheduleImport);
      Future<Boolean> notifyFuture =
          ThreadPoolUtil.submitTask(fubRetryScheduleService::retryScheduleNotify);
      ThreadPoolUtil.getResult(importFuture);
      ThreadPoolUtil.getResult(notifyFuture);
    } else if ("rePushNotesToFub".equals(eventType)) {
      FubLeadSyncService leadSyncService = ApplicationContextUtil.getBean(FubLeadSyncService.class);
      Future<Boolean> task = ThreadPoolUtil.submitTask(leadSyncService::rePushNotesToFub);
      ThreadPoolUtil.getResult(task);
    }
  }
}

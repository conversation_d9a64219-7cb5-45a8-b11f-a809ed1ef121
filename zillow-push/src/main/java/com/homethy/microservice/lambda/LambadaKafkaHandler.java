package com.homethy.microservice.lambda;

import com.homethy.microservice.service.LambdaConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LambadaKafkaHandler extends BaseKafkaHandler {
  @Override
  public void doHandler(List<KafkaRecord> records) {
    LambdaConsumerService lambdaConsumerService = ApplicationContextUtil.getBean(LambdaConsumerService.class);
    lambdaConsumerService.consume(records);
  }
}

package com.homethy.microservice.lambda;

import com.homethy.microservice.service.DisconnectConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DisconnectKafkaHandler extends BaseKafkaHandler {

  @Override
  public void doHandler(List<KafkaRecord> records) {
    DisconnectConsumerService disconnectConsumerService =
        ApplicationContextUtil.getBean(DisconnectConsumerService.class);
    disconnectConsumerService.consume(records);
  }
}

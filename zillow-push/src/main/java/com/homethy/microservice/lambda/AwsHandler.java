package com.homethy.microservice.lambda;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.homethy.microservice.App;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.util.response.ReturnJacksonUtil;
import com.homethy.zillow.util.MdcUtil;
import com.homethy.zillow.util.ThreadPoolUtil;

import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AwsHandler<I> implements RequestHandler<I, String> {
  private static UnicornPrimingResource resource = null;

  public AwsHandler() {
    if (resource != null) {
      return;
    }
    App.init();
    resource = new UnicornPrimingResource();
  }

  public String handleRequest(I input, Context context) {
    if (isEmpty(input)) {
      log.info("empty input:{}", input);
      return StringUtils.EMPTY;
    }
    long start = System.currentTimeMillis();
    MdcUtil.addSpanId();
    MdcUtil.addTraceId();
    Object data;
    try {
      initSpringBoot();
      data = handleRequest(input);
    } catch (Throwable e) {
      log.error("AwsHandler error", e);
      return ReturnJacksonUtil.result(500, e.getMessage(), null, null);
    }
    log.info("times={}", System.currentTimeMillis() - start);

    return ReturnJacksonUtil.resultOk(data, Locale.ENGLISH);
  }

  protected boolean isEmpty(I input) {
    return false;
  }

  private void initSpringBoot() {
    int times = 0;
    while (times++ < 2) {
      try {
        if (ApplicationContextUtil.getApplicationContext() == null) {
          log.info("spring boot start times{}", times);
          App.main(new String[]{});
        }
        return;
      } catch (Exception e) {
        log.info("spring boot start", e);
      }
    }
  }

  public abstract Object handleRequest(I input) throws Exception;
}

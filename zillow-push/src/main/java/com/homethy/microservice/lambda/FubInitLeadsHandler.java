package com.homethy.microservice.lambda;

import com.homethy.fub.service.FubInitLeadsService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FubInitLeadsHandler extends BaseKafkaHandler {

  @Override
  public void doHandler(List<KafkaRecord> records) {
    ApplicationContextUtil.getBean(FubInitLeadsService.class).initImportFubLeads(records);
  }
}

package com.homethy.microservice.lambda;

import com.homethy.microservice.service.LeadMergeConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeadMergeKafkaHandler extends BaseKafkaHandler {
  @Override
  public void doHandler(List<KafkaRecord> records) {
    LeadMergeConsumerService leadMergeConsumerService =
        ApplicationContextUtil.getBean(LeadMergeConsumerService.class);
    leadMergeConsumerService.consume(records);
  }
}

package com.homethy.microservice.lambda;

import com.homethy.microservice.service.TransactionConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TransactionKafkaHandler extends BaseKafkaHandler {
  @Override
  public void doHandler(List<KafkaRecord> records) {
    TransactionConsumerService transactionConsumerService =
        ApplicationContextUtil.getBean(TransactionConsumerService.class);
    transactionConsumerService.consume(records);
  }
}

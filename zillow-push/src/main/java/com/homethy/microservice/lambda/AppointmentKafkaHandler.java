package com.homethy.microservice.lambda;


import com.homethy.microservice.service.AppointmentConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

public class AppointmentKafkaHandler  extends BaseKafkaHandler{
  @Override
  public void doHandler(List<KafkaRecord> records) {
    AppointmentConsumerService appointmentConsumerService =
        ApplicationContextUtil.getBean(AppointmentConsumerService.class);
    appointmentConsumerService.consume(records);
  }
}

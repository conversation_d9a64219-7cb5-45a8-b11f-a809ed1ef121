package com.homethy.microservice.lambda;

import com.homethy.microservice.App;
import com.homethy.microservice.util.ApplicationContextUtil;

import org.crac.Context;
import org.crac.Core;
import org.crac.Resource;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UnicornPrimingResource implements Resource {
  public UnicornPrimingResource() {
    log.info("hook init");
    Core.getGlobalContext().register(this);
  }

  @Override
  public void beforeCheckpoint(Context<? extends Resource> context) {
    try {
      log.info("beforeCheckpoint hook");
      long start = System.currentTimeMillis();
      if (ApplicationContextUtil.getApplicationContext() == null) {
        log.info("spring boot start");
        App.main(new String[]{});
      }
      log.info("spring boot times={}", System.currentTimeMillis() - start);
    } catch (Exception e) {
      log.error("beforeCheckpoint", e);
      // expected exception when unicorn doesn't exist.
    }
  }

  @Override
  public void afterRestore(Context<? extends Resource> context) {
    log.info("afterRestore hook");
  }

}

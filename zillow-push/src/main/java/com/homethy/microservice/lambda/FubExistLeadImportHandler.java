package com.homethy.microservice.lambda;

import com.homethy.fub.service.FubExistLeadImportService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FubExistLeadImportHandler extends BaseKafkaHandler {

  @Override
  public void doHandler(List<KafkaRecord> records) {
    ApplicationContextUtil.getBean(FubExistLeadImportService.class).handle(records);
  }
}

package com.homethy.microservice.lambda;


import com.homethy.microservice.service.LeadTaskConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

public class LeadTaskKafkaHandler extends BaseKafkaHandler{
  @Override
  public void doHandler(List<KafkaRecord> records) {
    LeadTaskConsumerService leadTaskConsumerService =
        ApplicationContextUtil.getBean(LeadTaskConsumerService.class);
    leadTaskConsumerService.consume(records);
  }
}

package com.homethy.microservice.lambda;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Locale;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.serialization.LongDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.KafkaEvent;
import com.homethy.microservice.App;
import com.homethy.microservice.service.LambdaNonRepetitiveService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.util.response.ReturnJacksonUtil;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.util.MdcUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BaseKafkaHandler implements RequestHandler<KafkaEvent, String> {

  private static LongDeserializer deserializer = new LongDeserializer();
  private static StringDeserializer stringDeserializer = new StringDeserializer();

  private static UnicornPrimingResource resource = null;

  public BaseKafkaHandler() {
    if (resource != null) {
      return;
    }
    App.init();
    resource = new UnicornPrimingResource();
  }

  @Override
  public String handleRequest(KafkaEvent input, Context context) {
    long start = System.currentTimeMillis();
    MdcUtil.addSpanId();
    MdcUtil.addTraceId();
    log.debug("spring boot times={}", System.currentTimeMillis() - start);
    start = System.currentTimeMillis();
    List<KafkaRecord> records = new ArrayList<>();
    boolean msk = input.getBootstrapServers() != null && input.getBootstrapServers().contains("amazonaws.com");
    input.getRecords().forEach((k, v) -> {
      v.forEach(e -> {
        KafkaRecord record = new KafkaRecord();
        record.setKey(deserialize(e.getTopic(), e.getKey()));
        record.setPartition(e.getPartition());
        record.setOffset(e.getOffset());
        record.setValue(deserializeValue(e.getTopic(), e.getValue()));
        record.setTopic(e.getTopic());
        record.setMsk(msk);
        records.add(record);
      });
    });
    log.info("input.getRecords {}, isMsk: {}", records.size(), msk);
    LambdaNonRepetitiveService lambdaNonRepetitiveService =
        ApplicationContextUtil.getBean(LambdaNonRepetitiveService.class);
    doHandler(lambdaNonRepetitiveService.filter(records, msk));
    log.info("times={}", System.currentTimeMillis() - start);
    return ReturnJacksonUtil.resultOk(Locale.ENGLISH);
  }

  public abstract void doHandler(List<KafkaRecord> records);

  public long deserialize(String topic, String key) {
    log.debug("deserialize key={}", key);
    if (StringUtils.isEmpty(key)) {
      return 0L;
    }
    try {
      return deserializer.deserialize(topic, Base64.getDecoder().decode(key));
    } catch (Exception e) {
      log.error("str deserialize key={}", key, e);
    }
    return 0L;
  }

  public String deserializeValue(String topic, String value) {
    log.debug("deserialize key={}", value);
    if (StringUtils.isEmpty(value)) {
      return "";
    }
    try {
      return stringDeserializer.deserialize(topic, Base64.getDecoder().decode(value));
    } catch (Exception e) {
      log.error("str deserialize key={}", value, e);
    }
    return "";
  }
}

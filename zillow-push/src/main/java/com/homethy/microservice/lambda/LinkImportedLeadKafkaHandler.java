package com.homethy.microservice.lambda;

import com.homethy.microservice.service.LinkOtherLeadConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

public class LinkImportedLeadKafkaHandler extends BaseKafkaHandler{
  @Override
  public void doHandler(List<KafkaRecord> records) {
    LinkOtherLeadConsumerService linkOtherLeadConsumerService =
        ApplicationContextUtil.getBean(LinkOtherLeadConsumerService.class);
    linkOtherLeadConsumerService.consume(records);
  }
}

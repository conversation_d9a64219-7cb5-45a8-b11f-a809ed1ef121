package com.homethy.microservice.lambda;

import com.homethy.microservice.service.TransactionToFubConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class FubTransactionKafkaHandler extends BaseKafkaHandler{

  @Override
  public void doHandler(List<KafkaRecord> records) {
    ApplicationContextUtil.getBean(TransactionToFubConsumerService.class).consume(records);
  }
}

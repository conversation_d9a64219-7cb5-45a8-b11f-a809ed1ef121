package com.homethy.microservice.lambda;

import com.homethy.microservice.service.FubToLoftyConsumerService;
import com.homethy.microservice.service.ImportLeadConsumerService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FubToLoftyHandler extends BaseKafkaHandler{

  @Override
  public void doHandler(List<KafkaRecord> records) {
    FubToLoftyConsumerService fubToLoftyConsumerService =
        ApplicationContextUtil.getBean(FubToLoftyConsumerService.class);
    fubToLoftyConsumerService.consume(records);

  }
}

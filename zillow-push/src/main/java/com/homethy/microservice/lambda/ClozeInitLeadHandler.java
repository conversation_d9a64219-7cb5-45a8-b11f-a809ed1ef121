package com.homethy.microservice.lambda;

import com.homethy.cloze.service.ClozeInitLeadsService;
import com.homethy.microservice.util.ApplicationContextUtil;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

/**
 * export history data to cloze
 *
 * <AUTHOR>
 */
public class ClozeInitLeadHandler extends BaseKafkaHandler {

  @Override
  public void doHandler(List<KafkaRecord> records) {
    ApplicationContextUtil.getBean(ClozeInitLeadsService.class).initClozeLeads(records);
  }
}

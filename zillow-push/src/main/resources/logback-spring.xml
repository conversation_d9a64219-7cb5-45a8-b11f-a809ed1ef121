<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/default.xml"/>
    <!--<jmxConfigurator/>-->

    <springProperty scope="context" name="applicationName" source="spring.application.name"/>
    <property name="LOG_FILE"
              value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/app.log}"/>
    <appender name="APP-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- keep 30 days' worth of history capped at 3GB total size -->
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder class="com.homethy.logback.DesensitizationLayoutEncoder">
            <pattern>
                <!--[%date{ISO8601}]|%-5level|[%thread]|%logger{36}|%msg%n-->
                [%date{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}]|%level|[${applicationName},%X{traceId:-},%X{spanId:-}]|[%thread]|%c{0}:%L|%msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                [%date{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}]|%level|[${applicationName},%X{traceId:-},%X{spanId:-}]|[%thread]|%c{0}:%L|%msg%n
            </pattern>
        </encoder>
    </appender>

    <logger name="org.apache.kafka.clients.consumer.ConsumerConfig" level="off"/>

    <logger name="org.apache.kafka.common.config.AbstractConfig" level="off"/>

    <logger name="org.apache.kafka.clients.producer.ProducerConfig" level="off"/>

    <logger name="com.zaxxer.hikari.pool.HikariPool" level="off"/>

    <springProfile name="test,dev">
        <logger name="ShardingSphere-SQL" level="info"/>
    </springProfile>

    <logger name="com.homethy.configcenter" level="warn"/>
    <logger name="com.homethy.microservice.mvc" level="warn"/>
    <logger name="org.apache.commons.httpclient" level="error"/>
    <logger name="org.apache.kafka.clients" level="error"/>
    <logger name="org." level="warn"/>
    <root level="INFO">
<!--        <appender-ref ref="APP-LOG"/>-->
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>

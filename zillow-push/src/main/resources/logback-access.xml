<configuration>
    <!-- always a good activate OnConsoleStatusListener -->
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/>
    <springProperty scope="context" name="applicationName" source="spring.application.name"/>

    <property name="ACCESS_LOG_FILE"
              value="${ACCESS_LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/access.log}"/>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ACCESS_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${ACCESS_LOG_FILE}.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>

        <encoder>
            <pattern>
                [%date{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}]|[${applicationName},%i{X-B3-TraceId},%i{X-B3-SpanId},%i{X-Span-Export:-}]|%A|%h|%i{X-Real-IP}|%reqAttribute{LOGIN_USER_ID}|%r|%s|%b|%D|%i{Referer}|%i{User-Agent}
            </pattern>
        </encoder>
    </appender>

    <appender-ref ref="FILE"/>
</configuration>

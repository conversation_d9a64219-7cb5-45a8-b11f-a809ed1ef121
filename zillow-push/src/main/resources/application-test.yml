microservice:
  user:
    url: "https://crm.test.lofty.com/lambda-gateway/user"
  user-kernel:
    url: "https://crm.test.lofty.com/lambda-gateway/user-kernel"
  lead-user-kernel:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-user-kernel"
  user-permission-offline:
    url: "https://crm.test.lofty.com/lambda-gateway/user-permission-offline"
  leadsearch:
    url: "http://lead-search-test.w.chime.me"
  lead-permission-offline:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-permission-offline"
  lead-kernel:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-kernel"
  agent-offline:
    url: "https://crm.test.lofty.com/lambda-gateway/agent-offline"
  lead:
    url: "https://crm.test.lofty.com/lambda-gateway/lead"
    timeline:
      url: "https://crm.test.lofty.com/lambda-gateway/timeline"
  user-permission:
    url: "https://crm.test.lofty.com/lambda-gateway/user-permission"
  email:
    url: "https://crm.test.lofty.com/lambda-gateway/email"
  lead-offline:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-offline"
  lead-user-contact:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-user-contact"
  lead-assign:
    url: "https://crm.test.lofty.com/lambda-gateway/lead-assign"
  thirdparty:
    integration:
      url: "https://crm.test.lofty.com/lambda-gateway/thirdparty"
  transaction:
    url: "https://crm.test.lofty.com/lambda-gateway/transaction"
  smart-plan:
    url: "https://crm.test.lofty.com/lambda-gateway/smart-plan"
  leadimport:
    url: "https://crm.test.lofty.com/lambda-gateway/leadimport"
  notification:
    url: "https://crm.test.lofty.com/lambda-gateway/notification"
  international:
    url: "https://crm.test.lofty.com/lambda-gateway/international"

microservice:
  user:
    url: "https://crm-lambda.d.chime.me/lambda/user"
  user-kernel:
    url: "https://crm-lambda.d.chime.me/lambda/user-kernel"
  lead-user-kernel:
    url: "https://crm-lambda.d.chime.me/lambda/lead-user-kernel"
  user-permission-offline:
    url: "https://crm-lambda.d.chime.me/lambda/user-permission-offline"
  leadsearch:
    url: "http://lead-search.d.chime.me"
  lead-permission-offline:
    url: "https://crm-lambda.d.chime.me/lambda/lead-permission-offline"
  lead-kernel:
    url: "https://crm-lambda.d.chime.me/lambda/lead-kernel"
  agent-offline:
    url: "https://crm-lambda.d.chime.me/lambda/agent-offline"
  lead:
    url: "https://crm-lambda.d.chime.me/lambda/lead"
    timeline:
      url: "https://crm-lambda.d.chime.me/lambda/timeline"
  user-permission:
    url: "https://crm-lambda.d.chime.me/lambda/user-permission"
  email:
    url: "https://crm-lambda.d.chime.me/lambda/email"
  lead-offline:
    url: "https://crm-lambda.d.chime.me/lambda/lead-offline"
  lead-user-contact:
    url: "https://crm-lambda.d.chime.me/lambda/lead-user-contact"
  lead-assign:
    url: "https://crm-lambda.d.chime.me/lambda/lead-assign"
  thirdparty:
    integration:
      url: "https://crm-lambda.d.chime.me/lambda/thirdparty"
  transaction:
    url: "https://crm-lambda.d.chime.me/lambda/transaction"
  smart-plan:
    url: "https://crm-lambda.d.chime.me/lambda/smart-plan"
  leadimport:
    url: "https://crm-lambda.d.chime.me/lambda/leadimport"
  notification:
    url: "https://crm-lambda.d.chime.me/lambda/notification"
  international:
    url: "https://crm-lambda.d.chime.me/lambda/international"
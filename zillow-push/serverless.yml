service: zillow-push
useDotenv: true
provider:
  name: aws
  runtime: java17
  environment:
    env_type: "lambda"
  timeout: 900     # Timeout (default: 6 seconds)
package:
  artifact: build/distributions/zillow-push.zip
functions:
  zillow-communication-push-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.LambadaKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &kafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.bootstrapServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.topic"
          topic: "lead.timeline.update.topic"
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.manual.topic"
          topic: "lead.timeline.update.manual.topic"
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.offline.topic"
          topic: "lead.timeline.update.offline.topic"
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_timelineSearch.all.timeline"
          topic: "timelineSearch.all.timeline"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  zillow-communication-push-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.LambadaKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.topic"
          topic: "lead.timeline.update.topic"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.manual.topic"
          topic: "lead.timeline.update.manual.topic"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.timeline.update.offline.topic"
          topic: "lead.timeline.update.offline.topic"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_timelineSearch.all.timeline"
          topic: "timelineSearch.all.timeline"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  whiteList-remove-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.DisconnectKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &kafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.bootstrapServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.topic.function.change.user"
          topic: "topic.function.change.user"
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.topic.user.display.change"
          topic: "topic.user.display.change"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  whiteList-remove-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.DisconnectKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.topic.function.change.user"
          topic: "topic.function.change.user"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.topic.user.display.change"
          topic: "topic.user.display.change"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  import-lead-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.ImportLeadKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.zillow.import.lead"
          topic: "zillow.import.lead"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.zillow.import.lead"
          topic: "zillow.init.lead"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  lead-merge-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.LeadMergeKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &kafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.bootstrapServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.topic.lead.merge.pair"
          topic: "topic.lead.merge.pair"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  lead-merge-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.LeadMergeKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.topic.lead.merge.pair"
          topic: "topic.lead.merge.pair"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  transaction-sync-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.TransactionKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &kafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.bootstrapServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *kafkaConfig
          consumerGroupId: "zillow.group_lead.topic.transaction.sync.zillow"
          topic: "topic.transaction.sync.zillow"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  transaction-sync-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.TransactionKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "zillow.group_lead.topic.transaction.sync.zillow"
          topic: "topic.transaction.sync.zillow"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  link-imported-lead-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.LinkImportedLeadKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 5
        maximumBatchingWindow: 5
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime-zillow.group.chime-zillow.imported_lead_link"
          topic: "chime-zillow.imported_lead_link"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  fub-to-lofty-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubToLoftyHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "fub.group_lead.topic.lofty"
          topic: "chime_zillow.fub_import"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  leadtask-from-lofty-kafka-msk:
    snapStart: true
    handler: com.homethy.microservice.lambda.LeadTaskKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 160
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.sync.task_create"
          topic: "smart_plan.task_create"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.sync.task_update"
          topic: "smart_plan.task_update"
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.sync.task_delete"
          topic: "smart_plan.task_delete"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  schedule:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubRetrySchedule
    environment:
      JAVA_TOOL_OPTIONS: "${env:jvmOptions} -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name} -Denable.clickhouse=true"
    events:
      - schedule:
          rate: rate(1 minute)
          input:
            detail:
              eventType: fubRetrySchedule
      - schedule:
          rate: rate(5 minutes)
          input:
            detail:
              eventType: rePushNotesToFub
    onError: ${self:custom.onError}
    logRetentionInDays: 90
    maximumRetryAttempts: 0
  fub-push-new-lead-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubNewLeadPushHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.bootstrapServers}
        batchSize: 60
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.sync"
          topic: "lead.add_lead_v2"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  transaction-sync-to-fub-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubTransactionKafkaHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 60
        maximumBatchingWindow: 10
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.zillow_push.sync_to_fub"
          topic: "update.transaction.es.topic"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  fub-init-lead-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubInitLeadsHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 10
        maximumBatchingWindow: 1
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.fub.init"
          topic: "chime_zillow.fub_init_lead"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  cloze-init-lead-kafka:
    snapStart: true
    handler: com.homethy.microservice.lambda.FubInitLeadsHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 10
        maximumBatchingWindow: 1
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.fub.init"
          topic: "chime_zillow.fub_init_lead"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
  fub-exist-lead-import-kafka:
    snapStart: true
    memorySize: 1536
    handler: com.homethy.microservice.lambda.FubExistLeadImportHandler
    environment:
      spring_application_name: ${env:spring_application_name}
      PROFILE: ${env:PROFILE}
      JAVA_TOOL_OPTIONS: "-XX:+TieredCompilation -XX:TieredStopAtLevel=1 -Xms${env:jvmMemorySize} -Xmx${env:jvmMemorySize} -Dsun.net.inetaddr.ttl=0 -Dspring.application.name=${env:spring_application_name} -Dspring.profiles.active=${env:PROFILE} -Dhttp.agent=${env:spring_application_name}"
    events:
      - &mskKafkaConfig
        accessConfigurations: ${self:custom.kafkaConfig.accessConfigurations}
        bootstrapServers: ${self:custom.kafkaConfig.mskServers}
        batchSize: 50
        maximumBatchingWindow: 1
        startingPosition: LATEST
      - kafka:
          <<: *mskKafkaConfig
          consumerGroupId: "chime_zillow.group.fub.exist.lead"
          topic: "chime_zillow.fub_lead_import"
    onError: ${self:custom.onError}
    logRetentionInDays: 90
package com.homethy.chimeZillow.client;

import com.homethy.chimeZillow.client.model.ZillowLeadCheckDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "${microservice.chime-zillow.service.id:chime-zillow}",
    url = "${microservice.chime-zillow.url:http://chime-zillow.crm.svc:8080}",
    path = "/client")
public interface ZillowLeadClient {

  @GetMapping("/checking/zillow-leads")
  List<Long> checkZillowLeads(@RequestParam(name = "leadIds") List<Long> leadIds);

  @PutMapping(value = "/merge/zillow-leads/from/{fromLeadId}/to/{toLeadId}")
  boolean processTrForMergeLead(
      @PathVariable(name = "fromLeadId") long fromLeadId,
      @PathVariable(name = "toLeadId") long toLeadId);

  @GetMapping("/info/zillow-leads")
  List<ZillowLeadCheckDTO> zillowLeadsInfo(@RequestParam(name = "leadIds") List<Long> leadIds);
}

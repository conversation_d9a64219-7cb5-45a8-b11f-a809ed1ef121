package com.homethy.chimeZillow.client;

import com.homethy.chimeZillow.client.model.ZillowTrDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "${microservice.chime-zillow.service.id:chime-zillow}",
    url = "${microservice.chime-zillow.url:http://chime-zillow.crm.svc:8080}",
    path = "/client")
public interface ZillowTansactionClient {

  @PutMapping("/transaction/push/{transactionId}")
  boolean push(@PathVariable long transactionId);

  @RequestMapping(value = "/checking/zillow-tansactions", method = {RequestMethod.GET})
  List<Long> checkZillowTrs(@RequestParam(name = "ids") List<Long> ids);

  @PutMapping(value = "/remove/zillow-tansactions")
  void deleteByIds(@RequestParam(name = "ids") List<Long> ids);

  @PutMapping(value = "/merge/zillow-tansactions/from/{fromLeadId}/to/{toLeadId}")
  boolean processTrForMergeLead(
      @PathVariable(name = "fromLeadId") long fromLeadId,
      @PathVariable(name = "toLeadId") long toLeadId);

  @RequestMapping(value = "/zillow-tansactions", method = {RequestMethod.GET})
  List<ZillowTrDTO> getZillowTr(@RequestParam(name = "ids") List<Long> ids);
}

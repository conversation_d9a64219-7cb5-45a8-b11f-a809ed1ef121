package com.homethy.chimeZillow.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "${microservice.chime-zillow.service.id:chime-zillow}",
    url = "${microservice.chime-zillow.url:http://chime-zillow.crm.svc:8080}")
public interface ZillowTokenClient {
  @GetMapping("/isBinding")
  boolean isBinding(@RequestParam("agentId") long agentId);
}

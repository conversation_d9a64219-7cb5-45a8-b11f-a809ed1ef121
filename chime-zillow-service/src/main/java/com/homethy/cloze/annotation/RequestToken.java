package com.homethy.cloze.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解：标记参数只用于上下文处理，不发送到实际请求
 * 
 * 使用场景：
 * - 当需要传递对象到 Feign 客户端用于拦截器处理
 * - 但不想将这个对象发送到实际的 HTTP 请求中
 * 
 * 示例：
 * 
 * <pre>
 * &#64;GetMapping("/v1/user/profile")
 * String getUserProfile(@RequestHeader("Authorization") String authToken,
 *         @ContextOnly ClozeOauth2Token contextToken);
 * </pre>
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequestToken {

    String value() default "";

}

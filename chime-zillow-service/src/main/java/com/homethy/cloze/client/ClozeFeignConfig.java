package com.homethy.cloze.client;

import org.springframework.context.annotation.Bean;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClozeFeignConfig {

    @Bean
    public RequestInterceptor headerInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                template.header("Accept", "application/json");
                RequestTokenParameterProcessor.setTemplate(template);
            }
        };
    }

    @Bean
    public RequestTokenParameterProcessor requestTokenParameterProcessor() {
        return new RequestTokenParameterProcessor();
    }

    // @Bean
    // public ErrorDecoder errorDecoder() {
    // return new RateErrorDecoder();
    // }
}

package com.homethy.cloze.client;

import java.lang.annotation.Annotation;
import java.util.Arrays;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.cloud.openfeign.AnnotatedParameterProcessor;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.cloze.annotation.RequestToken;

import feign.MethodMetadata;
import feign.Param;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign parameter processor:
 * - Marks a method parameter annotated with {@link RequestToken} as a hidden
 * context header so it is not sent as a request param/body directly.
 * - Uses a custom {@link feign.Param.Expander} to encode
 * {@link ClozeOauth2Token}
 * into a compact string payload carried by the hidden header.
 * - A request interceptor later reads this hidden header to route the token to
 * either the Authorization header (Bearer) or the api_key query parameter.
 */
@Slf4j
public class RequestTokenParameterProcessor implements AnnotatedParameterProcessor {

    public static final String CONTEXT_HEADER = "X-Cloze-Token";

    @Override
    public Class<? extends Annotation> getAnnotationType() {
        return RequestToken.class;
    }

    @Override
    public boolean processArgument(AnnotatedParameterContext context, Annotation annotation,
            java.lang.reflect.Method method) {
        final MethodMetadata metadata = context.getMethodMetadata();
        final int paramIndex = context.getParameterIndex();

        // Name the parameter with the hidden context header for placeholder binding
        metadata.indexToName().put(paramIndex, Arrays.asList(CONTEXT_HEADER));

        // Attach an Expander to encode ClozeOauth2Token into a string at runtime
        metadata.indexToExpanderClass().put(paramIndex, ClozeTokenToAuthHeaderExpander.class);

        // Declare the hidden header whose value comes from the parameter placeholder
        metadata.template().header(CONTEXT_HEADER, "{" + CONTEXT_HEADER + "}");

        // 返回 true 表示该参数已被处理
        return true;
    }

    /**
     * Encodes {@link ClozeOauth2Token} into a payload string stored in the
     * hidden header for the interceptor to parse and route later.
     */
    public static class ClozeTokenToAuthHeaderExpander implements Param.Expander {
        @Override
        public String expand(Object value) {
            if (value == null) {
                return "";
            }
            if (value instanceof ClozeOauth2Token) {
                return getCallToken((ClozeOauth2Token) value);
            }
            if (value instanceof CharSequence) {
                return value.toString();
            }
            throw new IllegalArgumentException("@RequestToken parameter must be ClozeOauth2Token or String");
        }
    }

    /**
     * Build a compact token string for transport via the hidden header.
     * Format:
     * - "Bearer {accessToken}" when accessToken is present
     * - "api_key {apiKey}" when accessToken is empty
     */
    public static String getCallToken(ClozeOauth2Token token) {
        if (token.getAccessToken() == null || token.getAccessToken().trim().isEmpty()) {
            return "api_key " + token.getApiKey();
        }
        return "Bearer " + token.getAccessToken();
    }

    /**
     * Parse the compact token string back to a pair of (scheme, value).
     * Example inputs:
     * - "Bearer abc" -> ("Bearer", "abc")
     * - "api_key xyz" -> ("api_key", "xyz")
     */
    public static Pair<String, String> getCallToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }
        if (token.startsWith("api_key ")) {
            return Pair.of("api_key", token.substring("api_key ".length()));
        }
        return Pair.of("Bearer", token.substring("Bearer ".length()));
    }

    /**
     * Route the token from the hidden header into either:
     * - Authorization: Bearer ... (when scheme is Bearer), or
     * - query param api_key=... (when scheme is api_key).
     * The hidden header is removed afterwards.
     */
    public static void setTemplate(RequestTemplate template) {
        Pair<String, String> payload = extractHiddenTokenPayload(template);
        if (payload == null) {
            return;
        }
        if ("api_key".equals(payload.getLeft())) {
            template.query("api_key", payload.getRight());
        } else {
            template.header("Authorization", payload.getLeft() + " " + payload.getRight());
        }
        template.headers().remove(RequestTokenParameterProcessor.CONTEXT_HEADER);
    }

    /**
     * Extract and parse the hidden header payload.
     */
    private static Pair<String, String> extractHiddenTokenPayload(RequestTemplate template) {
        try {
            String raw = template.headers()
                    .get(RequestTokenParameterProcessor.CONTEXT_HEADER).stream().findFirst()
                    .orElse(null);
            return getCallToken(raw);
        } catch (Exception e) {
            log.debug("Failed to parse X-Cloze-Token", e);
            return null;
        }
    }
}

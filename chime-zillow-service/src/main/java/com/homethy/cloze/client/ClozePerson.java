package com.homethy.cloze.client;

import lombok.Data;

import java.util.List;

@Data
public class Cloze<PERSON>erson {
    private String id;
    private String first;
    private String last;
    private String syncKey;
    private List<ClozeEmail> emails;
    private List<ClozePhone> phones;
    private String segment;
    private List<String> keywords;
    //mapping the primary agent email
    private String assignTo;
    private List<ClozeAddress> addresses;
}

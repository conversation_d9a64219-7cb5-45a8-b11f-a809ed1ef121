package com.homethy.cloze.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.cloze.annotation.RequestToken;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "https://api.cloze.com", name = "cloze", configuration = ClozeFeignConfig.class)
public interface ClozeClient {
    // TODO:TOKEN TOKEN
    @PostMapping("/oauth/token")
    TokenResult getToken(@RequestHeader("Authorization") String code);

    // TODO:TOKEN TOKEN
    @PostMapping("/refresh/oauth/token")
    TokenResult refreshToken(@RequestToken ClozeOauth2Token token);

    @GetMapping("/v1/user/profile")
    ClozeResult<ClozeProfile> getUserProfile(@RequestToken ClozeOauth2Token token);

    @GetMapping("/v1/people/get")
    ClozeResult<ClozePerson> getPerson(@RequestToken ClozeOauth2Token token, @RequestParam String uniqueid);

    @PostMapping("/v1/people")
    ClozeResult<ClozePerson> createPerson(@RequestToken ClozeOauth2Token token, @RequestBody ClozePerson person);
}

package com.homethy.cloze.dao;

import com.homethy.cloze.model.constant.ClozeNotifyLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Mapper
public interface ClozeNotifyLogDao {

  String table = "cloze_notify_log";
  String all = "id, cloze_id, lofty_id, type, notify_msg, push_msg, status, result, retry, " +
      "ownership_id, ownership_scope, create_time, update_time";

  @Insert("INSERT INTO " + table + " (cloze_id, lofty_id, type, notify_msg, push_msg, status, " +
      "result, retry, ownership_id, ownership_scope) VALUES " +
      "(#{clozeId}, #{loftyId}, #{type}, #{notifyMsg}, #{pushMsg}, #{status}, #{result}, " +
      "#{retry}, #{ownershipId}, #{ownershipScope})")
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insert(ClozeNotifyLog clozeNotifyLog);


  @Select("SELECT " + all + " FROM " + table + " WHERE lofty_id = #{loftyId} AND type = #{type} " +
      "ORDER BY create_time DESC LIMIT 1")
  ClozeNotifyLog selectByLoftyIdAndType(@Param("loftyId") long loftyId, @Param("type") String type);

  @Select("SELECT " + all + " FROM " + table + " WHERE status IN (1, 2, 9) AND retry < 3 " +
      "ORDER BY create_time ASC LIMIT #{limit}")
  List<ClozeNotifyLog> selectRetryRecords(@Param("limit") int limit);

  @Update("<script>" +
          "UPDATE " + table + " SET " +
          "<if test='clozeId != null'>cloze_id = #{clozeId},</if>" +
          "<if test='type != null'>type = #{type},</if>" +
          "<if test='notifyMsg != null'>notify_msg = #{notifyMsg},</if>" +
          "<if test='pushMsg != null'>push_msg = #{pushMsg},</if>" +
          "<if test='status != null'>status = #{status},</if>" +
          "<if test='result != null'>result = #{result},</if>" +
          "<if test='retry != null'>retry = #{retry},</if>" +
          "<if test='ownershipId != null'>ownership_id = #{ownershipId},</if>" +
          "<if test='ownershipScope != null'>ownership_scope = #{ownershipScope},</if>" +
          "update_time = CURRENT_TIMESTAMP " +
          "WHERE id = #{id}" +
          "</script>")
  int update(ClozeNotifyLog clozeNotifyLog);

}

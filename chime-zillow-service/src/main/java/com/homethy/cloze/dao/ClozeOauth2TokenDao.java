package com.homethy.cloze.dao;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;

@Mapper
@DataSourceBase
public interface ClozeOauth2TokenDao {

    String table = " cloze_oauth2_token ";
    String all = " id,team_id, access_token, refresh_token, api_key, cloze_email, cloze_id, "
            + "operator_id, delete_flag, create_time, update_time, refresh_time, expires_in, "
            + "ownership_id, ownership_scope, sync_history_lead ";

    @Insert("INSERT INTO " + table + " (team_id,access_token, refresh_token, api_key, cloze_email, "
                    + "cloze_id, operator_id, delete_flag, refresh_time, "
            + "expires_in, ownership_id, ownership_scope, sync_history_lead) "
                    + "VALUES (#{teamId}, #{accessToken}, #{refreshToken}, #{apiKey}, #{clozeEmail}, "
                    + "#{clozeId}, #{operatorId}, #{deleteFlag}, "
            + "#{refreshTime}, #{expiresIn}, #{ownershipId}, #{ownershipScope}, #{syncHistoryLead})")
    int insertToken(ClozeOauth2Token token);

    @Update("UPDATE " + table
                    + " SET team_id = #{teamId}, access_token = #{accessToken}, refresh_token = #{refreshToken}, "
            + "api_key = #{apiKey}, cloze_email = #{clozeEmail}, cloze_id = #{clozeId}, "
                    + "operator_id = #{operatorId}, delete_flag = #{deleteFlag}, "
            + "refresh_time = #{refreshTime}, expires_in = #{expiresIn}, ownership_id = #{ownershipId}, "
                    + "ownership_scope = #{ownershipScope}, sync_history_lead = #{syncHistoryLead}, "
            + "WHERE id = #{id}")
    int updateToken(ClozeOauth2Token token);

    @Update("UPDATE " + table
                    + " SET  sync_history_lead = #{syncHistoryLead} "
                    + "WHERE id = #{id}")
    int updateTokenSyncHistoryLead(ClozeOauth2Token token);

    @Update("UPDATE " + table
                    + " SET access_token = #{accessToken}, refresh_time = #{refreshTime},refresh_token = #{refreshToken} "
                    + "expires_in = #{expiresIn} WHERE id = #{id}")
    int updateAccessToken(ClozeOauth2Token token);

    @Update("UPDATE " + table + " SET delete_flag = 1 WHERE id = #{id}")
    int deleteToken(@Param("id") Long id);

    @Update("UPDATE " + table + " SET delete_flag = 1 "
            + "WHERE ownership_id = #{ownershipId} AND ownership_scope = #{ownershipScope}")
    int deleteByOwnership(@Param("ownershipId") Long ownershipId,
                    @Param("ownershipScope") OwnershipScope ownershipScope);

    @Select("SELECT " + all + " FROM " + table + " WHERE id = #{id} AND delete_flag = 0")
    ClozeOauth2Token getTokenById(@Param("id") Long id);

    @Select("SELECT " + all + " FROM " + table + " WHERE ownership_id = #{ownershipId} "
            + "AND ownership_scope = #{ownershipScope} AND delete_flag = 0")
    ClozeOauth2Token getTokenByOwnership(@Param("ownershipId") Long ownershipId,
                    @Param("ownershipScope") OwnershipScope ownershipScope);

    @Select("SELECT " + all + " FROM " + table + " WHERE ownership_id = #{ownershipId} "
                    + "AND ownership_scope = #{ownershipScope} order by create_time desc limit 1")
    ClozeOauth2Token getTokenByOwnershipWithDelete(@Param("ownershipId") Long ownershipId,
                    @Param("ownershipScope") OwnershipScope ownershipScope);

    @Select("SELECT " + all + " FROM " + table + " WHERE cloze_id = #{clozeId} AND delete_flag = 0")
    ClozeOauth2Token getTokenByClozeId(@Param("clozeId") String clozeId);

    @Select("SELECT " + all + " FROM " + table + " WHERE cloze_email = #{clozeEmail} AND delete_flag = 0")
    ClozeOauth2Token getTokenByClozeEmail(@Param("clozeEmail") String clozeEmail);

    @Select("SELECT " + all + " FROM " + table + " WHERE operator_id = #{operatorId} AND delete_flag = 0")
    List<ClozeOauth2Token> getTokensByOperatorId(@Param("operatorId") Long operatorId);

    @Lang(MybatisExtendedLanguageDriver.class)
    @Delete("DELETE FROM " + table + " WHERE id IN (#{ids})")
    int removeTokens(@Param("ids") List<Long> ids);

    @Select("SELECT " + all + " FROM " + table + " WHERE delete_flag = 0")
    List<ClozeOauth2Token> getAllActiveTokens();
}

package com.homethy.cloze.dao;

import com.homethy.cloze.model.constant.ClozeLead;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Mapper
public interface ClozeLeadDao {

  String table = "cloze_lead";
  String all = "id, lead_id, cloze_sync_key, ownership_scope, ownership_id, delete_flag, " +
      "create_time, update_time";


  @Insert("INSERT INTO " + table + " (lead_id, cloze_sync_key, ownership_scope, ownership_id) VALUES " +
      "(#{leadId}, #{clozeSyncKey}, #{ownershipScope}, #{ownershipId})")
  @Options(useGeneratedKeys = true, keyProperty = "id")
  int insert(ClozeLead clozeLead);

  @Select("SELECT " + all + " FROM " + table + " WHERE id = #{id} AND delete_flag = 0")
  ClozeLead selectById(@Param("id") long id);

  @Select("SELECT " + all + " FROM " + table + " WHERE lead_id = #{leadId} AND delete_flag = 0 " +
      "ORDER BY create_time DESC LIMIT 1")
  ClozeLead getLatestClozeLeadByLeadId(@Param("leadId") long leadId);

  @Select("SELECT " + all + " FROM " + table + " WHERE lead_id = #{leadId} " +
      "AND ownership_scope = #{ownershipScope} AND ownership_id = #{ownershipId} " +
      "AND delete_flag = 0 LIMIT 1")
  ClozeLead selectByLeadIdAndOwnership(@Param("leadId") long leadId,
                                      @Param("ownershipScope") String ownershipScope,
                                      @Param("ownershipId") long ownershipId);

  @Select("SELECT " + all + " FROM " + table + " WHERE cloze_sync_key = #{clozeSyncKey} " +
      "AND delete_flag = 0 LIMIT 1")
  ClozeLead selectByClozeSyncKey(@Param("clozeSyncKey") String clozeSyncKey);

}

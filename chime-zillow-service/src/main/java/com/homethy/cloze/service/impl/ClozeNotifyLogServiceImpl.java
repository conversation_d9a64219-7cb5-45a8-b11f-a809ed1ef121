package com.homethy.cloze.service.impl;

import com.homethy.cloze.dao.ClozeNotifyLogDao;
import com.homethy.cloze.model.constant.ClozeNotifyLog;
import com.homethy.cloze.service.ClozeNotifyLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/***
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClozeNotifyLogServiceImpl implements ClozeNotifyLogService {

  @Autowired
  private ClozeNotifyLogDao clozeNotifyLogDao;

  @Override
  public int insert(ClozeNotifyLog clozeNotifyLog) {
    try {
      return clozeNotifyLogDao.insert(clozeNotifyLog);
    } catch (Exception e) {
      log.error("Error inserting ClozeNotifyLog: {}", clozeNotifyLog, e);
      return 0;
    }
  }

  public ClozeNotifyLog createSuccessLog(long loftyId, String clozeId, String type, 
                                        String notifyMsg, String pushMsg,
                                        String ownershipScope, long ownershipId) {
    ClozeNotifyLog clozeNotifyLog = ClozeNotifyLog.builder()
        .loftyId(loftyId)
        .clozeId(clozeId)
        .type(type)
        .notifyMsg(notifyMsg)
        .pushMsg(pushMsg)
        .status(ClozeNotifyLog.Status.SUCCESS.getCode())
        .retry(0)
        .ownershipScope(ownershipScope)
        .ownershipId(ownershipId)
        .build();
    clozeNotifyLogDao.update(clozeNotifyLog);
    return clozeNotifyLog;
  }

  public ClozeNotifyLog createFailureLog(long loftyId, String type, String notifyMsg, 
                                        String result, int status,
                                        String ownershipScope, long ownershipId) {
    ClozeNotifyLog clozeNotifyLog = ClozeNotifyLog.builder()
        .loftyId(loftyId)
        .type(type)
        .notifyMsg(notifyMsg)
        .status(status)
        .result(result)
        .retry(0)
        .ownershipScope(ownershipScope)
        .ownershipId(ownershipId)
        .build();
    clozeNotifyLogDao.update(clozeNotifyLog);
    return clozeNotifyLog;
  }

}

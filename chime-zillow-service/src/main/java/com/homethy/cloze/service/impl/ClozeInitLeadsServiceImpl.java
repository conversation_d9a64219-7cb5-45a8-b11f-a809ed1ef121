package com.homethy.cloze.service.impl;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.cloze.model.constant.ClozeNotifyLog;
import com.homethy.cloze.model.constant.ClozeStateInfo;
import com.homethy.cloze.model.po.ClozeLeadInit;
import com.homethy.cloze.service.ClozeInitLeadsService;
import com.homethy.cloze.service.ClozeNotifyLogService;
import com.homethy.cloze.service.ClozeOauth2TokenService;
import com.homethy.cloze.service.ClozePushService;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.leadsearch.lead.LeadDetailBo;
import com.homethy.microservice.client.leadsearch.lead.SearchResultBo;
import com.homethy.zillow.manager.LeadSearchManager;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.kafka.producer.KafkaUtil;
import com.homethy.util.jackson.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.homethy.zillow.model.constant.KafkaTopic.TOPIC_CLOZE_INIT_LEAD;
/***
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClozeInitLeadsServiceImpl implements ClozeInitLeadsService {

  private static final int DEFAULT_PAGE_SIZE = 50;

  @Autowired
  private ClozePushService clozePushService;
  @Autowired
  private LeadSearchManager leadSearchManager;
  @Autowired
  private UserManager userManager;
  @Autowired
  private ClozeNotifyLogService clozeNotifyLogService;
  @Autowired
  private ClozeOauth2TokenService clozeOauth2TokenService;

  @Override
  public void initClozeLeads(List<KafkaRecord> records) {
    records.forEach(this::handleSingleRecord);
  }

  private void handleSingleRecord(KafkaRecord record) {
    try {
      ClozeLeadInit clozeLeadInit = JacksonUtils.fromJson(record.getValue(), ClozeLeadInit.class);
      log.info("Processing ClozeLeadInit: {}", clozeLeadInit);

      switch (clozeLeadInit.getType()) {
        case EXPORT_LEAD -> doExportLead(clozeLeadInit);
        case EXPORT_LEAD_PAGE -> doExportLeadPage(clozeLeadInit);
        case EXPORT_NOTE -> doExportNote(clozeLeadInit);
        default -> log.warn("Unknown sync type: {}", clozeLeadInit.getType());
      }

    } catch (Exception e) {
      log.error("Error processing ClozeLeadInit record: {}", record.getValue(), e);
    }
  }


  private void doExportLead(ClozeLeadInit clozeLeadInit) {
    ClozeStateInfo clozeInfo = clozeLeadInit.getClozeInfo();
    if (clozeInfo == null || clozeInfo.getOwnershipScope() != OwnershipScope.PERSONAL) {
      log.info("not personal,ignore");
      return;
    }

    ClozeOauth2Token clozeOauth2Token = clozeOauth2TokenService.refreshToken(OwnershipInfo.builder()
            .ownershipId(clozeInfo.getOwnershipId())
            .ownershipScope(clozeInfo.getOwnershipScope()).build());
    if (clozeOauth2Token == null || clozeOauth2Token.getSyncHistoryLead() == ClozeOauth2Token.SyncHistoryLeadEnum.NO) {
      log.info("History import is disabled for ownership: {}", clozeInfo.toOwnership());
      return;
    }

    User agent = userManager.getUserById(clozeInfo.getOwnershipId());
    if (agent == null) {
      log.info("Agent not found: {}", clozeInfo.getBindingAgentId());
      return;
    }

    SearchResultBo result;
    if (StringUtils.isBlank(clozeLeadInit.getScrollId())) {
      result = leadSearchManager.leadIdsByAssign(agent, DEFAULT_PAGE_SIZE);
    } else {
      result = leadSearchManager.scroll(clozeLeadInit.getScrollId());
    }

    if (result == null || CollectionUtils.isEmpty(result.getLeadDetailModels())) {
      log.info("No more leads to export for agent: {}", agent.getId());
      return;
    }

    result.getLeadDetailModels().forEach(leadDetail -> {
      long leadId = leadDetail.getLead().getLeadId();
      if(leadDetail.getEmailList().isEmpty()){
        return;
      }
      if(leadDetail.getEmailList().stream().noneMatch(e -> e.isPrimary() && e.getFamilyMemberId() == 0)){
        log.info("No primary email for lead: {}", leadId);
        return;
      }
      pushLeadToCloze(leadDetail, clozeInfo);
      sendExportNoteMessage(leadId, clozeInfo);
    });

    if (StringUtils.isNotBlank(result.getScrollId()) &&
        result.getLeadDetailModels().size() >= DEFAULT_PAGE_SIZE) {
      sendExportLeadPageMessage(clozeLeadInit, result.getScrollId());
    }
  }

  private void doExportLeadPage(ClozeLeadInit clozeLeadInit) {
    doExportLead(clozeLeadInit);
  }


  private void doExportNote(ClozeLeadInit clozeLeadInit) {
    if (clozeLeadInit.getLeadId() == null) {
      log.warn("LeadId is null for EXPORT_NOTE: {}", clozeLeadInit);
      return;
    }

    long leadId = clozeLeadInit.getLeadId();
    ClozeStateInfo clozeInfo = clozeLeadInit.getClozeInfo();

    log.info("Exporting notes for leadId: {}", leadId);

    try {
      List<KafkaTimeline> timelines = queryLeadTimelines(leadId);

      if (CollectionUtils.isEmpty(timelines)) {
        log.info("No timelines found for leadId: {}", leadId);
        return;
      }

      List<KafkaTimeline> syncableTimelines = timelines.stream()
          .filter(this::isSyncableTimeline)
          .collect(Collectors.toList());

      log.info("Found {} syncable timelines for leadId: {}", syncableTimelines.size(), leadId);

      syncTimelinesToCloze(syncableTimelines, clozeInfo);

    } catch (Exception e) {
      log.error("Error exporting notes for leadId: {}", leadId, e);
    }
  }

  private List<KafkaTimeline> queryLeadTimelines(long leadId) {
    // TODO: to be done;
    log.info("Querying timelines for leadId: {}", leadId);
    return new ArrayList<>();
  }


  private boolean isSyncableTimeline(KafkaTimeline timeline) {
    int timelineType = timeline.getTimelineType();

    return timelineType == 26 || timelineType == 6 || timelineType == 124 || // Email
           timelineType == 8 || timelineType == 25 || timelineType == 42 ||  // Call
           timelineType == 28 || timelineType == 43 || timelineType == 56 || // Text
           timelineType == 16; // Note
  }

  private void syncTimelinesToCloze(List<KafkaTimeline> timelines, ClozeStateInfo clozeInfo) {
    // TODO: to be done
  }

  private void pushLeadToCloze(LeadDetailBo leadDetail, ClozeStateInfo clozeInfo) {
    try {
      long leadId = leadDetail.getLead().getLeadId();
      log.info("Pushing lead {} to Cloze for ownership: {}", leadId, clozeInfo.toOwnership());

      String notifyMsg = JacksonUtils.toJson(Map.of("agentId", clozeInfo.getOwnershipId(),
              "pushType", "exportToCloze"));
      ClozeNotifyLog notifyLog = ClozeNotifyLog.builder()
              .type(ClozeNotifyLog.ClozeNotifyLogType.LEAD_CREATE.name())
              .ownershipId(clozeInfo.getOwnershipId())
              .ownershipScope(clozeInfo.getOwnershipScope().name())
              .notifyMsg(notifyMsg).build();
      clozeNotifyLogService.insert(notifyLog);
      clozePushService.pushPersonToCloze(leadDetail, notifyLog);
    } catch (Exception e) {
      log.error("Error pushing lead {} to Cloze", leadDetail.getLead().getLeadId(), e);
    }
  }

  private void sendExportNoteMessage(long leadId, ClozeStateInfo clozeInfo) {
    ClozeLeadInit exportNote = ClozeLeadInit.builder()
        .clozeInfo(clozeInfo)
        .type(ClozeLeadInit.SyncType.EXPORT_NOTE)
        .leadId(leadId)
        .build();

    KafkaUtil.msk(TOPIC_CLOZE_INIT_LEAD,
        clozeInfo.getBindingAgentId(), 
        JacksonUtils.toJson(exportNote));
    
    log.info("Sent EXPORT_NOTE message for leadId: {}", leadId);
  }

  private void sendExportLeadPageMessage(ClozeLeadInit clozeLeadInit, String scrollId) {
    clozeLeadInit.setScrollId(scrollId);
    clozeLeadInit.setType(ClozeLeadInit.SyncType.EXPORT_LEAD_PAGE);

    log.info("go next page:{}, scrollId: {}", clozeLeadInit.getCurPage(), scrollId);
    KafkaUtil.msk(TOPIC_CLOZE_INIT_LEAD, clozeLeadInit.getClozeInfo().getBindingAgentId(), clozeLeadInit);
  }

}

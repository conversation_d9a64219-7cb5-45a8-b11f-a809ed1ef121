package com.homethy.cloze.service;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.fub.FubConnect;
public interface ClozeOauth2TokenService {
    public FubConnect getConnect(OwnershipInfo ownershipInfo, User user);

    public void udpateSetting(ClozeOauth2Token token, User user);

    public int binding(String code, String state);

    public int disconnect(OwnershipInfo ownershipInfo);
    /**
     * Refresh token
     */
    ClozeOauth2Token refreshToken(OwnershipInfo ownershipInfo);

    ClozeOauth2Token getTokenByOwnership(OwnershipInfo ownershipInfo);
}

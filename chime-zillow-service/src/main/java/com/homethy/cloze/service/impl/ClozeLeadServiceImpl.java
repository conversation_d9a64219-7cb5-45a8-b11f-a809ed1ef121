package com.homethy.cloze.service.impl;

import com.homethy.cloze.dao.ClozeLeadDao;
import com.homethy.cloze.model.constant.ClozeLead;
import com.homethy.cloze.service.ClozeLeadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/***
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClozeLeadServiceImpl implements ClozeLeadService {

  @Autowired
  private ClozeLeadDao clozeLeadDao;

  @Override
  public int insert(ClozeLead clozeLead) {
    try {
      return clozeLeadDao.insert(clozeLead);
    } catch (Exception e) {
      log.error("Error inserting ClozeLead: {}", clozeLead, e);
      return 0;
    }
  }
}

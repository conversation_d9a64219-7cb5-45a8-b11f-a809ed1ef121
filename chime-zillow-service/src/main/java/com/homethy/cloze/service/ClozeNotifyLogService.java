package com.homethy.cloze.service;

import com.homethy.cloze.model.constant.ClozeNotifyLog;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClozeNotifyLogService {

  int insert(ClozeNotifyLog clozeNotifyLog);

  ClozeNotifyLog createSuccessLog(long loftyId, String clozeId, String type, String notifyMsg,
                                  String pushMsg, String ownershipScope, long ownershipId);

  ClozeNotifyLog createFailureLog(long loftyId, String type, String notifyMsg, String result,
                                  int status, String ownershipScope, long ownershipId);
}

package com.homethy.cloze.service.impl;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.cloze.client.ClozeClient;
import com.homethy.cloze.client.ClozeEmail;
import com.homethy.cloze.client.ClozePhone;
import com.homethy.cloze.client.ClozePerson;
import com.homethy.cloze.client.ClozeResult;
import com.homethy.cloze.dao.ClozeNotifyLogDao;
import com.homethy.cloze.model.constant.ClozeLead;
import com.homethy.cloze.model.constant.ClozeNotifyLog;
import com.homethy.cloze.service.ClozeLeadService;
import com.homethy.cloze.service.ClozeNotifyLogService;
import com.homethy.microservice.client.leadsearch.lead.LeadBo;
import com.homethy.microservice.client.leadsearch.lead.UserPhoneBo;

import java.util.ArrayList;
import java.util.List;
import com.homethy.cloze.service.ClozeOauth2TokenService;
import com.homethy.cloze.service.ClozePushService;
import com.homethy.microservice.client.leadsearch.lead.LeadDetailBo;
import com.homethy.microservice.client.leadsearch.lead.UserEmailBo;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ClozePushServiceImpl implements ClozePushService {

    @Autowired
    private ClozeClient clozeClient;

    @Autowired
    private ClozeOauth2TokenService clozeOauth2TokenService;

    @Autowired
    private ClozeLeadService clozeLeadService;

    @Autowired
    private ClozeNotifyLogService clozeNotifyLogService;

    @Autowired
    private ClozeNotifyLogDao clozeNotifyLogDao;

    @Override
    public void pushPersonToCloze(LeadDetailBo leadDetail, ClozeNotifyLog clozeNotifyLog) {
        OwnershipInfo ownershipInfo = OwnershipInfo.builder().ownershipId(clozeNotifyLog.getOwnershipId())
                .ownershipScope(OwnershipScope.valueOf(clozeNotifyLog.getOwnershipScope())).build();
        ClozeOauth2Token clozeOauth2Token = clozeOauth2TokenService.refreshToken(ownershipInfo);
        if(clozeOauth2Token == null){
            log.info("no cloze token, ownershipInfo: {}", ownershipInfo);
            return;
        }
        String primaryEmail = leadDetail.getEmailList().stream()
                .filter(e -> e.isPrimary() && e.getFamilyMemberId() == 0)
                .map(UserEmailBo::getEmail)
                .findFirst()
                .orElse(null);
        if(primaryEmail == null){
            return;
        }
        ClozeResult<ClozePerson> clozeResult = clozeClient.getPerson(clozeOauth2Token, primaryEmail);
        if(clozeResult.getErrorCode() == 0){
            ClozePerson existingPerson = clozeResult.getProfile();
            String clozeSyncKey = existingPerson.getSyncKey();

            ClozeLead clozeLead = ClozeLead.builder()
                .leadId(leadDetail.getLead().getLeadId())
                .clozeSyncKey(clozeSyncKey)
                .ownershipScope(clozeNotifyLog.getOwnershipScope())
                .ownershipId(clozeNotifyLog.getOwnershipId())
                .deleteFlag(false)
                .build();

            clozeLeadService.insert(clozeLead);

            // todo: update notify log

            log.info("Successfully linked existing Cloze person for leadId: {}, email: {}",
                leadDetail.getLead().getLeadId(), primaryEmail);
        }else{
            ClozePerson newPerson = convertLeadDetailToClozePerson(leadDetail);

            ClozeResult<ClozePerson> createResult = clozeClient.createPerson(clozeOauth2Token, newPerson);
            if(createResult.getErrorCode() == 0){
                ClozePerson createdPerson = clozeClient.getPerson(clozeOauth2Token, newPerson.getEmails().get(0).getValue()).getProfile();
                String clozeSyncKey = createdPerson.getSyncKey();

                ClozeLead clozeLead = ClozeLead.builder()
                    .leadId(leadDetail.getLead().getLeadId())
                    .clozeSyncKey(clozeSyncKey)
                    .ownershipScope(clozeNotifyLog.getOwnershipScope())
                    .ownershipId(clozeNotifyLog.getOwnershipId())
                    .deleteFlag(false)
                    .build();

                clozeLeadService.insert(clozeLead);
                // todo: update notify log
                log.info("Successfully created new Cloze person for leadId: {}, email: {}",
                    leadDetail.getLead().getLeadId(), primaryEmail);
            }else{
                // todo: update notify log
                log.error("Failed to create Cloze person for leadId: {}",
                    leadDetail.getLead().getLeadId());
            }
        }
    }

    private ClozePerson convertLeadDetailToClozePerson(LeadDetailBo leadDetail) {
        ClozePerson clozePerson = new ClozePerson();

        if (leadDetail.getLead() != null) {
            LeadBo lead = leadDetail.getLead();
            clozePerson.setFirst(lead.getFirstName());
            clozePerson.setLast(lead.getLastName());
        }

        //set emails
        List<UserEmailBo> emailList = leadDetail.getEmailList();
        List<ClozeEmail> clozeEmails = new ArrayList<>();
        if (emailList != null) {
            emailList.forEach(email -> {
                ClozeEmail clozeEmail = new ClozeEmail();
                clozeEmail.setValue(email.getEmail());
                clozeEmails.add(clozeEmail);
            });
            clozePerson.setEmails(clozeEmails);
        }

        //set phones
        List<UserPhoneBo> phoneList = leadDetail.getPhoneList();
        List<ClozePhone> clozePhones = new ArrayList<>();
        if (phoneList != null) {
            phoneList.forEach(phone -> {
                ClozePhone clozePhone = new ClozePhone();
                clozePhone.setValue(phone.getPhone());
                clozePhones.add(clozePhone);
            });
            clozePerson.setPhones(clozePhones);
        }

        // todo: tags assign segment property

        return clozePerson;
    }

}

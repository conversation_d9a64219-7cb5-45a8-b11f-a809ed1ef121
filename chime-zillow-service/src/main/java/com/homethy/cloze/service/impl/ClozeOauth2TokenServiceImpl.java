package com.homethy.cloze.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.homethy.cloze.ClozeOauth2Token;
import com.homethy.cloze.client.ClozeClient;
import com.homethy.cloze.client.ClozeProfile;
import com.homethy.cloze.client.ClozeResult;
import com.homethy.cloze.client.TokenResult;
import com.homethy.cloze.dao.ClozeOauth2TokenDao;
import com.homethy.cloze.service.ClozeOauth2TokenService;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.configuration.ZillowConfig;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubConnect;
import com.homethy.zillow.util.JwtUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ClozeOauth2TokenServiceImpl implements ClozeOauth2TokenService {

    @Autowired
    private ClozeOauth2TokenDao clozeOauth2TokenDao;
    @Autowired
    private ClozeClient clozeClient;

    public FubConnect getConnect(OwnershipInfo ownershipInfo, User user) {
        if (ownershipInfo == null || user == null) {
            log.warn("[getConnect] ownershipInfo or user is null, ownershipInfo: {}, user: {}", ownershipInfo, user);
            return new FubConnect();
        }
        log.info("[getConnect] called with ownershipInfo={}, operUserId={}", ownershipInfo, user.getId());
        ClozeOauth2Token token = getTokenByOwnership(ownershipInfo);
        FubConnect fubConnect = new FubConnect();
        if (token != null) {
            fubConnect.setConnect(true);
            return fubConnect;
        }
        String clientId = ZillowConfig.getProperties("chime-zillow.cloze.clientId", "");
        String redirectUrl = ZillowConfig.getProperties("chime-zillow.cloze.redirect_url", "");
        String connectUrl = String.format("https://www.cloze.com/oauth/authorize"
                + "/oauth/authorize?response_type=auth_code&client_id=%s&redirect_uri=%s&state=%s"
                + "&prompt=login", clientId, redirectUrl,
                getState(ownershipInfo, user));
        fubConnect.setUrl(connectUrl);
        return fubConnect;
    }

    public String getState(OwnershipInfo ownershipInfo, User operUser) {
        if (ownershipInfo == null || ownershipInfo.getOwnershipScope() == null
                || ownershipInfo.getOwnershipId() <= 0 || operUser == null) {
            log.warn("[getState] ownership info error, ownershipInfo: {}, operId: {}",
                    ownershipInfo, operUser);
            return null;
        }
        Map<String, Object> claims = new HashMap<>();
        claims.put("ownershipId", ownershipInfo.getOwnershipId());
        claims.put("ownershipScope", ownershipInfo.getOwnershipScope().name());
        claims.put("operatorId", operUser.getId());
        return JwtUtils.generateToken(claims,
                ZillowConfig.getProperties("chime-zillow.cloze.secret", ""),
                TimeUnit.DAYS.toSeconds(5));
    }

    public ClozeOauth2Token parserState(String state) {
        try {
            boolean verify = JwtUtils.verify(state, ZillowConfig.getProperties("chime-zillow.cloze.secret", ""));
            if (!verify) {
                log.warn("state is invalid, state: {} , exception: ", state);
                return null;
            }
            Map<String, Object> claims = JwtUtils.parsePayload(state);
            ClozeOauth2Token token = ClozeOauth2Token.builder()
                    .ownershipId((Long) claims.get("ownershipId"))
                    .ownershipScope(OwnershipScope.valueOf((String) claims.get("ownershipScope")))
                    .operatorId((Long) claims.get("operatorId"))
                    .build();
            return token;
        } catch (Exception e) {
            log.warn("state is invalid, state: {} , exception: ", state, e);
        }
        return null;
    }

    public void udpateSetting(ClozeOauth2Token token, User user) {
        if (token == null || user == null) {
            return;
        }
        ClozeOauth2Token dbToken = clozeOauth2TokenDao.getTokenByOwnershipWithDelete(token.getOwnershipId(),
                token.getOwnershipScope());
        if (dbToken != null) {
            dbToken.setSyncHistoryLead(token.getSyncHistoryLead());
            dbToken.setOperatorId(user.getId());
            dbToken.setTeamId(user.getTeamId());
            dbToken.setApiKey(token.getApiKey());
            if (StringUtils.isNotEmpty(token.getApiKey())) {
                token.setDeleteFlag(false);
            }
            clozeOauth2TokenDao.updateToken(dbToken);
            return;
        }
        token.setDeleteFlag(true);
        token.setOperatorId(user.getId());
        token.setTeamId(user.getTeamId());
        if (StringUtils.isNotEmpty(token.getApiKey())) {
            token.setDeleteFlag(false);
        }
        clozeOauth2TokenDao.insertToken(token);
    }

    public int binding(String code, String state) {
        ClozeOauth2Token stateInfo = parserState(state);
        if (stateInfo == null) {
            return 0;
        }
        log.info("authorizationCode: {}", JacksonUtils.toJson(stateInfo));
        ClozeOauth2Token dbToken = clozeOauth2TokenDao.getTokenByOwnershipWithDelete(stateInfo.getOwnershipId(),
                stateInfo.getOwnershipScope());
        if (dbToken == null) {
            dbToken = stateInfo;
            dbToken.setDeleteFlag(false);
        } else {
            dbToken.setOperatorId(stateInfo.getOperatorId());
            dbToken.setDeleteFlag(false);
        }
        TokenResult authorizationCode = clozeClient.getToken(code);
        dbToken.setAccessToken(authorizationCode.getAccessToken());
        dbToken.setRefreshToken(authorizationCode.getRefreshToken());
        dbToken.setExpiresIn(authorizationCode.getExpiresIn());
        dbToken.setRefreshTime((new Date()));
        ClozeResult<ClozeProfile> userInfo = clozeClient.getUserProfile(stateInfo);
        if (userInfo == null || userInfo.getErrorCode() != 0) {
            log.warn("get user info error, errorCode: {}", userInfo);
            return 0;
        }
        dbToken.setClozeId(userInfo.getProfile().getKey());
        dbToken.setClozeEmail(userInfo.getProfile().getEmail());
        ClozeOauth2Token clozeToken = clozeOauth2TokenDao.getTokenByClozeId(dbToken.getClozeId());
        int resultCode = 1;
        if (clozeToken != null && clozeToken.getOwnershipId() != dbToken.getOwnershipId()
                && clozeToken.getOwnershipScope() != dbToken.getOwnershipScope()) {
            disconnect(OwnershipInfo.builder()
                    .ownershipId(clozeToken.getOwnershipId())
                    .ownershipScope(clozeToken.getOwnershipScope())
                    .build());
            log.info("Squeeze disconnect, Ownership: {}", clozeToken);
            resultCode = 2;
        }
        if (dbToken.getId() > 0) {
            clozeOauth2TokenDao.updateToken(dbToken);
        } else {
            clozeOauth2TokenDao.insertToken(dbToken);
        }
        return resultCode;
    }

    public ClozeOauth2Token getTokenByOwnership(OwnershipInfo ownershipInfo) {
        return getTokenByOwnership(ownershipInfo.getOwnershipId(), ownershipInfo.getOwnershipScope());
    }

    @Override
    public ClozeOauth2Token getTokenByOwnership(long ownershipId, OwnershipScope ownershipScope) {
        if (ownershipScope == null) {
            return null;
        }
        return clozeOauth2TokenDao.getTokenByOwnership(ownershipId, ownershipScope);
    }

    public int disconnect(OwnershipInfo ownershipInfo) {
        if (ownershipInfo == null) {
            return 0;
        }
        return deleteByOwnership(ownershipInfo);
    }

    public int deleteByOwnership(OwnershipInfo ownershipInfo) {
        if (ownershipInfo == null) {
            return 0;
        }
        return clozeOauth2TokenDao.deleteByOwnership(ownershipInfo.getOwnershipId(), ownershipInfo.getOwnershipScope());
    }

    @Override
    public ClozeOauth2Token refreshToken(OwnershipInfo ownershipInfo) {
        if (ownershipInfo == null) {
            return null;
        }
        // Should call Cloze API to refresh token here
        // Temporarily return original token, actual implementation needs to follow
        // Cloze API documentation
        log.info("Refreshing token for Cloze ID: {}", ownershipInfo);
        ClozeOauth2Token token = getTokenByOwnership(ownershipInfo);
        if (token == null) {
            return null;
        }
        updateAccessToken(token);
        return token;
    }

    public void updateAccessToken(ClozeOauth2Token token) {
        if (token.getExpiresIn() > System.currentTimeMillis() + 10000) {
            return;
        }
        // Update refresh time
        token.setRefreshTime(new Date());
        TokenResult tokenResult = clozeClient.refreshToken(token);
        token.setAccessToken(tokenResult.getAccessToken());
        token.setRefreshToken(tokenResult.getRefreshToken());
        token.setExpiresIn(tokenResult.getExpiresIn());
        token.setRefreshTime(new Date());
        clozeOauth2TokenDao.updateAccessToken(token);
    }

}

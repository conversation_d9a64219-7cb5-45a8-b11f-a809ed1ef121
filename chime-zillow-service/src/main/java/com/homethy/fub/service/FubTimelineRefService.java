package com.homethy.fub.service;

import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.fub.FubStatusBo;
import com.homethy.zillow.model.po.FubTimelineStatusQuery;

import java.util.Map;

public interface FubTimelineRefService {

  Map<Long, FubStatusBo> getFubStatus(Map<Long, FubTimelineStatusQuery> queries, long teamId);

  void clearTimelineRef(OwnershipInfo ownershipInfo);
}

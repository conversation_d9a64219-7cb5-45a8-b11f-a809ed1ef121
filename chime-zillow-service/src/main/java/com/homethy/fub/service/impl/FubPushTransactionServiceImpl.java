package com.homethy.fub.service.impl;

import com.homethy.fub.dao.FubTrRefDao;
import com.homethy.fub.service.FubNotifyLogService;
import com.homethy.fub.service.FubPushTransactionService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.BO.Commission;
import com.homethy.microservice.BO.Commission.PayType;
import com.homethy.microservice.BO.Commissions;
import com.homethy.microservice.BO.LeadTransactionDetailBO;
import com.homethy.microservice.BO.TransactionPipelineBo;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.transaction.TransactionPipelineClient;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.i18n.util.MsgException;
import com.homethy.zillow.client.FubClientManager;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubDeal;
import com.homethy.zillow.model.constant.fub.FubDealRequest;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;
import com.homethy.zillow.model.constant.fub.FubPipeline;
import com.homethy.zillow.model.constant.fub.FubPipelinesResponse;
import com.homethy.zillow.model.constant.fub.FubStage;
import com.homethy.zillow.model.constant.fub.FubTrRef;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FubPushTransactionServiceImpl implements FubPushTransactionService {

  private static final String IGNORE_STATUS = "Cancelled";

  @Autowired
  FubClientManager fubClientManager;
  @Autowired
  FubTokenService fubTokenService;
  @Autowired
  FubTrRefDao fubTrRefDao;
  @Autowired
  FubNotifyLogService fubNotifyLogService;
  @Autowired
  TransactionPipelineClient transactionPipelineClient;
  @Autowired
  UserManager userManager;

  @Override
  public void handCreateTransaction(LeadTransactionDetailBO leadTransaction, FubLead fubLead) {
    FubNotifyLog notifyLog = saveNotifyLog(leadTransaction);
    try {
      String callToken = getCallToken(fubLead);
      if (callToken == null) {
        log.info("create fub deal failed, token is null, leadTransactionId: {}",
                leadTransaction.getId());
        return;
      }
      FubDealRequest fubDealRequest = convert(leadTransaction, callToken, fubLead);
      FubDeal createdFubDeal = fubClientManager.createFubDeal(fubDealRequest, callToken);
      saveTrRef(createdFubDeal, leadTransaction, fubLead);
      log.info("create fub deal success, leadTransactionId: {}, fubDealId: {}",
          leadTransaction.getId(), createdFubDeal.getId());
    } catch (MsgException e) {
      if (e.getKey() == ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED) {
        fubNotifyLogService.saveRateLimit(notifyLog.getId());
      } else {
        fubNotifyLogService.saveFailLog(notifyLog.getId(), (ZillowErrorCodeEnum) e.getKey());
      }
    } catch (Exception e) {
      fubNotifyLogService.saveFailLog(notifyLog.getId(), ZillowErrorCodeEnum.UNKNOWN_ERROR);
      log.warn("push create transaction to fub fail, exception: ", e);
    }
  }

  private String getCallToken(FubLead fubLead) {
    return fubTokenService.getCallToken(
        new OwnershipInfo(fubLead.getOwnershipId(), fubLead.getOwnershipScope()));
  }

  private FubNotifyLog saveNotifyLog(LeadTransactionDetailBO leadTransaction) {
    FubNotifyLog notifyLog = FubNotifyLog.builder()
        .loftyId(leadTransaction.getId())
        .type(FubNotifyLog.FubNotifyLogType.TRANSACTION_CREATE.name())
        .notifyMsg(JacksonUtils.toJson(leadTransaction)).build();
    fubNotifyLogService.insert(notifyLog);
    return notifyLog;
  }

  @Override
  public void handUpdateTransaction(FubTrRef fubTrRef, LeadTransactionDetailBO leadTransaction, FubLead fubLead) {
    try {
      if (!checkCanUpdateTransaction(leadTransaction)) {
        log.info("Failed transaction update cause the time limit, leadTransactionId: {}",
            leadTransaction.getId());
        return;
      }
      String callToken = getCallToken(fubLead);
      if (callToken == null) {
        log.info("update fub deal failed, token is null, leadTransactionId: {}",
            leadTransaction.getId());
        return;
      }
      FubDealRequest fubDealRequest = convert(leadTransaction, callToken, fubLead);
      FubDeal fubDeal = fubClientManager.updateFubDeal(fubTrRef.getDealId(), fubDealRequest,
          callToken);
      log.info("update fub deal success, leadTransactionId: {}, fubDealId: {}",
          leadTransaction.getId(), fubDeal.getId());
    } catch (MsgException e) {
      log.warn("push update transaction to fub fail, exception: ", e);
    }
  }

  public boolean checkCanUpdateTransaction(LeadTransactionDetailBO leadTransaction) {
    long trUpdateTimeFromCache = fubClientManager.getTrUpdateTimeFromCache(leadTransaction.getId());
    if (trUpdateTimeFromCache > 0) {
      Duration duration = Duration.between(
          Instant.ofEpochMilli(trUpdateTimeFromCache),
          leadTransaction.getUpdateTime().toInstant());
      return duration.getSeconds() > 10;
    }
    return true;
  }

  @Override
  public void handDeleteTransaction(FubTrRef fubTrRef, LeadTransactionDetailBO leadTransaction, FubLead fubLead) {
    String callToken = getCallToken(fubLead);
    if (callToken == null) {
      log.info("delete fub deal failed, token is null, leadTransactionId: {}",
              leadTransaction.getId());
      return;
    }
    fubClientManager.deleteFubDeal(fubTrRef.getDealId(), callToken);
    log.info("delete fub deal success, leadTransactionId: {}, fubDealId: {}",
        leadTransaction.getId(), fubTrRef.getDealId());
  }

  private FubDealRequest convert(LeadTransactionDetailBO leadTransaction, String token, FubLead fubLead) {
    FubDealRequest request = new FubDealRequest();
    request.setName(leadTransaction.getTransactionName());
    request.setStageId(getDealStageId(leadTransaction, token));
    request.setPeopleIds(Collections.singletonList(fubLead.getPeopleId()));
    long fubUserId = getFubUserId(token, leadTransaction.getOwnerId());
    request.setUserIds(Collections.singletonList(fubUserId));
    request.setPrice(leadTransaction.getHomePrice());
    request.setCommissionValue(leadTransaction.getGci());
    request.setProjectedCloseDate(leadTransaction.getCloseTime());
    handleCommission(leadTransaction, request);
    return request;
  }

  /**
   * Handle commission assignment for FubDealRequest
   * @param leadTransaction transaction details
   * @param request FubDealRequest to set commission values
   */
  public void handleCommission(LeadTransactionDetailBO leadTransaction, FubDealRequest request) {
    Commissions commissions = leadTransaction.getCommissions();
    if (commissions == null) {
      log.info("Commissions data is null for transaction: {}", leadTransaction.getId());
      return;
    }

    List<Commission> split = commissions.getSplit();
    if (CollectionUtils.isEmpty(split)) {
      log.info("split commission data is empty for transaction: {}", leadTransaction.getId());
      return;
    }

    long ownerId = leadTransaction.getOwnerId();
    boolean agentTag = false;
    boolean teamTag = false;
    for (Commission commission : split) {
      if (commission == null) {
        log.info("Invalid commission data found in transaction: {}", leadTransaction.getId());
        continue;
      }
      try {
        if (PayType.AGENT.equals(commission.getPayType()) && commission.getPayTo() == ownerId) {
          BigDecimal agentCommission = convertToBigDecimal(commission.getValue());
          request.setAgentCommission(agentCommission);
          agentTag = true;
          log.info("Set agent commission: {} for transaction: {}", agentCommission,
              leadTransaction.getId());
        } else if (PayType.TEAM.equals(commission.getPayType())) {
          BigDecimal teamCommission = convertToBigDecimal(commission.getValue());
          request.setTeamCommission(teamCommission);
          teamTag = true;
          log.info("Set team commission: {} for transaction: {}", teamCommission,
              leadTransaction.getId());
        }
      } catch (Exception e) {
        log.warn("Error processing commission for transaction: {}, commission: {}",
            leadTransaction.getId(), commission, e);
      }
    }
    if (!agentTag) {
      request.setAgentCommission(BigDecimal.ZERO);
    }
    if (!teamTag) {
      request.setTeamCommission(BigDecimal.ZERO);
    }
  }

  /**
   * Convert long value to BigDecimal with proper scaling
   * @param value long value to convert
   * @return BigDecimal representation
   */
  private BigDecimal convertToBigDecimal(long value) {
    // Convert back to dollars by dividing by 100
    return BigDecimal.valueOf(Math.abs(value))
        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
  }

  private long getFubUserId(String token, long loftyUserId) {
    User userById = userManager.getUserById(loftyUserId);
    if (Objects.isNull(userById)) {
      log.info("unknown userId: {}", loftyUserId);
      throw ZillowErrorCodeEnum.UNKNOWN_USER.exp();
    }
    FubUserInfo userInfo = fubClientManager.getUserInfo(token, userById.getAccount());
    if (Objects.isNull(userInfo)) {
      log.info("unknown userId: {}, account: {}", loftyUserId, userById.getAccount());
      throw ZillowErrorCodeEnum.UNKNOWN_USER.exp();
    }
    return userInfo.getId();
  }

  private long getDealStageId(LeadTransactionDetailBO leadTransaction, String token) {
    TransactionPipelineBo pipelineByStatus = transactionPipelineClient.getPipelineByStatus(leadTransaction.getTeamId(),
        String.valueOf(leadTransaction.getStatus()));
    if (Objects.isNull(pipelineByStatus)) {
      return 0L;
    }
    if (IGNORE_STATUS.equalsIgnoreCase(pipelineByStatus.getName())) {
      throw ZillowErrorCodeEnum.FUB_TR_IGNORE_STATUS.exp();
    }
    FubPipelinesResponse pipelinesResponse = fubClientManager.getPipelines(token);
    if (Objects.isNull(pipelinesResponse)) {
      return 0L;
    }
    List<FubPipeline> pipelines = pipelinesResponse.getPipelines();
    if (CollectionUtils.isEmpty(pipelines)) {
      return 0L;
    }
    String fubDealType = getDealType(leadTransaction);
    Optional<FubPipeline> first = pipelines.stream()
        .filter(pipeline -> pipeline.getName().equalsIgnoreCase(fubDealType))
        .findFirst();
    if (first.isEmpty()) {
      return 0L;
    }
    List<FubStage> stages = first.get().getStages();
    if (CollectionUtils.isEmpty(stages)) {
      return 0L;
    }
    return getFubDealStatus(pipelineByStatus.getName(), stages);
  }

  private long getFubDealStatus(String loftyStatusName, List<FubStage> fubStages) {
    Map<String, Long> collect = fubStages.stream().collect(Collectors.toMap(FubStage::getName, FubStage::getId));
    if (collect.containsKey(loftyStatusName)) {
      return collect.get(loftyStatusName);
    }
    return fubStages.stream().findFirst().map(FubStage::getId).orElse(0L);
  }

  private String getDealType(LeadTransactionDetailBO leadTransaction) {
    return switch (leadTransaction.getType()) {
      case 1 -> "Buyers";
      case 2 -> "Sellers";
      default -> "Other";
    };
  }

  private void saveTrRef(FubDeal createdFubDeal,
      LeadTransactionDetailBO leadTransaction, FubLead fubLead) {
    FubTrRef trRef = FubTrRef.builder()
        .trId(leadTransaction.getId())
        .dealId(createdFubDeal.getId())
        .leadId(leadTransaction.getLeadId())
        .teamId(leadTransaction.getTeamId())
        .ownershipScope(fubLead.getOwnershipScope().name())
        .ownershipId(fubLead.getOwnershipId())
        .fubStatus(FubLead.FubStatusEnum.TO_FUB.getCode())
        .fubTeamId(fubLead.getFubTeamId())
        .build();
    fubTrRefDao.insert(trRef);
  }
}

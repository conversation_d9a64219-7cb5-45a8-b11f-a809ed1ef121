package com.homethy.fub.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.persistence.UserTag;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.po.KafkaRecord;

import org.springframework.stereotype.Service;

import java.util.List;
public interface FubTagService {
  void handleTag(FubImportMsg record);

  List<UserTag> addUserTags(List<String> tagNameList, User user, boolean userPermission);
}

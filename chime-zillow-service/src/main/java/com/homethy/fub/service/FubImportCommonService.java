package com.homethy.fub.service;

import static com.homethy.web.crm.util.BizException.assertTrue;
import static com.homethy.zillow.model.constant.KafkaTopic.TOPIC_FUB_INIT_LEAD;

import com.homethy.util.jackson.JacksonUtils;
import com.homethy.web.crm.util.BizException;
import com.homethy.zillow.configuration.ZillowConfig;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.constant.fub.FubToken;
import com.homethy.zillow.model.po.FubLeadInit;
import com.homethy.zillow.model.po.FubLeadInit.SyncType;
import com.homethy.zillow.service.KafkaService;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public interface FubImportCommonService {

  default long getRetryAfterMills() {
    return Optional.ofNullable(
            ZillowConfig.getProperties("chime-zillow.fub.importLead.retry_after_mills", "5000"))
        .filter(NumberUtils::isCreatable)
        .map(NumberUtils::toLong)
        .orElse(5000L);
  }

  default long getNextRetryTime() {
    return System.currentTimeMillis() + getRetryAfterMills();
  }

  default FubToken getFubToken(FubStateInfo fubInfo) throws BizException {
    return getFubToken(fubInfo, false);
  }

  default FubToken getFubToken(FubStateInfo fubInfo, boolean notCheckExist) throws BizException {
    FubToken token = getFubTokenService().getTokenWithRefresh(fubInfo.toOwnership());
    if (notCheckExist && token == null) {
      return null;
    }

    assertTrue(token != null && StringUtils.isNotBlank(token.getCallToken()), "token is null");
    assertTrue(token.getTeamId() == fubInfo.getTeamId(),
        "teamId wrong:" + token.getTeamId() + "" + fubInfo.getTeamId());
    assertTrue(token.getBindingAgentId() == fubInfo.getBindingAgentId(),
        "bindingAgentId is not equal to operUserId");
    return token;
  }

  FubTokenService getFubTokenService();

  default void nextStepForImportHis(FubStateInfo fubInfo) {
    FubLeadInit init = FubLeadInit.builder()
        .fubInfo(fubInfo)
        .type(SyncType.IMPORT_HIS)
        .build();
    getKafkaService().sendMskMessage(TOPIC_FUB_INIT_LEAD,
        fubInfo.getBindingAgentId(), JacksonUtils.toJson(init));
  }

  KafkaService getKafkaService();

}

package com.homethy.fub.service.impl;

import static com.homethy.web.crm.util.BizException.assertTrue;
import static com.homethy.zillow.model.constant.KafkaTopic.TOPIC_FUB_LEAD_IMPORT;

import com.homethy.fub.service.FubPeopleImportSendService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.web.crm.util.BizException;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.fub.FubSimplePeople;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.po.FubLeadImportTask;
import com.homethy.zillow.service.KafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FubPeopleImportSendServiceImpl implements FubPeopleImportSendService {

  @Autowired
  private KafkaService kafkaService;

  @Override
  public boolean addImportTask(FubStateInfo stateInfo, long peopleId,
      FubImportTypeEnum importTypeEnum) {
    try {
      assertTrue(stateInfo != null, "stateInfo is null");
      assertTrue(stateInfo.getOwnershipId() > 0,
          "ownershipId is invalid:" + stateInfo.getOwnershipId());
      assertTrue(stateInfo.getOwnershipScope() != null,
          "ownershipScope is invalid:" + stateInfo.getOwnershipScope());
      assertTrue(stateInfo.getTeamId() > 0, "teamId is invalid:" + stateInfo.getTeamId());
      assertTrue(stateInfo.getFubTeamId() > 0,
          "fubTeamId is invalid:" + stateInfo.getFubTeamId());
      assertTrue(stateInfo.getBindingAgentId() > 0,
          "bindingAgentId is invalid:" + stateInfo.getBindingAgentId());
      assertTrue(peopleId > 0, "peopleId is invalid:" + peopleId);
      assertTrue(importTypeEnum != null, "importTypeEnum is null");

      FubLeadImportTask task = new FubLeadImportTask();
      task.setFubInfo(stateInfo);
      task.setFubPeople(FubSimplePeople.builder().id(peopleId).build());
      task.setImportTypeEnum(importTypeEnum);

      kafkaService.sendMskMessage(TOPIC_FUB_LEAD_IMPORT,
          stateInfo.getBindingAgentId(),
          JacksonUtils.toJson(task));
      return true;
    } catch (BizException e) {
      log.warn("run biz:{},{}", e.getCode(), e.getMsg());
      e.printStackTrace();
    } catch (Exception e) {
      log.warn("run wrong:{}", e.getMessage());
    }
    return false;
  }

}

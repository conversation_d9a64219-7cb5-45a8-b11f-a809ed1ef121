package com.homethy.fub.service;

import com.homethy.zillow.model.po.KafkaRecord;
import org.slf4j.Logger;

public interface KafkaLogAbility {

  default void logKafkaInfo(KafkaRecord record, long start) {
    getLog().info("HDLKFK|{}|{}|{}|{}-{}|{}|{}", this.getClass().getSimpleName(),
        record.getTopic(),
        record.getKey(),
        record.getPartition(),
        record.getOffset(),
        System.currentTimeMillis() - start,
        record.isMsk() ? "msk" : "");
  }

  Logger getLog();

}

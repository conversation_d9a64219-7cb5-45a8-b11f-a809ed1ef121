package com.homethy.fub.service;

import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubImportLog;

public interface FubImportLogService {

  long insert(FubImportLog fubImportLog);

  void retryAddOne(long id);

  void saveFailLog(long notifyLogId, ZillowErrorCodeEnum zillowErrorCodeEnum);

  void saveHis(long fubAgentId, long fubTeamId, long fubPeopleId);

  int updateById(FubImportLog fubImportLog);

  int updateStatusById(FubImportLog fubImportLog);
}

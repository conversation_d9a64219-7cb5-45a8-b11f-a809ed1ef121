package com.homethy.fub.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubImportSetting;
import com.homethy.zillow.model.constant.fub.FubSettingChangeInfo;
import com.homethy.zillow.model.constant.fub.FubSettingType;

import java.util.List;

public interface FubImportSettingService {

  FubImportSetting getImportSetting(OwnershipScope ownershipScope, long ownershipId, User operUser);
  void updateImportSetting(FubSettingType type, FubSettingChangeInfo info);
}

package com.homethy.fub.service;

import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;

public interface FubNotifyLogService {

  void saveFailLog(long notifyLogId, ZillowErrorCodeEnum zillowErrorCodeEnum);

  void saveFailLog(long notifyLogId, ZillowErrorCodeEnum zillowErrorCodeEnum,
                   Object... formatValue);

  void saveRateLimit(long notifyLogId);

  void saveLeadNotFind(long notifyLogId);

  void saveSuccessLog(long notifyLogId, long fubId);

  void insert(FubNotifyLog fubNotifyLog);

  void saveOwnership(long notifyLogId, OwnershipInfo ownershipInfo);
}

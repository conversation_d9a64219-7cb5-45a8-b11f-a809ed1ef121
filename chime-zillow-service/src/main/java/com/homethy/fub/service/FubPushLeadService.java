package com.homethy.fub.service;

import com.homethy.microservice.client.leadsearch.lead.LeadPropertyBo;
import com.homethy.microservice.client.leadsearch.lead.UserEmailBo;
import com.homethy.microservice.client.leadsearch.lead.UserLeadTagBo;
import com.homethy.microservice.client.leadsearch.lead.UserPhoneBo;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;
import com.homethy.zillow.model.po.FubPeopleInfo;
import com.homethy.zillow.model.po.FubPushInfo;
import com.homethy.zillow.model.po.KafkaRecord;

import java.util.List;

public interface FubPushLeadService {

  void pushNewLeads(List<KafkaRecord> records);
  Boolean handle(FubPushInfo pushInfo, FubNotifyLog notifyLog);
  void setFubPeopleEmail(FubPeopleInfo fubPeopleInfo, List<UserEmailBo> leadEmailBos);
  void setFubPeoplePhone(FubPeopleInfo fubPeopleInfo, List<UserPhoneBo> leadPhones);
  void setAddress(FubPeopleInfo fubPeopleInfo, List<LeadPropertyBo> propertyBos);
  void setTags(FubPeopleInfo fubPeopleInfo, List<UserLeadTagBo> tags);
}

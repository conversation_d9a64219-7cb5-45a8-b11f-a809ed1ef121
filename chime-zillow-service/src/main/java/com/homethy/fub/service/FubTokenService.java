package com.homethy.fub.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubConnect;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.constant.fub.FubToken;

import java.util.List;

public interface FubTokenService {

  int binding(String code, String state);
  FubConnect getConnect(OwnershipInfo ownershipInfo, User user);
  boolean disconnect(OwnershipInfo ownershipInfo);
  String getState(OwnershipInfo ownershipInfo, long operUserId, long teamId);
  FubStateInfo parserState(String state);
  FubToken getTokenWithoutCallToken(OwnershipInfo ownershipInfo);
  FubToken getTokenWithRefresh(OwnershipInfo ownershipInfo);
  boolean checkTokenExist(OwnershipInfo ownershipInfo);
  String getCallToken(OwnershipInfo ownershipInfo);
  List<Long> checkTokens(List<Long> ownershipIds, OwnershipScope ownershipScope);
  FubToken getTokenWithoutCallToken(long fubAgentId, long fubTeamId);
}

package com.homethy.fub.service;

import com.homethy.microservice.client.model.LeadBo;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.TimeFrameEnum;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.po.FubPeopleInfo;
import com.homethy.zillow.model.po.KafkaTimeline;
import java.util.List;
import java.util.Map;

public interface FubLeadSyncService {

  void assignLead(List<KafkaTimeline> timelineList, Map<Long, FubLead> leadMap);

  void updateAssign(FubLead fubLead, FubPeopleInfo fubPeopleInfo, long assignToId);

  void updateFubLead(KafkaTimeline timeline, FubLead fubLead, FubPeopleInfo fubPeopleInfo);

  String updatePerson(String token, long peopleId, FubPeopleInfo fubPeopleInfo);

  void communicationLogs(KafkaTimeline timeline, FubLead fubLead,
      CommunicationEnum communication);

  String getToken(FubLead fubLead);

  List<FubLead> getFubLeads(List<Long> leadIds);

  FubLead getFubLead(long leadId);

  boolean mergeLeads(List<Long> leadIds);

  void pushLeadToPlatform(KafkaTimeline timeline, FubLead platformLead);

  void setTimeFrame(OwnershipInfo fubLeadOwnership, FubPeopleInfo fubPeopleInfo,
                    TimeFrameEnum timeFrameEnum);

  void addRelationship(LeadBo leadBo, FubLead fubLead);

  /**
   * Re-push note information to FUB for the last 5 minutes
   * 
   * @return true if the operation completed successfully, false otherwise
   */
  boolean rePushNotesToFub();
}

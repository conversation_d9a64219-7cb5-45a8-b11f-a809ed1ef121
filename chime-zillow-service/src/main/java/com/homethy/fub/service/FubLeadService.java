package com.homethy.fub.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;
import com.homethy.zillow.model.constant.fub.FubPeople;
import com.homethy.zillow.model.constant.fub.FubStageRequest;
import com.homethy.zillow.model.po.ZillowStatus;

import java.util.List;
import java.util.Map;

public interface FubLeadService {
  List<Long> checkFubLead(List<Long> leadIds);

  FubLead getFubLead(long leadId);
  FubLead getLatestFubLead(long leadId);

  void addFubLead(FubLead fubLead);

  FubLead addFubLead(FubNotifyLog fubNotifyLog, long teamId, FubPeople fubPeople, long fubTeamId);

  void updateFubStage(FubStageRequest fubStageRequest);

  Map<String, Object> getFubStageList(long leadId, int page, int size, User user);

  void clearFubLead(OwnershipInfo ownershipInfo);

  FubLead getFubLeadByMapping(long fubTeamId, long peopleId, long teamId);

  List<FubLead> getFubLeads(long fubTeamId, List<Long> fubPeopleIds, long teamId);

  void handleLead(FubImportMsg record);

  ZillowStatus copyAndSupplementFub(long leadId, ZillowStatus orgZillowStatus);
}

package com.homethy.fub.service.impl;

import static com.homethy.microservice.client.model.LeadPropertyVoBo.LabelEnum.HIGH_INTERST;
import static com.homethy.microservice.client.model.LeadPropertyVoBo.LabelEnum.HOME;
import static com.homethy.web.crm.util.BizException.assertTrue;
import static com.homethy.zillow.client.NotificationManager.FUB_IMPORT_CHANNEL;
import static com.homethy.zillow.model.constant.fub.SyncStatus.FAIL;
import static com.homethy.zillow.model.constant.fub.SyncStatus.FUB_LIMIT_EXCEEDED;
import static com.homethy.zillow.model.constant.fub.SyncStatus.INIT;
import static com.homethy.zillow.model.constant.fub.SyncStatus.SUCCESS;

import com.google.common.collect.Lists;
import com.homethy.fub.dao.FubImportSettingDao;
import com.homethy.fub.service.FubExistLeadImportService;
import com.homethy.fub.service.FubImportCommonService;
import com.homethy.fub.service.FubImportLogService;
import com.homethy.fub.service.FubLeadService;
import com.homethy.fub.service.FubRetryService;
import com.homethy.fub.service.FubTagService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.fub.service.KafkaLogAbility;
import com.homethy.i18n.util.MsgException;
import com.homethy.microservice.client.bo.p.PermissionEnum;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.persistence.UserTag;
import com.homethy.microservice.client.bo.team.TeamSource;
import com.homethy.microservice.client.bo.team.pipline.TeamStage;
import com.homethy.microservice.client.leadsearch.api.LeadSearchService;
import com.homethy.microservice.client.leadsearch.lead.FilterRoleAssigneeBo;
import com.homethy.microservice.client.leadsearch.lead.LeadDetailBo;
import com.homethy.microservice.client.leadsearch.lead.LeadFamilyMemberBo;
import com.homethy.microservice.client.leadsearch.lead.LeadPropertyBo;
import com.homethy.microservice.client.leadsearch.lead.LeadQueryBo;
import com.homethy.microservice.client.leadsearch.lead.SearchResultBo;
import com.homethy.microservice.client.leadsearch.lead.UserLeadTagBo;
import com.homethy.microservice.client.leadsearch.model.LeadSort;
import com.homethy.microservice.client.model.DuplicateLeadQueryBo;
import com.homethy.microservice.client.model.EditLeadModel;
import com.homethy.microservice.client.model.LeadAddRequestBo;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadBo.LeadListingTypeEnum;
import com.homethy.microservice.client.model.LeadFamilyMemberVoBo;
import com.homethy.microservice.client.model.LeadInquiriesBo;
import com.homethy.microservice.client.model.LeadListingInfoBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadSourceEnum;
import com.homethy.microservice.client.model.LocationElemBo;
import com.homethy.microservice.client.model.NotifyInfoBo;
import com.homethy.microservice.client.model.OwnershipBeanBo;
import com.homethy.microservice.client.model.RealEditSource;
import com.homethy.microservice.client.model.SourceInfoBo;
import com.homethy.microservice.client.model.UserEmailBo;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.microservice.client.offline.AgentOfflineService;
import com.homethy.microservice.client.offline.DuplicateLeadQueryOfflineClient;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.microservice.model.LeadUserInfo.RealSourceEnum;
import com.homethy.microservice.model.Result;
import com.homethy.microservice.permission.client.service.offline.PermissionOfflineClientService;
import com.homethy.microservice.persistence.domain.Lead;
import com.homethy.microservice.persistence.domain.lead.LeadAssignee;
import com.homethy.persistence.domain.LeadListingInfo;
import com.homethy.persistence.domain.user.UserPhone;
import com.homethy.persistence.domain.user.UserPhone.PhoneState;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.web.crm.util.BizException;
import com.homethy.zillow.client.FubClientManager;
import com.homethy.zillow.client.LeadManager;
import com.homethy.zillow.client.NoSubLeadFamilyMemberVoBo;
import com.homethy.zillow.client.NotificationManager;
import com.homethy.zillow.configuration.RedisLockService;
import com.homethy.zillow.handler.CreateLeadHandler;
import com.homethy.zillow.handler.NoteCreatedHandler;
import com.homethy.zillow.handler.NoteUpdatedHandler;
import com.homethy.zillow.handler.ProfileCreatedHandler;
import com.homethy.zillow.handler.PropertyCreatedHandler;
import com.homethy.zillow.handler.ReassignHandler;
import com.homethy.zillow.handler.StatusChangeHandler;
import com.homethy.zillow.handler.TourCreatedHandler;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.TimeFrameEnum;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubEmail;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubImportSetting;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubLead.FubStatusEnum;
import com.homethy.zillow.model.constant.fub.FubPeople;
import com.homethy.zillow.model.constant.fub.FubPeople.FubAddress;
import com.homethy.zillow.model.constant.fub.FubPeople.FubPeopleRelationship;
import com.homethy.zillow.model.constant.fub.FubPeople.FubPhone;
import com.homethy.zillow.model.constant.fub.FubSimplePeople;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.constant.fub.FubToken;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import com.homethy.zillow.model.po.FubLeadImportTask;
import com.homethy.zillow.model.po.KafkaRecord;
import com.homethy.zillow.model.po.PeopleImportContext;
import com.homethy.zillow.service.KafkaService;
import com.homethy.zillow.util.MdcUtil;
import com.homethy.zillow.util.ThreadUtil;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FubExistLeadImportServiceImpl implements FubExistLeadImportService,
    FubImportCommonService, KafkaLogAbility, FubRetryService<FubLeadImportTask, String> {

  private static final long MAIN_MEMBER_ID = 0L;
  private static final int EMAIL_LIMIT = 5;
  private static final int PHONE_LIMIT = 5;
  private static final int MEMBER_LIMIT = 5;

  private static final int SHOULD = 1;

  @Autowired
  private FubClientManager fubClientManager;
  @Autowired
  private FubTokenService fubTokenService;
  @Autowired
  private FubImportSettingDao fubImportSettingDao;
  @Autowired
  private FubLeadService fubLeadService;
  @Autowired
  private FubImportLogService fubImportLogService;
  @Autowired
  private LeadSearchService leadSearchService;
  @Autowired
  DuplicateLeadQueryOfflineClient duplicateLeadQueryOfflineClient;
  @Autowired
  private KafkaService kafkaService;
  @Autowired
  private AgentOfflineService agentOfflineService;
  @Autowired
  CreateLeadHandler createLeadHandler;
  @Autowired
  NoteCreatedHandler noteCreatedHandler;
  @Autowired
  NoteUpdatedHandler noteUpdatedHandler;
  @Autowired
  ProfileCreatedHandler profileCreatedHandler;
  @Autowired
  PropertyCreatedHandler propertyCreatedHandler;
  @Autowired
  ReassignHandler reassignHandler;
  @Autowired
  StatusChangeHandler statusChangeHandler;
  @Autowired
  TourCreatedHandler tourCreatedHandler;
  @Autowired
  private FubTagService fubTagService;
  @Autowired
  PermissionOfflineClientService permissionOfflineClientService;
  @Autowired
  LeadOfflineService leadOfflineService;
  @Autowired
  LeadManager leadManager;
  @Autowired
  RedisService redisService;
  @Autowired
  NotificationManager notificationManager;
  @Autowired
  RedisLockService redisLockService;

  @Override
  public void handle(List<KafkaRecord> records) {
    List<FubLeadImportTask> results = Lists.newArrayList();
    records.stream()
        .map(record -> ThreadUtil.submit(() -> doConsumer(record)))
        .toList()
        .forEach(future -> results.add(ThreadUtil.getResult(future, null)));
    results.forEach(this::handleNotification);

  }

  private FubLeadImportTask doConsumer(KafkaRecord record) {
    MdcUtil.addSpanId();
    long start = System.currentTimeMillis();
    FubLeadImportTask task = null;
    try {
      task = JacksonUtils.fromJson(record.getValue(), FubLeadImportTask.class);
    } catch (IOException e) {
      log.warn("handle json wrong:{}", record.getValue(), e);
      return task;
    }
    try {
      assertTrue(task != null, "fubLeadImport is null");
      assertTrue(task.getFubPeople() != null, "fubPeople is null");
      assertTrue(task.getFubInfo() != null, "fubInfo is null");
      handleTask(task, false);
      return task;
    } catch (BizException e) {
      log.warn("handle biz:{},{},{}", e.getCode(), e.getMsg(), record.getValue());
    } catch (Exception e) {
      log.warn("handle wrong:{}", record.getValue(), e);
    } finally {
      if (task != null) {
        finish(task);
      }
      logKafkaInfo(record, start);
      return task;
    }
  }

  private void handleTask(FubLeadImportTask task, boolean isRetry) throws BizException {
    PeopleImportContext ctx = PeopleImportContext.builder()
        .setting(getFubImportSetting(task.getFubInfo()))
        .task(task)
        .logId(initLog(task, task.getFubInfo()))
        .build();
    String addPeopleRedisKey = getFubSyncPeopleKey(task.getFubInfo().getTeamId(),
        task.getFubPeople().getId());
    if (!redisLockService.lock(ZillowConstant.ZILLOW_AREA, addPeopleRedisKey, 30)) {
      log.info("lock task:{}", task);
      return;
    }
    try {
      toHandleTask(ctx);
    } catch (MsgException e) {
      if (e.getKey() == ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED) {
        log.info("fub call limit exceeded:{},{}", task.getFubPeople().getId(), isRetry);
        if (!isRetry) {
          setRetryLog(ctx.getLogId());
        }
      } else if (e.getKey() == ZillowErrorCodeEnum.FUB_TOKEN_INVALID) {
        ZillowErrorCodeEnum errorCode = (ZillowErrorCodeEnum) e.getKey();
        log.info("the fub token is invalid:{},{}", task.getFubPeople().getId(), isRetry);
        setFailLog(ctx.getLogId(), errorCode.getErrorCode() + ", " + errorCode.getErrorMsg());
      } else {
        ZillowErrorCodeEnum errorCode = (ZillowErrorCodeEnum) e.getKey();
        log.warn("run biz:{},{}", errorCode.getErrorCode(), errorCode.getErrorMsg());
        setFailLog(ctx.getLogId(), errorCode.getErrorCode() + ", " + errorCode.getErrorMsg());
      }
    } catch (BizException e) {
      log.warn("handle biz:{},{},{}", e.getCode(), e.getMsg(), JacksonUtils.toJson(task));
      setFailLog(ctx.getLogId(), e.getCode() + ", " + e.getMsg());
    } catch (Exception e) {
      log.warn("handle wrong:{}", JacksonUtils.toJson(task), e);
      setFailLog(ctx.getLogId(), e.getMessage());
    } finally {
      redisLockService.unlock(ZillowConstant.ZILLOW_AREA, addPeopleRedisKey);
    }
  }

  private void toHandleTask(PeopleImportContext ctx) throws BizException {
    FubStateInfo fubInfo = ctx.getTask().getFubInfo();

    boolean success = setFubPeopleDetail(ctx);
    if (!success){
      log.info("not find fubPeople detail:{}", ctx.getTask().getFubPeople().getId());
      setFailLog(ctx.getLogId(), "not find fubPeople detail");
      return;
    }

    long assignId = getAssignId(ctx);
    if (assignId <= 0) {
      setFailLog(ctx.getLogId(), "not find assign");
      return;
    }

    long mappingLeadId = getMappingLeadId(ctx);
    if (mappingLeadId > 0) {
      LeadDetailBo leadDetailBo = searchLeadById(mappingLeadId, fubInfo.getTeamId());
      if (leadDetailBo == null) {
        log.info("lead not found,ignore:{}", mappingLeadId);
        setFailLog(ctx.getLogId(), "mapping lead not found:" + mappingLeadId);
      } else {
        updateExistLead(ctx, leadDetailBo, false, assignId);
      }
      return;
    }
    LeadDetailBo leadDetailBo = searchLeadByEmailsAndAssign(ctx);
    if (leadDetailBo != null) {
      FubLead latestFubLead = fubLeadService.getLatestFubLead(leadDetailBo.getLead().getLeadId());
      if (latestFubLead == null) {
        updateExistLead(ctx, leadDetailBo, true, assignId);
      } else if (latestFubLead.getPeopleId() == ctx.getFubPeople().getId()) {
        updateExistLead(ctx, leadDetailBo, false, assignId);
      } else {
        setFailLog(ctx.getLogId(), "related to fubPeopleId:" + latestFubLead.getPeopleId());
      }
      return;
    }
    long duplicateLeadId = getDuplicateLead(ctx.getTask().getFubInfo(), ctx.getFubPeople());
    if (duplicateLeadId > 0L) {
      log.info("has duplicate lead:{}", duplicateLeadId);
      setFailLog(ctx.getLogId(), "has duplicate lead:" + duplicateLeadId);
      return;
    }
    addNewLead(ctx, assignId);
  }

  private long getAssignId(PeopleImportContext ctx) {
    FubToken token = fubTokenService.getTokenWithoutCallToken(ctx.getFubPeople().getAssignedUserId(),
        ctx.getTask().getFubInfo().getFubTeamId());
    if (token != null) {
      return token.getBindingAgentId();
    }
    String callToken = fubTokenService.getCallToken(ctx.getTask().getFubInfo().toOwnership());
    if (callToken == null) {
      return 0;
    }
    FubUserInfo fubUser = fubClientManager.getUserById(callToken, ctx.getFubPeople().getAssignedUserId());
    User user = agentOfflineService.getUserByAccount(fubUser.getEmail());
    if (user != null && user.getTeamId() == ctx.getTeamId()) {
      return user.getId();
    }
    return 0;
  }

  private void addNewLead(PeopleImportContext ctx, long assignId) {
    FubPeople fubPeople = ctx.getFubPeople();
    FubStateInfo fubInfo = ctx.getTask().getFubInfo();

    LeadAddRequestBo leadAddRequestBo = buildLeadAddRequestBo(ctx.getTask(), fubPeople,
        fubInfo, ctx.getSetting(), assignId);
    log.info("lead add request: {}", leadAddRequestBo);
    leadManager.toValidate(fubPeople.getEmails());
    Result<LeadBo> leadBoResult = leadOfflineService.addLeadV2(leadAddRequestBo);

    FubImportLog logInfo;
    if (leadBoResult.getErrorCode() == 0 && leadBoResult.getData() != null) {
      fubLeadService.addFubLead(buildFubLead(fubPeople, fubInfo, leadBoResult.getData().getId()));
      handleTaskAfterAddLead(ctx.getTask());
      EditLeadModel editLeadModel = addMemberAndProperties(leadBoResult, ctx);
      saveHis(fubPeople, fubInfo);
      logInfo = buildAddLeadSuccessLog(ctx, leadAddRequestBo, leadBoResult, editLeadModel);
    } else {
      logInfo = buildAddLeadFailedLog(ctx, leadAddRequestBo, leadBoResult);
    }
    fubImportLogService.updateById(logInfo);
  }

  private void saveHis(FubPeople fubPeople, FubStateInfo fubInfo) {
    try {
      FubToken fubToken = fubTokenService.getTokenWithoutCallToken(fubInfo.toOwnership());
      if(fubToken == null){
        return;
      }
      fubImportLogService.saveHis(fubToken.getFubAgentId(),
          fubInfo.getTeamId(), fubPeople.getId());
    } catch (Exception e) {
      log.warn("save his error:{}", e.getMessage(), e);
    }
  }

  private FubImportLog buildAddLeadFailedLog(PeopleImportContext ctx,
      LeadAddRequestBo leadAddRequestBo, Result<LeadBo> leadBoResult) {
    return FubImportLog.builder()
        .id(ctx.getLogId())
        .importContent(ctx.getFubPeopleJson())
        .addMsg(JacksonUtils
            .toJson(Map.of("addRequest", leadAddRequestBo, "addResult", leadBoResult)))
        .status(FAIL.getStatus())
        .result(leadBoResult.getErrorMsg() + ", " + leadBoResult.getErrorMsg()
            + "," + MdcUtil.getSpanId())
        .build();
  }

  private FubImportLog buildAddLeadSuccessLog(PeopleImportContext ctx,
      LeadAddRequestBo leadAddRequestBo, Result<LeadBo> leadBoResult, EditLeadModel editLeadModel) {
    return FubImportLog.builder()
        .id(ctx.getLogId())
        .importContent(ctx.getFubPeopleJson())
        .addMsg(JacksonUtils.toJson(Map.of("addRequest", leadAddRequestBo,
            "addResult", leadBoResult,
            "editModel", editLeadModel == null ? new HashMap<>() : editLeadModel)))
        .status(SUCCESS.getStatus())
        .result("add new SUCCESS, " + MdcUtil.getSpanId())
        .loftyId(leadBoResult.getData().getId())
        .build();
  }

  private FubLead buildFubLead(FubPeople fubPeople, FubStateInfo fubInfo, long leadId) {
    return FubLead.builder()
        .fubStatus(FubStatusEnum.TO_LOFTY.getCode())
        .fubTeamId(fubInfo.getFubTeamId())
        .teamId(fubInfo.getTeamId())
        .peopleId(fubPeople.getId())
        .leadId(leadId)
        .fubStageId(fubPeople.getStageId())
        .fubStageName(fubPeople.getStage())
        .ownershipId(fubInfo.getOwnershipId())
        .ownershipScope(fubInfo.getOwnershipScope())
        .build();
  }

  private LeadAddRequestBo buildLeadAddRequestBo(FubLeadImportTask task, FubPeople fubPeople,
      FubStateInfo fubInfo, FubImportSetting setting, long assignId) {
    LeadAddRequestBo leadAddRequestBo = new LeadAddRequestBo();
    leadAddRequestBo.setOwnerId(fubInfo.getBindingAgentId());
    leadAddRequestBo.setOwnership(buildOwnership(fubInfo));
    leadAddRequestBo.setPrivate(false);
    leadAddRequestBo.setStageId(TeamStage.StageType.NewLead.getType());
    leadAddRequestBo.setRealEditSource(RealEditSource.FUB_IMPORT);
    leadAddRequestBo.setFirstName(fubPeople.getFirstName());
    leadAddRequestBo.setLastName(fubPeople.getLastName());
    leadAddRequestBo.setSourceInfo(buildSourceInfoBo(setting, fubInfo.getTeamId()));
    List<Integer> leadTyps = getListingType(fubPeople.getTags(), true);
    leadAddRequestBo.setLeadTypes(leadTyps);
    leadAddRequestBo.setTagIds(getTagIds(fubPeople, fubInfo));
    leadAddRequestBo.setAssignedId(assignId);
    leadAddRequestBo.setLeadProperties(buildLeadProperties(fubPeople, leadTyps));
    setTimeFrameAndPrice(fubPeople, leadAddRequestBo, leadTyps);

    leadAddRequestBo.setCannotText(!setting.isTextOptIn());
    leadAddRequestBo.setCannotEmail(!setting.isEmailOptIn());
    leadAddRequestBo.setCannotCall(!setting.isCallOptIn());
    leadAddRequestBo.setConsentState(setting.getNumberConsent());
    leadAddRequestBo.setNotifyInfo(task.isHistorySync() ? buildNotify() : buildNotify(setting));
    leadAddRequestBo.setAddTimeline(true);
    HashMap<String, Object> timelineContentMap = new HashMap<>();
    timelineContentMap.put("source", RealSourceEnum.FUBOauth.getDescription());
    leadAddRequestBo.setTimelineContentMap(timelineContentMap);
    return leadAddRequestBo;
  }

  private EditLeadModel addMemberAndProperties(Result<LeadBo> leadBoResult,
      PeopleImportContext ctx) {
    FubPeople fubPeople = ctx.getFubPeople();
    FubStateInfo fubInfo = ctx.getTask().getFubInfo();
    EditLeadModel editLeadModel = null;
    if (CollectionUtils.isEmpty(fubPeople.getRelationships())
        && fubPeople.getEmailStrs().isEmpty() && fubPeople.getPhoneStrs().isEmpty()
        && fubPeople.getAddresses().isEmpty()) {
      return editLeadModel;
    }
    try {
      editLeadModel = new EditLeadModel();
      editLeadModel.setUserId(fubInfo.getBindingAgentId());
      List<LeadFamilyMemberVoBo> memberVoBos = fubPeople.getRelationships()
          .stream()
          .map(r -> toNewFamilyMember(r, ctx))
          .limit(MEMBER_LIMIT)
          .toList();
      if (memberVoBos.size() > 0) {
        editLeadModel.setFamilyMemberVos(JacksonUtils.toJson(memberVoBos));
      }
      List<UserPhoneBo> userPhoneBos = fubPeople.getPhones().stream()
          .map(fubPhone -> toUserPhone(fubPhone, ctx, false))
          .toList();
      if (userPhoneBos.size() > 0) {
        editLeadModel.setPhones(JacksonUtils.toJson(userPhoneBos));
      }
      List<UserEmailBo> userEmailBos = fubPeople.getEmails().stream()
          .map(fubEmail -> toUserEmail(fubEmail, false))
          .toList();
      if (userEmailBos.size() > 0) {
        editLeadModel.setEmails(JacksonUtils.toJson(userEmailBos));
      }
      if (!fubPeople.getAddresses().isEmpty()) {
          List<LeadPropertyVoBo> leadProperties = buildLeadProperties(fubPeople, leadBoResult.getData().getLeadTypes());
          editLeadModel.setPropertiesJson(JacksonUtils.toJson(leadProperties));
      }
      leadManager.toValidate(fubPeople.getEmails());
      leadOfflineService.editLead(leadBoResult.getData().getId(), editLeadModel,
          RealEditSource.FUB_IMPORT);
    } catch (Exception e) {
      log.warn("edit family member failed", e);
    }
    return editLeadModel;
  }

  private LeadFamilyMemberVoBo toNewFamilyMember(FubPeopleRelationship relationship,
      PeopleImportContext ctx) {
    LeadFamilyMemberVoBo memberVoBo = new NoSubLeadFamilyMemberVoBo();
    memberVoBo.setFirstName(relationship.getFirstName());
    memberVoBo.setLastName(relationship.getLastName());
    memberVoBo.setRelation(relationship.getType());
    memberVoBo.setUserEmailList(relationship.getEmails().stream()
        .map(email -> toUserEmail(email, false))
        .limit(EMAIL_LIMIT).toList());
    memberVoBo.setUserPhoneList(relationship.getPhones().stream()
        .map(fubPhone -> toUserPhone(fubPhone, ctx, false))
        .limit(PHONE_LIMIT).toList());
    return memberVoBo;
  }

  private UserPhoneBo toUserPhone(FubPhone fubPhone,
      PeopleImportContext ctx, boolean hasPrimary) {
    UserPhoneBo userPhoneBo = new UserPhoneBo();
    userPhoneBo.setPhone(fubPhone.getValue());
    userPhoneBo.setIsPrimary(fubPhone.getIsPrimary() == 1 && !hasPrimary);
    userPhoneBo.setDescription(fubPhone.getType());
    userPhoneBo.setConsentState(ctx.getSetting().getNumberConsent());
    userPhoneBo.setState(
        fubPhone.isValid() ? UserPhone.PhoneState.VALID.toIntValue() : PhoneState.BAD.toIntValue());
    return userPhoneBo;
  }

  private UserEmailBo toUserEmail(FubEmail email, boolean hasPrimary) {
    return UserEmailBo.builder()
        .description(email.getType())
        .email(email.getValue())
        .primary(email.getIsPrimary() == 1 && !hasPrimary)
        .build();
  }

  private OwnershipBeanBo buildOwnership(FubStateInfo fubInfo) {
    return OwnershipBeanBo.builder()
        .ownershipId(fubInfo.getLeadOwnershipId())
        .ownershipScope(LeadBo.OwnershipScopeEnum.valueOf(fubInfo.getOwnershipScope().name()))
        .build();
  }

  private void setTimeFrameAndPrice(FubPeople fubPeople, LeadAddRequestBo leadAddRequestBo,
      List<Integer> leadTyps) {
    String leadTimeFrame = getLeadTimeFrame(fubPeople);
    if (isSeller(leadTyps)) {
      LeadListingInfoBo leadListingInfo = new LeadListingInfoBo();
      if (StringUtils.isNotBlank(leadTimeFrame)) {
        leadListingInfo.setTimeFrame(getLeadTimeFrame(fubPeople));
        leadAddRequestBo.setLeadListingInfo(leadListingInfo);
      }
    } else {
      LeadInquiriesBo leadInquiries = new LeadInquiriesBo();
      leadInquiries.setTimeFrame(leadTimeFrame);
      leadInquiries.setPriceMin(fubPeople.getPrice());
      leadAddRequestBo.setLeadInquiries(leadInquiries);
    }
  }

  private boolean isSeller(List<Integer> leadTyps) {
    return leadTyps.size() == 1 && hasSeller(leadTyps);
  }

  private String getLeadTimeFrame(FubPeople fubPeople) {
    if (fubPeople.getTimeframeId() > 0 && fubPeople.getTimeframeMap() != null) {
      String peopleTimeFrame = fubPeople.getTimeframeMap().get(fubPeople.getTimeframeId());
      return toLeadTimeFrame(peopleTimeFrame);
    }
    return null;
  }

  private String toLeadTimeFrame(String peopleTimeFrame) {
    if (StringUtils.isBlank(peopleTimeFrame)) {
      return null;
    }
    peopleTimeFrame = peopleTimeFrame.replace(" Months", "");
    TimeFrameEnum timeFrameEnum = TimeFrameEnum.of(peopleTimeFrame);
    if (timeFrameEnum != null) {
      return timeFrameEnum.getValue();
    }
    if ("0-3".equalsIgnoreCase(peopleTimeFrame)) {
      return TimeFrameEnum.ONE_THREE.getValue();
    }
    return TimeFrameEnum.JUST_LOOKING.getValue();
  }

  private List<LeadPropertyVoBo> buildLeadProperties(FubPeople fubPeople,
      List<Integer> leadTypes) {
    List<LeadPropertyVoBo> propertyVoBos = new ArrayList<>();
    fubPeople.getAddresses()
        .stream()
        .map(fubAddress -> toProperty(fubAddress, leadTypes))
        .forEach(propertyVoBos::add);
    if (hasSeller(leadTypes)) {
      propertyVoBos.stream()
          .findFirst()
          .ifPresent(propertyVoBo -> propertyVoBo.setMailAddress(true));
    }
    return propertyVoBos;
  }

  private boolean hasSeller(List<Integer> leadTypes) {
    return leadTypes.contains(LeadListingTypeEnum.Sale.getType());
  }

  private LeadPropertyVoBo toProperty(FubAddress fubAddress,
      List<Integer> leadTypes) {
    return LeadPropertyVoBo.builder()
        .streetAddress(fubAddress.getStreet())
        .city(fubAddress.getCity())
        .state(fubAddress.getState())
        .zipcode(fubAddress.getCode())
        .note(fubAddress.getType())
        .label(isSeller(leadTypes) ? HOME.label : HIGH_INTERST.label)
        .build();
  }

  private List<Long> getTagIds(FubPeople fubPeople, FubStateInfo fubInfo) {
    List<String> tagNames = fubPeople.getTags();
    if (CollectionUtils.isEmpty(tagNames)) {
      return new ArrayList<>();
    }
    User user = new User();
    user.setId(fubInfo.getBindingAgentId());
    user.setTeamId(fubInfo.getTeamId());
    List<UserTag> addUserTags = fubTagService.addUserTags(tagNames, user,
        permissionOfflineClientService.hasAny(user.getId(),
            List.of(PermissionEnum.MANAGE_TAG.name(),
                PermissionEnum.MANAGE_TAG_DEPARTMENT.name())));
    return Optional.ofNullable(addUserTags).orElse(new ArrayList<>())
        .stream().map(UserTag::getId).distinct().toList();
  }

  private SourceInfoBo buildSourceInfoBo(FubImportSetting setting, long teamId) {
    SourceInfoBo sourceInfo = new SourceInfoBo();
    sourceInfo.setSourceMeta(Lead.LeadSourceMetaEnum.User.getType());
    sourceInfo.setUserSource(RealSourceEnum.FUBOauth.getDescription());
    sourceInfo.setRealSource(RealSourceEnum.FUBOauth.getType());
    Optional<TeamSource> teamSourceOpt = getTeamSourceByLeadSource(
        teamId, setting.getSourceId());
    if (teamSourceOpt.isPresent()) {
      sourceInfo.setSource(teamSourceOpt.get().getLeadSource());
      sourceInfo.setSourceName(teamSourceOpt.get().getDescription());
    } else {
      sourceInfo.setSource(LeadSourceEnum.FUB.getType());
      sourceInfo.setSourceName(LeadSourceEnum.FUB.getDescription());
    }
    return sourceInfo;
  }

  private NotifyInfoBo buildNotify(FubImportSetting importSetting) {
    NotifyInfoBo notifyInfo = new NotifyInfoBo();
    notifyInfo.defaultSet();
    notifyInfo.setSendSmsToLead(true);
    notifyInfo.setSendSmsToAgent(true);
    if (importSetting.isSendWelcomeEmail()) {
      notifyInfo.setSendWelcome(true);
      notifyInfo.setSendWelcomeWithNoSite(true);
    }
    return notifyInfo;
  }

  private NotifyInfoBo buildNotify() {
    NotifyInfoBo notifyInfo = new NotifyInfoBo();
    notifyInfo.setShouldBeNewLead(false);
    notifyInfo.setSendNotifyToAssign(false);
    notifyInfo.setSendNotifyToOwner(false);
    notifyInfo.setSendSmsToLead(false);
    notifyInfo.setSendSmsToAgent(false);
    notifyInfo.setSendWelcome(false);
    notifyInfo.setSendWelcomeWithNoSite(false);
    return notifyInfo;
  }

  private Optional<TeamSource> getTeamSourceByLeadSource(long teamId, int sourceId) {
    if (sourceId == 0) {
      return Optional.empty();
    }
    return Optional.ofNullable(agentOfflineService.getByLeadSource(teamId, sourceId));
  }

  private List<Integer> getListingType(List<String> tags, boolean forAdd) {
    List<Integer> typeList = new LinkedList<>();
    if (tags.stream().map(String::toLowerCase)
        .anyMatch(tag -> tag.equalsIgnoreCase(LeadListingTypeEnum.Buy.getDescription()))) {
      typeList.add(LeadListingTypeEnum.Buy.getType());
    }
    if (tags.stream().map(String::toLowerCase)
        .anyMatch(tag -> tag.equalsIgnoreCase(LeadListingTypeEnum.Sale.getDescription()))) {
      typeList.add(LeadListingTypeEnum.Sale.getType());
    }
    if (CollectionUtils.isEmpty(typeList) && forAdd) {
      typeList.add(LeadListingTypeEnum.Other.getType());
    }
    return typeList;
  }

  private void updateExistLead(PeopleImportContext ctx, LeadDetailBo leadDetail,
      boolean addFubLead, long assignId) {
    log.info("peopleId:{},leadId:{},addFubLead;{}",
        ctx.getFubPeople().getId(), leadDetail.getLead().getId(), addFubLead);
    FubPeople fubPeople = ctx.getFubPeople();

    EditLeadModel model = new EditLeadModel();
    model.setUserId(ctx.getTask().getFubInfo().getBindingAgentId());
    model.setFirstName(ctx.getFubPeople().getFirstName());
    model.setLastName(ctx.getFubPeople().getLastName());
    model.setAssignedUserId(assignId);
    List<Integer> fubTypes = getListingType(fubPeople.getTags(), false);
    List<Integer> leadTypes = mergeList(leadDetail.getLead().getLeadTypes(), fubTypes);
    model.setLeadTypes(leadTypes);
    List<Long> fubTagIds = getTagIds(fubPeople, ctx.getTask().getFubInfo());
    model.setTags(mergeList(getTagIds(leadDetail), fubTagIds));

    dropDuplicateEmailAndPhone(fubPeople, leadDetail);
    Map<Long, List<UserEmailBo>> memberEmailsMap = getMemberEmailsMap(leadDetail);
    Map<Long, List<UserPhoneBo>> memberPhonesMap = getMemberPhonesMap(leadDetail);
    model.setEmails(JacksonUtils.toJson(mergeEmailsOfMianLead(fubPeople, memberEmailsMap)));
    model.setPhones(JacksonUtils.toJson(mergePhonesOfMianLead(fubPeople, memberPhonesMap, ctx)));
    List<LeadFamilyMemberVoBo> allLeadMembers = mergeMember(
        leadDetail, fubPeople, memberEmailsMap, memberPhonesMap, ctx);
    model.setFamilyMemberVos(JacksonUtils.toJson(allLeadMembers));

    List<LeadPropertyVoBo> allLeadProperties = getLeadProperties(leadDetail);
    mergeProperties(fubPeople, allLeadProperties, leadTypes);
    model.setPropertiesJson(JacksonUtils.toJson(allLeadProperties));
    setTimeFrameAndPrice(leadDetail, fubPeople, model, leadTypes);

    long leadId = leadDetail.getLead().getId();
    FubImportLog importLog = FubImportLog.builder()
        .id(ctx.getLogId())
        .importContent(JacksonUtils
            .toJson(Map.of("fubPeople", ctx.getFubPeopleJson(), "leadDetail", leadDetail)))
        .addMsg(JacksonUtils.toJson(model))
        .loftyId(leadId)
        .build();
    leadManager.toValidate(fubPeople.getEmails());
    try {
      leadOfflineService.editLead(leadId, model, RealEditSource.FUB_IMPORT);
      if (addFubLead) {
        fubLeadService.addFubLead(buildFubLead(fubPeople, ctx.getTask().getFubInfo(), leadId));
      }
      importLog.setStatus(SUCCESS.getStatus());
      importLog.setResult(SUCCESS.name() + (addFubLead ? "(add new relation)" : "")
          + "," + MdcUtil.getSpanId());
    } catch (Exception e) {
      importLog.setStatus(FAIL.getStatus());
      importLog.setResult("handle edit failed:" + e.getMessage()
          + "," + MdcUtil.getSpanId());
      log.warn("handle edit failed:{},{}", fubPeople.getId(), leadId, e);
    }
    fubImportLogService.updateById(importLog);
  }

  private void mergeProperties(FubPeople fubPeople, List<LeadPropertyVoBo> leadProperties,
      List<Integer> leadTypes) {
    boolean hasMailingAddress = leadProperties.stream().anyMatch(LeadPropertyVoBo::isMailAddress);
    int existProperties = leadProperties.size();
    fubPeople.getAddresses()
        .stream()
        .filter(fubAddress -> leadProperties.stream().noneMatch(p -> isSameAddr(p, fubAddress)))
        .map(fubAddress -> toProperty(fubAddress, leadTypes))
        .forEach(leadProperties::add);
    if (!hasMailingAddress) {
      leadProperties.stream().skip(existProperties).findFirst()
          .ifPresent(leadProperty -> leadProperty.setMailAddress(hasSeller(leadTypes)));
    }
  }

  private boolean isSameAddr(LeadPropertyVoBo leadPropertyVoBo, FubAddress fubAddress) {
    return StringUtils.equalsIgnoreCase(leadPropertyVoBo.getState(), fubAddress.getState())
        && StringUtils.equalsIgnoreCase(leadPropertyVoBo.getCity(), fubAddress.getCity())
        && StringUtils.equalsIgnoreCase(leadPropertyVoBo.getZipcode(), fubAddress.getCode())
        && StringUtils.equalsIgnoreCase(leadPropertyVoBo.getStreetAddress(),
        fubAddress.getStreet());
  }

  private List<LeadPropertyVoBo> getLeadProperties(LeadDetailBo leadDetail) {
    return Optional.ofNullable(leadDetail.getLeadPropertyList())
        .orElse(Collections.emptyList())
        .stream()
        .map(this::convert)
        .collect(Collectors.toList());
  }

  private LeadPropertyVoBo convert(LeadPropertyBo leadPropertyBo) {
    return LeadPropertyVoBo.builder()
        .id(leadPropertyBo.getId())
        .leadUserId(leadPropertyBo.getLeadUserId())
        .listingId(leadPropertyBo.getListingId())
        .autoListingId(leadPropertyBo.getAutoListingId())
        .state(leadPropertyBo.getState())
        .county(leadPropertyBo.getCounty())
        .city(leadPropertyBo.getCity())
        .zipcode(leadPropertyBo.getZipcode())
        .streetAddress(leadPropertyBo.getStreetAddress())
        .label(leadPropertyBo.getLabel())
        .propertyType(leadPropertyBo.getPropertyType())
        .bedRooms(leadPropertyBo.getBedRooms())
        .bathRooms(leadPropertyBo.getBathRooms())
        .receptionrooms(leadPropertyBo.getReceptionrooms())
        .squareFeet(leadPropertyBo.getSquareFeet())
        .lotSize(leadPropertyBo.getLotSize())
        .note(leadPropertyBo.getNote())
        .price(leadPropertyBo.getPrice())
        .mailAddress(leadPropertyBo.isMailAddress())
        .listingStatus(leadPropertyBo.getListingStatus())
        .labelList(leadPropertyBo.getLabelList())
        .pictureUrl(leadPropertyBo.getPictureUrl())
        .propertySource(leadPropertyBo.getPropertySource())
        .siteListingUrl(leadPropertyBo.getSiteListingUrl())
        .build();
  }

  private void setTimeFrameAndPrice(LeadDetailBo leadDetailBo, FubPeople fubPeople,
      EditLeadModel editLeadModel, List<Integer> leadTypes) {
    String leadTimeFrame = getLeadTimeFrame(fubPeople);
    if (isSeller(leadTypes)) {
      LeadListingInfoBo listingInfoBo = convertOrNew(leadDetailBo.getLeadListingInfo());
      if (StringUtils.isNotBlank(leadTimeFrame)) {
        listingInfoBo.setTimeFrame(leadTimeFrame);
        editLeadModel.setListingInfo(JacksonUtils.toJson(listingInfoBo));
      }
    } else {
      LeadInquiriesBo inquiriesBo = convert(leadDetailBo.getLeadInquiries());
      handleInquire(fubPeople, editLeadModel, leadTimeFrame, inquiriesBo);
    }
  }

  private void handleInquire(FubPeople fubPeople, EditLeadModel editLeadModel, String leadTimeFrame,
      LeadInquiriesBo inquiriesBo) {
    if (inquiriesBo == null) {
      addInquire(fubPeople, editLeadModel, leadTimeFrame);
    } else {
      updateInquire(fubPeople, editLeadModel, leadTimeFrame, inquiriesBo);
    }
  }

  private void updateInquire(FubPeople fubPeople, EditLeadModel editLeadModel, String leadTimeFrame,
      LeadInquiriesBo inquiriesBo) {
    boolean update = false;
    if (StringUtils.isNotBlank(leadTimeFrame)) {
      inquiriesBo.setTimeFrame(leadTimeFrame);
      update = true;
    }
    if (fubPeople.getPrice() > 0) {
      update = setPrice(fubPeople, inquiriesBo);
    }
    if (update) {
      editLeadModel.setInquiryJson(JacksonUtils.toJson(inquiriesBo));
    }
  }

  private void addInquire(FubPeople fubPeople, EditLeadModel editLeadModel, String leadTimeFrame) {
    LeadInquiriesBo inquiriesBo;
    if (StringUtils.isNotBlank(leadTimeFrame) || fubPeople.getPrice() > 0) {
      inquiriesBo = new LeadInquiriesBo();
      inquiriesBo.setTimeFrame(leadTimeFrame);
      inquiriesBo.setPriceMin(fubPeople.getPrice());
      editLeadModel.setInquiryJson(JacksonUtils.toJson(inquiriesBo));
    }
  }

  private boolean setPrice(FubPeople fubPeople, LeadInquiriesBo inquiriesBo) {
    if (fubPeople.getPrice() > inquiriesBo.getPriceMax() && inquiriesBo.getPriceMax() >= 0) {
      log.info("price is too high:{},{}", fubPeople.getPrice(), inquiriesBo.getPriceMax());
      return false;
    } else {
      inquiriesBo.setPriceMin(fubPeople.getPrice());
      return true;
    }
  }

  private LeadListingInfoBo convertOrNew(LeadListingInfo leadListingInfo) {
    if (leadListingInfo == null) {
      return new LeadListingInfoBo();
    }
    return LeadListingInfoBo.builder()
        .id(leadListingInfo.getId())
        .leadUserId(leadListingInfo.getLeadUserId())
        .priceMax(leadListingInfo.getPriceMax())
        .priceMin(leadListingInfo.getPriceMin())
        .state(leadListingInfo.getState())
        .county(leadListingInfo.getCounty())
        .city(leadListingInfo.getCity())
        .streetAddress(leadListingInfo.getStreetAddress())
        .zipcode(leadListingInfo.getZipcode())
        .propertyType(leadListingInfo.getPropertyType())
        .bedrooms(leadListingInfo.getBedrooms())
        .bathrooms(leadListingInfo.getBathrooms())
        .squareFeet(leadListingInfo.getSquareFeet())
        .lotSize(leadListingInfo.getLotSize())
        .parkingSpace(leadListingInfo.getParkingSpace())
        .floors(leadListingInfo.getFloors())
        .timeFrame(leadListingInfo.getTimeFrame())
        .mortgage(leadListingInfo.getMortgage())
        .buyHouse(leadListingInfo.getBuyHouse())
        .hasAgent(leadListingInfo.getHasAgent())
        .addressForNotification(leadListingInfo.getAddressForNotification())
        .address(leadListingInfo.getAddress())
        .sellerRequest(leadListingInfo.getSellerRequest())
        .PriceRange(leadListingInfo.getPriceRange())
        .build();
  }

  private LeadInquiriesBo convert(
      com.homethy.microservice.client.leadsearch.lead.LeadInquiriesBo leadInquiries) {
    if (leadInquiries == null) {
      return null;
    }
    return LeadInquiriesBo.builder()
        .leadUserId(leadInquiries.getLeadUserId())
        .priceMin(leadInquiries.getPriceMin())
        .priceMax(leadInquiries.getPriceMax())
        .propertyType(leadInquiries.getPropertyType())
        .bedroomsMin(leadInquiries.getBedroomsMin())
        .bedroomsMax(leadInquiries.getBedroomsMax())
        .bathroomsMin(leadInquiries.getBathroomsMin())
        .bathroomsMax(leadInquiries.getBathroomsMax())
        .sizeMin(leadInquiries.getSizeMin())
        .sizeMax(leadInquiries.getSizeMax())
        .locations(convert(leadInquiries.getLocations()))
        .preQual(leadInquiries.getPreQual())
        .fthb(leadInquiries.getFthb())
        .timeFrame(leadInquiries.getTimeFrame())
        .sellHouse(leadInquiries.getSellHouse())
        .hasAgent(leadInquiries.getHasAgent())
        .build();
  }

  private List<LocationElemBo> convert(
      List<com.homethy.microservice.client.leadsearch.lead.LocationElemBo> locations) {
    if (locations == null) {
      return null;
    }
    return locations.stream()
        .map(location -> {
          LocationElemBo locationElemBo = new LocationElemBo();
          locationElemBo.setType(location.getType());
          locationElemBo.setValue(location.getValue());
          locationElemBo.setStateCode(location.getStateCode());
          return locationElemBo;
        })
        .collect(Collectors.toList());
  }

  private List<LeadFamilyMemberVoBo> mergeMember(LeadDetailBo leadDetailBo,
      FubPeople fubPeople, Map<Long, List<UserEmailBo>> memberEmailsMap,
      Map<Long, List<UserPhoneBo>> memberPhonesMap,
      PeopleImportContext ctx) {
    List<LeadFamilyMemberVoBo> allLeadMembers = buildOldMembers(leadDetailBo, memberEmailsMap,
        memberPhonesMap);

    fubPeople.getRelationships().forEach(relationship -> {
      LeadFamilyMemberVoBo matchedMember = allLeadMembers.stream()
          .filter(member -> nameMatch(member, relationship)).findFirst().orElse(null);
      if (matchedMember != null) {
        matchedMember.setUserEmailList(mergeMemberEmails(
            matchedMember.getUserEmailList(), relationship.getEmails(), matchedMember));
        matchedMember.setUserPhoneList(mergeMemberPhones(
            matchedMember.getUserPhoneList(), relationship.getPhones(), matchedMember, ctx));
        if (StringUtils.isBlank(matchedMember.getRelation())) {
          matchedMember.setRelation(relationship.getType());
        }
      } else if (allLeadMembers.size() <= MEMBER_LIMIT) {
        allLeadMembers.add(toNewFamilyMember(relationship, ctx));
      }
    });
    return allLeadMembers;
  }

  private List<UserPhoneBo> mergeMemberPhones(List<UserPhoneBo> userPhoneList,
      List<FubPhone> phones, LeadFamilyMemberVoBo matchedMember, PeopleImportContext ctx) {
    List<UserPhoneBo> result = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(userPhoneList)) {
      result.addAll(userPhoneList);
    }
    int size = result.size();
    boolean hasPrimary = userPhoneList.stream().anyMatch(UserPhoneBo::isPrimary);
    phones.stream()
        .map(fubPhone -> toUserPhone(fubPhone, ctx, hasPrimary))
        .peek(phone -> phone.setFamilyMemberId(matchedMember.getId()))
        .limit(PHONE_LIMIT - size)
        .forEach(result::add);
    return result;
  }

  private List<UserEmailBo> mergeMemberEmails(List<UserEmailBo> userEmailList,
      List<FubEmail> emails, LeadFamilyMemberVoBo matchedMember) {
    List<UserEmailBo> result = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(userEmailList)) {
      result.addAll(userEmailList);
    }
    int size = result.size();
    boolean hasPrimary = userEmailList.stream().anyMatch(UserEmailBo::isPrimary);
    emails.stream()
        .map(fubEmail -> toUserEmail(fubEmail, hasPrimary))
        .peek(email -> email.setFamilyMemberId(matchedMember.getId()))
        .limit(EMAIL_LIMIT - size)
        .forEach(result::add);
    return result;
  }

  private List<LeadFamilyMemberVoBo> buildOldMembers(LeadDetailBo leadDetailBo,
      Map<Long, List<UserEmailBo>> memberEmailsMap, Map<Long, List<UserPhoneBo>> memberPhonesMap) {
    return Optional.ofNullable(leadDetailBo.getFamilyMemberList())
        .orElse(Collections.emptyList())
        .stream()
        .map(bo -> convert(bo, memberEmailsMap, memberPhonesMap))
        .collect(Collectors.toList());
  }

  private boolean nameMatch(LeadFamilyMemberVoBo member, FubPeopleRelationship relationship) {
    return same(member.getFirstName(), relationship.getFirstName())
        && same(member.getLastName(), relationship.getLastName());
  }

  private boolean same(String firstName, String firstName1) {
    return StringUtils.equalsIgnoreCase(
        StringUtils.defaultIfBlank(firstName, ""),
        StringUtils.defaultIfBlank(firstName1, ""));
  }

  private LeadFamilyMemberVoBo convert(LeadFamilyMemberBo leadFamilyMemberBo,
      Map<Long, List<UserEmailBo>> memberEmailsMap, Map<Long, List<UserPhoneBo>> memberPhonesMap) {
    LeadFamilyMemberVoBo memberVoBo = new NoSubLeadFamilyMemberVoBo();
    memberVoBo.setId(leadFamilyMemberBo.getId());
    memberVoBo.setUserId(leadFamilyMemberBo.getUserId());
    memberVoBo.setRelation(leadFamilyMemberBo.getRelation());
    memberVoBo.setFirstName(leadFamilyMemberBo.getFirstName());
    memberVoBo.setLastName(leadFamilyMemberBo.getLastName());
    memberVoBo.setSubscriptionState(leadFamilyMemberBo.getSubscriptionState());
    memberVoBo.setBirthday(leadFamilyMemberBo.getBirthday());
    memberVoBo.setUserEmailList(
        memberEmailsMap.getOrDefault(leadFamilyMemberBo.getId(), new ArrayList<>()));
    memberVoBo.setUserPhoneList(
        memberPhonesMap.getOrDefault(leadFamilyMemberBo.getId(), new ArrayList<>()));
    return memberVoBo;
  }

  private List<Long> getTagIds(LeadDetailBo leadDetailBo) {
    return Optional.ofNullable(leadDetailBo.getLead().getTags())
        .orElse(Collections.emptyList())
        .stream()
        .map(UserLeadTagBo::getTagId)
        .toList();
  }

  private <T> List<T> mergeList(List<T> first, List<T> second) {
    List<T> result = new ArrayList<>();
    if (CollectionUtils.isNotEmpty(first)) {
      result.addAll(first);
    }
    if (CollectionUtils.isEmpty(second)) {
      return result;
    }
    for (T item : second) {
      if (!result.contains(item)) {
        result.add(item);
      }
    }
    return result;
  }

  private List<UserPhoneBo> mergePhonesOfMianLead(FubPeople fubPeople,
      Map<Long, List<UserPhoneBo>> memberPhonesMap,
      PeopleImportContext ctx) {
    List<UserPhoneBo> phoneBos = memberPhonesMap.getOrDefault(
        MAIN_MEMBER_ID, new ArrayList<>());
    boolean hasPrimary = phoneBos.stream().anyMatch(UserPhoneBo::isPrimary);
    phoneBos.addAll(fubPeople.getPhones().stream()
        .map(fubPhone -> toUserPhone(fubPhone, ctx, hasPrimary))
        .toList());
    return phoneBos.stream().limit(PHONE_LIMIT).collect(Collectors.toList());
  }

  private Map<Long, List<UserEmailBo>> getMemberEmailsMap(
      LeadDetailBo leadDetailBo) {
    return Optional.ofNullable(leadDetailBo.getEmailList())
        .orElse(Collections.emptyList())
        .stream()
        .map(this::convert)
        .collect(Collectors.groupingBy(UserEmailBo::getFamilyMemberId));
  }

  private UserEmailBo convert(
      com.homethy.microservice.client.leadsearch.lead.UserEmailBo userEmailBo) {
    return UserEmailBo.builder()
        .autoId(userEmailBo.getAutoId())
        .userId(userEmailBo.getUserId())
        .familyMemberId(userEmailBo.getFamilyMemberId())
        .email(userEmailBo.getEmail())
        .description(userEmailBo.getDescription())
        .valid(userEmailBo.isValid())
        .teamId(userEmailBo.getTeamId())
        .isBounced(userEmailBo.isBounced())
        .primary(userEmailBo.isPrimary())
        .build();
  }

  private UserPhoneBo convert(
      com.homethy.microservice.client.leadsearch.lead.UserPhoneBo userPhoneBo) {
    UserPhoneBo bo = new UserPhoneBo();
    bo.setAutoId(userPhoneBo.getAutoId());
    bo.setUserId(userPhoneBo.getUserId());
    bo.setFamilyMemberId(userPhoneBo.getFamilyMemberId());
    bo.setPhone(userPhoneBo.getPhone());
    bo.setConsentState(userPhoneBo.getConsentState());
    bo.setDescription(userPhoneBo.getDescription());
    bo.setValid(userPhoneBo.isValid());
    bo.setState(userPhoneBo.getState());
    bo.setType(userPhoneBo.getType());
    bo.setPhoneCountry(userPhoneBo.getPhoneCountry());
    bo.setPhoneCode(userPhoneBo.getPhoneCode());
    return bo;
  }

  private Map<Long, List<UserPhoneBo>> getMemberPhonesMap(
      LeadDetailBo leadDetailBo) {
    return Optional.ofNullable(leadDetailBo.getPhoneList())
        .orElse(Collections.emptyList())
        .stream()
        .map(this::convert)
        .collect(Collectors.groupingBy(UserPhoneBo::getFamilyMemberId));
  }

  private List<UserEmailBo> mergeEmailsOfMianLead(
      FubPeople fubPeople, Map<Long, List<UserEmailBo>> memberEmailsMap) {
    List<UserEmailBo> leadEmails = memberEmailsMap.getOrDefault(
        MAIN_MEMBER_ID, new ArrayList<>());
    boolean hasPrimary = leadEmails.stream().anyMatch(UserEmailBo::isPrimary);
    fubPeople.getEmails().stream()
        .map(fubEmail -> toUserEmail(fubEmail, hasPrimary))
        .forEach(leadEmails::add);
    return leadEmails.stream().limit(EMAIL_LIMIT).collect(Collectors.toList());
  }

  private void dropDuplicateEmailAndPhone(FubPeople fubPeople, LeadDetailBo leadDetailBo) {
    Set<String> emailSet = getEmailSet(leadDetailBo);
    Set<String> phoneSet = getPhoneSet(leadDetailBo);

    fubPeople.setEmails(filterDupliateEmail(fubPeople.getEmails(), emailSet));
    fubPeople.setPhones(filterDuplicatePhone(phoneSet, fubPeople.getPhones()));
    if (CollectionUtils.isNotEmpty(fubPeople.getRelationships())) {
      fubPeople.getRelationships().forEach(relationship -> {
        relationship.setEmails(filterDupliateEmail(relationship.getEmails(), emailSet));
        relationship.setPhones(filterDuplicatePhone(phoneSet, relationship.getPhones()));
      });
    }
  }

  private Set<String> getPhoneSet(LeadDetailBo leadDetailBo) {
    return Optional.ofNullable(leadDetailBo.getPhoneList())
        .orElse(new ArrayList<>())
        .stream()
        .map(com.homethy.microservice.client.leadsearch.lead.UserPhoneBo::getPhone)
        .collect(Collectors.toSet());
  }

  private Set<String> getEmailSet(LeadDetailBo leadDetailBo) {
    return Optional.ofNullable(leadDetailBo.getEmailList())
        .orElse(new ArrayList<>())
        .stream()
        .map(com.homethy.microservice.client.leadsearch.lead.UserEmailBo::getEmail)
        .map(String::toLowerCase)
        .collect(Collectors.toSet());
  }

  private List<FubPhone> filterDuplicatePhone(Set<String> phoneSet, List<FubPhone> phones) {
    return phones
        .stream()
        .filter(phone -> !phoneSet.contains(phone.getValue().toLowerCase()))
        .collect(Collectors.toList());
  }

  private List<FubEmail> filterDupliateEmail(List<FubEmail> emails, Set<String> emailSet) {
    return emails
        .stream()
        .filter(email -> !emailSet.contains(email.getValue().toLowerCase()))
        .collect(Collectors.toList());
  }

  private boolean setFubPeopleDetail(PeopleImportContext ctx) throws BizException {
    FubStateInfo fubInfo = ctx.getTask().getFubInfo();
    FubSimplePeople simplePeople = ctx.getTask().getFubPeople();
    FubPeople fubPeople = getFubPeopleDetail(fubInfo, simplePeople);
    if (fubPeople == null){
      return false;
    }
    ctx.setFubPeople(fubPeople);
    ctx.setFubPeopleJson(JacksonUtils.toJson(fubPeople));
    return true;
  }

  private long getMappingLeadId(PeopleImportContext ctx) {
    FubStateInfo fubInfo = ctx.getTask().getFubInfo();
    FubSimplePeople simplePeople = ctx.getTask().getFubPeople();
    FubLead mapping = fubLeadService.getFubLeadByMapping(fubInfo.getFubTeamId(),
        simplePeople.getId(), fubInfo.getTeamId());
    return mapping == null ? 0L : mapping.getLeadId();
  }

  private void setFailLog(long logId, String errMsg) {
    FubImportLog log = FubImportLog.builder()
        .id(logId)
        .status(FAIL.getStatus())
        .result(errMsg + "," + MdcUtil.getSpanId())
        .build();
    fubImportLogService.updateStatusById(log);
  }

  private long initLog(FubLeadImportTask task, FubStateInfo fubInfo) {
    FubImportLog initLog = FubImportLog.builder()
        .importContent(JacksonUtils.toJson(task))
        .addMsg(StringUtils.EMPTY)
        .type(task.getImportTypeEnum())
        .loftyId(0L)
        .status(INIT.getStatus())
        .result(INIT.name() + "," + MdcUtil.getSpanId())
        .ownershipScope(fubInfo.getOwnershipScope().name())
        .ownershipId(fubInfo.getOwnershipId())
        .importUserId(fubInfo.getBindingAgentId())
        .build();
    return fubImportLogService.insert(initLog);
  }

  private long getDuplicateLead(FubStateInfo fubInfo, FubPeople fubPeople) {
    DuplicateLeadQueryBo duplicateQuery = DuplicateLeadQueryBo.builder()
        .teamId(fubInfo.getTeamId())
        .emails(fubPeople.getEmailStrs())
        .userId(fubInfo.getBindingAgentId())
        .ownership(OwnershipBeanBo.builder()
            .ownershipId(fubInfo.getLeadOwnershipId())
            .ownershipScope(LeadBo.OwnershipScopeEnum
                .of(fubInfo.getOwnershipScope().name()))
            .build())
        .build();
    log.info("duplicateQuery: {}", duplicateQuery);
    LeadBo duplicateLead =
        duplicateLeadQueryOfflineClient.getEmailsDuplicateLead(duplicateQuery);
    return duplicateLead == null ? 0L : duplicateLead.getId();
  }

  private void setRetryLog(long logId) {
    FubImportLog retryLog = FubImportLog.builder()
        .id(logId)
        .loftyId(0L)
        .status(FUB_LIMIT_EXCEEDED.getStatus())
        .result(FUB_LIMIT_EXCEEDED.name() + "," + MdcUtil.getSpanId())
        .build();
    fubImportLogService.updateStatusById(retryLog);
  }

  private void finish(FubLeadImportTask task) {
    if (task.getFubPeople().isLastOne()) {
      log.info("finish task: {}", task.getFubPeople().getId());
      FubStateInfo fubInfo = task.getFubInfo();
      nextStepForImportHis(fubInfo);
    }
  }

  @Override
  public KafkaService getKafkaService() {
    return kafkaService;
  }

  private FubPeople getFubPeopleDetail(FubStateInfo fubInfo, FubSimplePeople simplePeople)
      throws BizException {
    FubToken fubToken = getFubToken(fubInfo, true);
    if (fubToken == null || fubToken.getCallToken() == null) {
      log.info("token not found:{}", fubInfo.toOwnership());
      return null;
    }
    FubPeople fubPeople = fubClientManager.getPeopleDetail(simplePeople.getId(),
        getFubToken(fubInfo).getCallToken());
    if(fubPeople == null){
      log.info("fubPeople not found:{}", fubInfo.toOwnership());
      return null;
    }
//    assertTrue(fubPeople != null, "fubPeople not found:" + simplePeople.getId());
    fubPeople.setLastOne(simplePeople.isLastOne());
    List<FubPeopleRelationship> relationships = fubClientManager
        .getPeopleRelationships(simplePeople.getId(), getFubToken(fubInfo).getCallToken());

    fubPeople.setTimeframeMap(fubClientManager
        .getTimeframeConfigCache(getFubToken(fubInfo).getCallToken()));
    if (CollectionUtils.isNotEmpty(fubPeople.getEmails())) {
      Set<String> relationEmails = getRelationEmails(relationships);
      fubPeople.setEmails(fubPeople.getEmails()
          .stream()
          .filter(email -> StringUtils.isNotBlank(email.getValue()))
          .filter(email -> !relationEmails.contains(email.getValue()))
          .limit(EMAIL_LIMIT)
          .collect(Collectors.toList()));
    }
    if (CollectionUtils.isNotEmpty(fubPeople.getPhones())) {
      Set<String> relationshipPhones = getRelationshipPhones(relationships);
      fubPeople.setPhones(fubPeople.getPhones()
          .stream()
          .filter(phone -> StringUtils.isNotBlank(phone.getValue()))
          .filter(phone -> !relationshipPhones.contains(phone.getValue()))
          .limit(PHONE_LIMIT)
          .collect(Collectors.toList()));
    }
    relationships = limit(relationships);
    fubPeople.setRelationships(relationships);
    return fubPeople;
  }

  private Set<String> getRelationshipPhones(List<FubPeopleRelationship> relationships) {
    return relationships.stream().map(FubPeopleRelationship::getPhones)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .map(FubPhone::getValue)
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toSet());
  }

  private Set<String> getRelationEmails(List<FubPeopleRelationship> relationships) {
    return relationships.stream().map(FubPeopleRelationship::getEmails)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .map(FubEmail::getValue)
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toSet());
  }

  private List<FubPeopleRelationship> limit(
      List<FubPeopleRelationship> relationships) {
    relationships = relationships.stream()
        .limit(MEMBER_LIMIT)
        .peek(relationship -> {
          relationship.setEmails(relationship.getEmails().stream().limit(EMAIL_LIMIT).collect(
              Collectors.toList()));
          relationship.setPhones(relationship.getPhones().stream().limit(PHONE_LIMIT).collect(
              Collectors.toList()));
        })
        .collect(Collectors.toList());
    return relationships;
  }

  @Override
  public Logger getLog() {
    return log;
  }

  @Override
  public FubTokenService getFubTokenService() {
    return fubTokenService;
  }

  private FubImportSetting getFubImportSetting(FubStateInfo fubInfo) throws BizException {
    FubImportSetting setting = fubImportSettingDao.selectByOwnership(
        fubInfo.getOwnershipId(), fubInfo.getOwnershipScope().name());
    assertTrue(setting != null, "setting is null");
    return setting;
  }

  private LeadDetailBo searchLeadByEmailsAndAssign(PeopleImportContext ctx) {
    if (CollectionUtils.isEmpty(ctx.getFubPeople().getEmailStrs())) {
      return null;
    }
    long agentId = ctx.getTask().getFubInfo().getBindingAgentId();
    LeadQueryBo query = new LeadQueryBo();
    query.setTeamId(ctx.getTask().getFubInfo().getTeamId());
    query.setRoleAssigneeList(mustBeAgentRole(agentId));
    query.setAllMatchEmails(ctx.getFubPeople().getEmailStrs());
    query.setIsPrivateLead(-1);
    query.setLeadSort(
        new String[]{LeadSort.LAST_TOUCH_DESC.name(), LeadSort.CREATE_TIME_DESC.name()});
    return queryLeadDetail(query);
  }

  private List<FilterRoleAssigneeBo> mustBeAgentRole(long agentId) {
    FilterRoleAssigneeBo filter = new FilterRoleAssigneeBo();
    filter.setRoleId(LeadAssignee.LeadAssigneeRole.Agent.toIntValue());
    filter.setIncludeType(SHOULD);
    filter.setAssigneeToIds(List.of(agentId));
    return List.of(filter);
  }

  private LeadDetailBo searchLeadById(long leadId, long teamId) {
    LeadQueryBo query = new LeadQueryBo();
    query.setTeamId(teamId);
    query.setLeadIds(new long[]{leadId});
    return queryLeadDetail(query);
  }

  private String[] returnField() {
    return new String[]{"data.lead.leadId", "data.lead.tags", "data.lead.leadTypes",
        "data.leadPropertyList", "data.phoneList", "data.emailList", "data.familyMemberList",
        "data.leadListingInfo", "data.leadInquiries"};
  }

  private LeadDetailBo queryLeadDetail(LeadQueryBo query) {
    query.setSize(1);
    query.setReturnField(returnField());
    SearchResultBo result = leadSearchService.search(query);
    if (result == null || result.getLeadDetailModels() == null
        || result.getLeadDetailModels().size() == 0) {
      return null;
    }
    return result.getLeadDetailModels().stream().findFirst().orElse(null);
  }

  @Override
  public String handle(FubImportMsg fubImportMsg, FubLeadImportTask task) {
    try {
      assertTrue(task != null, "fubLeadImport is null");
      handleTask(task, true);
      return "finish";
    } catch (BizException e) {
      log.warn("handle biz:{},{},{}", e.getCode(), e.getMsg(), JacksonUtils.toJson(task));
      return "handle biz:{}" + e.getCode() + ":" + e.getMsg();
    } catch (Exception e) {
      log.warn("handle wrong:{}", JacksonUtils.toJson(task), e);
      return "handle wrong:" + e.getMessage();
    }
  }

  @Override
  public FubLeadImportTask getInput(FubImportLog fubImportLog) {
    String importContent = fubImportLog.getImportContent();
    try {
      return JacksonUtils.fromJson(importContent,
          FubLeadImportTask.class);
    } catch (IOException e) {
      log.warn("handle wrong:{}", importContent, e);
    }
    return null;
  }

  @Override
  public FubImportMsg getFubContext(FubImportLog fubImportLog) {
    FubImportMsg fubImportMsg = new FubImportMsg();
    String importContent = fubImportLog.getImportContent();
    try {
      FubLeadImportTask task = JacksonUtils.fromJson(importContent, FubLeadImportTask.class);
      fubImportMsg.setStateInfo(task.getFubInfo());
    } catch (IOException e) {
      log.warn("handle wrong:{}", importContent, e);
    }
    return fubImportMsg;
  }

  @Override
  public FubImportTypeEnum getImportType() {
    return FubImportTypeEnum.PEOPLE_IMPORTED;
  }

  public void handleTaskAfterAddLead(FubLeadImportTask task) {
    if (!task.isHistorySync()) {
      return;
    }
    long agentId = task.getFubInfo().getBindingAgentId();
    addLeadCount(agentId);
  }

  @Override
  public void handleNotification(FubLeadImportTask task) {
    if (task == null || !task.isHistorySync() || !task.getFubPeople().isLastOne()) {
      return;
    }
    long agentId = task.getFubInfo().getBindingAgentId();
    int leadCount = getLeadCount(agentId);
    if (leadCount <= 0) {
      log.info("not add any lead");
      return;
    }
    notificationManager.addNotification(agentId, leadCount, FUB_IMPORT_CHANNEL);
    redisService.del(ZillowConstant.ZILLOW_AREA, "FUB_SYNC_LEAD_" + agentId);
  }

  public void addLeadCount(long agentId) {
    redisService.incr(ZillowConstant.ZILLOW_AREA,
        "FUB_SYNC_LEAD_" + agentId);
  }

  public int getLeadCount(long agentId) {
    return NumberUtils.toInt(
        redisService.get(ZillowConstant.ZILLOW_AREA, "FUB_SYNC_LEAD_" + agentId), 0);
  }

  public String getFubSyncPeopleKey(long teamId, long peopleId) {
    return "FUB_SYNC_LEAD_PEOPLE_" + teamId +"_"+ peopleId;
  }
}

package com.homethy.fub.service;


import com.homethy.microservice.BO.LeadTransactionDetailBO;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubTrRef;

public interface FubPushTransactionService {

  void handCreateTransaction(LeadTransactionDetailBO leadTransaction, FubLead fubLead);

  void handUpdateTransaction(FubTrRef fubTrRef, LeadTransactionDetailBO leadTransaction, FubLead fubLead);

  void handDeleteTransaction(FubTrRef fubTrRef, LeadTransactionDetailBO leadTransaction, FubLead fubLead);

}

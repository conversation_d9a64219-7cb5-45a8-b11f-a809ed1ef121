package com.homethy.fub.service;

import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubImportMsg;

import java.time.format.DateTimeFormatter;

public interface FubRetryService<I, R> {

  DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM d, yy, h:mma");

  R handle(FubImportMsg fubImportMsg, I input);

  I getInput(FubImportLog fubImportLog);

  FubImportMsg getFubContext(FubImportLog fubImportLog);

  FubImportTypeEnum getImportType();
}

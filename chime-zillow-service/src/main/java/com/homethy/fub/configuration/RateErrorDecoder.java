package com.homethy.fub.configuration;

import java.io.InputStream;
import java.util.Set;


import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RateErrorDecoder implements ErrorDecoder {

  @Override
  public Exception decode(String s, Response response) {
    if (response.status() == 429) {
      String retryAfter = response.headers()
          .getOrDefault("Retry-After", Set.of("0"))
          .iterator().next();
      log.info("FubClient {},{}", s, response.status(), retryAfter);
      return ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED.exp();
    }
    String responseBody = getBody(response);
    if (response.status() == 401 || (response.status() == 400 && responseBody.contains("Invalid refresh token"))) {
      log.info("Fub token is invalid {},{}", s, response.status());
      return ZillowErrorCodeEnum.FUB_TOKEN_INVALID.exp();
    }
    log.info("FubClient {}:{},{},{}", s, response.status(), response.reason(), responseBody);
    return new RuntimeException(responseBody);
  }

  private String getBody(Response response) {
    InputStream is = null;
    try {
      if (response.body() != null) {
        is = response.body().asInputStream();
        return new String(is.readAllBytes(), "UTF-8");
      }
      return "";
    } catch (Exception e) {
      log.warn("getBody", e);
      return "get body null";
    } finally {
      if (is != null) {
        try {
          is.close();
        } catch (Exception e) {
        }
      }
    }
  }
}

package com.homethy.fub.configuration;

import com.homethy.zillow.configuration.ZillowConfig;

import org.springframework.context.annotation.Bean;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RateFeignConfig {

  private static final String xSystem = ZillowConfig.getProperties("chime-zillow.fub.xSystem", "");
  private static final String xSystemKey = ZillowConfig.getProperties("chime-zillow.fub.xSystemKey",
      "");

  @Bean
  public RequestInterceptor fubHeaderInterceptor() {
    return new RequestInterceptor() {
      @Override
      public void apply(RequestTemplate template) {
        template.header("x-System", xSystem);
        template.header("x-System-Key", xSystemKey);
        if (log.isDebugEnabled()) {
          log.debug("add x-System header");
        }
      }
    };
  }

  @Bean
  public ErrorDecoder errorDecoder() {
    return new RateErrorDecoder();
  }
}

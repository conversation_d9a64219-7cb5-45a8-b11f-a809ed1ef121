package com.homethy.web.crm.util;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BizException extends Exception {

  private static final int ERROR_CODE = -1;
  private static final int SUCCESS_CODE = 0;

  private int code;
  private String msg;

  public BizException(int code, String msg) {
    super(msg);
    this.code = code;
    this.msg = msg;
  }

  public BizException(int code, String msg, Throwable cause) {
    super(msg, cause);
    this.code = code;
    this.msg = msg;
  }

  public static void assertTrue(boolean condition, String msg) throws BizException {
    if (!condition) {
      throw new BizException(ERROR_CODE, msg);
    }
  }

  public static void assertTrue(boolean condition, int code, String msg) throws BizException {
    if (!condition) {
      throw new BizException(code, msg);
    }
  }

  public static void assertTrue(boolean condition, int code, String msg, Throwable cause)
      throws BizException {
    if (!condition) {
      throw new BizException(code, msg, cause);
    }
  }
}

package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.po.ZillowTransactionLink;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@DataSourceBase
public interface ZillowTransactionDao {

  String TABLE = " zillow_transaction_link ";
  String ALL_COL = " id, zillow_transaction_id, transaction_id, lead_id, delete_flag ";

  @Insert({"" +
      " insert into " +
      TABLE +
      " (zillow_transaction_id, transaction_id, lead_id, delete_flag) " +
      " values " +
      " (#{zillowTransactionId}, #{transactionId}, #{leadId}, #{deleteFlag}) "})
  int insert(ZillowTransactionLink po);

  @Update({"" +
      " update " +
      TABLE +
      " set " +
      " zillow_transaction_id = #{zillowTransactionId}, " +
      " transaction_id = #{transactionId}, " +
      " lead_id = #{leadId}, " +
      " delete_flag = #{deleteFlag} " +
      " where " +
      " id = #{id} "})
  int updateById(ZillowTransactionLink po);

  @Select({"" +
      " select " +
      ALL_COL +
      " from " +
      TABLE +
      " where " +
      " id = #{id} "})
  ZillowTransactionLink selectById(Serializable id);

  @Select({"" +
      " select " +
      ALL_COL +
      " from " +
      TABLE +
      " where " +
      " transaction_id = #{transactionId} and delete_flag = false "})
  ZillowTransactionLink selectByTrId(long transactionId);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select({"" +
      " select transaction_id " +
      " from " +
      TABLE +
      " where " +
      " transaction_id in (#{transactionIds}) and delete_flag = false "})
  List<Long> selectTrIdListByTrIds(@Param("transactionIds") List<Long> transactionIds);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Update({"" +
      " update " +
      TABLE +
      " set delete_flag = true " +
      " where transaction_id in (#{trIds}) "})
  int batchDeleteByTrIds(@Param("trIds") List<Long> trIds);

  @Update({"" +
      " update " +
      TABLE +
      " set delete_flag = true " +
      " where lead_id = #{leadId} "})
  int batchDeleteByLeadId(@Param("leadId") long leadId);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Update({"" +
      " update " +
      TABLE +
      " set delete_flag = true " +
      " where lead_id in (#{leadIds}) "})
  int batchDeleteByLeadIds(@Param("leadIds") List<Long> leadIds);

  @Update({"" +
      " update " +
      TABLE +
      " set lead_id = #{to} " +
      " where lead_id = #{from} "})
  int mergeLeadId(@Param("from") long from, @Param("to") long to);

  @Lang(MybatisExtendedLanguageDriver.class)
  @MapKey("transactionId")
  @Select({"" +
      " select " +
      ALL_COL +
      " from " +
      TABLE +
      " where " +
      " transaction_id in (#{transactionIds}) and delete_flag = false "})
  Map<Long, ZillowTransactionLink> selectLinksByTrIds(@Param("transactionIds") List<Long> transactionIds);


  @Update({"" +
      " update " +
      TABLE +
      " set " +
      " lead_id = #{leadId} " +
      " where " +
      " id = #{id} "})
  int updateLeadIdById(@Param("id") long id, @Param("leadId") long leadId);
}

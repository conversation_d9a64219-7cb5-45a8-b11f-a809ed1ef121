package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.constant.ImportType;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowInitSetting;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@DataSourceBase
public interface ZillowInitSettingDao {
  String table = " zillow_init_setting ";
  String all = " id, user_id, team_id, base_user_id, init_switch, sync, create_time, update_time,"
      + " ownership_scope, ownership_id, link_other_lead ";

  @Insert(" insert ignore into " + table
      + " (user_id, team_id, base_user_id, init_switch, sync, ownership_scope, ownership_id, link_other_lead) "
      + " value (#{userId}, #{teamId}, #{baseUserId}, #{initSwitch}, #{sync}, #{ownershipScope}, "
      + "#{ownershipId}, #{linkOtherLead})")
  void insert(ZillowInitSetting zillowInitSetting);

//  @Select(" select " + all
//      + " from " + table
//      + " where base_user_id = #{baseUserId}")
//  ZillowInitSetting getSetting(@Param("baseUserId") long baseUserId);

  @Select(" select " + all
      + " from " + table
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope}")
  ZillowInitSetting getSettingByOwnership(@Param("ownershipId") long ownershipId,
                                          @Param("ownershipScope") OwnershipScope ownershipScope);

//  @Update(" update " + table
//      + " set init_switch = #{initSwitch}, "
//      + " sync = #{sync} "
//      + " where base_user_id = #{baseUserId} ")
//  void operateSwitch(@Param("baseUserId") long baseUserId,
//                     @Param("initSwitch") boolean initSwitch,
//                     @Param("sync") ImportType sync);

  @Update(" update " + table
      + " set init_switch = #{initSwitch}, "
      + " sync = #{sync}, "
      + " link_other_lead = #{linkOtherLead} "
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope}")
  void operateSwitchByOwnership(@Param("initSwitch") boolean initSwitch,
                                @Param("sync") ImportType sync,
                                @Param("ownershipId") long ownershipId,
                                @Param("ownershipScope") OwnershipScope ownershipScope,
                                @Param("linkOtherLead") boolean linkOtherLead);
}

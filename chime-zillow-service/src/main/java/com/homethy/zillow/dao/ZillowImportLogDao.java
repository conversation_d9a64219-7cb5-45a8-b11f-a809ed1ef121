package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.vo.ZillowImportQuery;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSourceBase
public interface ZillowImportLogDao {
  String table = " zillow_import_log ";
  String all = " id, contact_id, import_contact, contact_info, status, result, add_request, "
      + "lead_id, binding_agent_id, assign_email, create_time, update_time ";

  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  @Insert(" insert into " + table
      + " set contact_id = #{contactId}, "
      + " import_contact = #{importContact}, "
      + " result = #{result}, "
      + " add_request = #{addRequest}, "
      + " lead_id = #{leadId}, "
      + " binding_agent_id = #{bindingAgentId}, "
      + " assign_email = #{assignEmail}, "
      + " contact_info = #{contactInfo}, "
      + " status = #{status}")
  void insertImport(ZillowImportLog zillowImportLog);

  @Update(" update " + table
      + " set contact_id = #{contactId}, "
      + " binding_agent_id = #{bindingAgentId}, "
      + " assign_email = #{assignEmail}, "
      + " contact_info = #{contactInfo} "
      + " where id = #{id}")
  void updateImport(ZillowImportLog zillowImportLog);

  @Select(" select " + all
      + " from " + table
      + " where id = #{id} ")
  ZillowImportLog getById(@Param("id") long id);

  @Update("update " + table
      + " set status = #{status}, "
      + " result = #{result} "
      + " where id = #{id}")
  void saveImportResult(@Param("id") long id, @Param("status") int status,
                        @Param("result") String result);

  @Update("update " + table
      + " set lead_id = #{leadId} "
      + " where id = #{id}")
  void saveLeadId(@Param("id") long id, @Param("leadId") long leadId);

  @SelectProvider(type = ZillowImportLogSqlProvider.class, method = "listMessages")
  List<ZillowImportLog> query(ZillowImportQuery query);

  @Update("update " + table
      + " set add_request = #{addRequest}, "
      + " lead_id = #{leadId} "
      + " where id = #{id}")
  void updateAddRequest(@Param("id") long id, @Param("addRequest") String addRequest,
                        @Param("leadId") long leadId);
}

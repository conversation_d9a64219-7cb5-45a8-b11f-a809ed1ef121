package com.homethy.zillow.dao;

import com.homethy.zillow.model.vo.ZillowImportQuery;

import org.apache.commons.lang.StringUtils;

import static org.apache.ibatis.jdbc.SqlBuilder.BEGIN;
import static org.apache.ibatis.jdbc.SqlBuilder.FROM;
import static org.apache.ibatis.jdbc.SqlBuilder.ORDER_BY;
import static org.apache.ibatis.jdbc.SqlBuilder.SELECT;
import static org.apache.ibatis.jdbc.SqlBuilder.SQL;
import static org.apache.ibatis.jdbc.SqlBuilder.WHERE;

public class ZillowImportLogSqlProvider {

  public String listMessages(ZillowImportQuery query) {
    BEGIN();
    SELECT(ZillowImportLogDao.all);
    FROM(ZillowImportLogDao.table);
    applyWhere(query);
    ORDER_BY(" id desc ");
    return SQL();
  }

  private void applyWhere(ZillowImportQuery query) {
    StringBuilder sb = new StringBuilder();
    sb.append(" 1=1 ");
    if (query.getLeadId() != 0) {
      sb.append(" and lead_id = #{leadId} ");
    }
    if (StringUtils.isNotEmpty(query.getAssignEmail())) {
      sb.append(" and assign_email = #{assignEmail} ");
    }
    if (query.getBindingAgentId() != 0 && query.getBaseId() != 0) {
      sb.append(" and binding_agent_id in (#{bindingAgentId}, #{baseId}) ");
    } else if (query.getBindingAgentId() != 0) {
      sb.append(" and binding_agent_id = #{bindingAgentId} ");
    } else if (query.getBaseId() != 0) {
      sb.append(" and binding_agent_id = #{baseId} ");
    }
    if (query.getStatus() != 0) {
      sb.append(" and status = #{status} ");
    }
    if (StringUtils.isNotEmpty(query.getContactId())) {
      sb.append(" and contact_id = #{contactId}");
    }
    if (!sb.toString().contains("and")) {
      sb.append(" and id = 0");
    }
    WHERE(sb.toString());
  }
}

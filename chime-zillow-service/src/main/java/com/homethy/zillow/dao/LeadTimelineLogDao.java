package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.dto.LeadTimelineLog;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @Date 2023/5/30
 */
@DataSourceBase
public interface LeadTimelineLogDao {

  String TABLE = " lead_timeline_log ";
  String COLUMN = " id, lead_id , timeline_id , ref_id , timeline_type ,timeline_time, note_id , "
      + "create_time, "
      + "update_time ";

  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  /*@Insert("""
      insert into lead_timeline_log set
        lead_id = #{leadId} ,
        timeline_id = #{timelineId} ,
        ref_id = #{refId},
        timeline_type = #{timelineType},
        timeline_time = #{timelineTime},
        note_id = #{noteId}
      on duplicate key update
        timeline_id = #{timelineId} ,
        ref_id = #{refId},
        note_id = #{noteId}
      """)*/
  @Insert("""
      insert ignore into lead_timeline_log set  
        lead_id = #{leadId} ,
        timeline_id = #{timelineId} ,
        ref_id = #{refId},
        timeline_type = #{timelineType},
        timeline_time = #{timelineTime},
        note_id = #{noteId}
      """)
  int insertOrUpdate(LeadTimelineLog leadTimelineLog);


  @Update("""
      update lead_timeline_log set note_id = #{noteId} where id = #{id}
      """)
  void updateTimelineLogByTimelineId(@Param("id") long id,
                                     @Param("noteId") String noteId);

  @Select("select " + COLUMN + " from " + TABLE + " where timeline_id = #{timelineId}")
  LeadTimelineLog getLeadTimelineLogByTimelineId(@Param("timelineId") long timelineId);

  @Select("select " + COLUMN + " from " + TABLE + " where ref_id = #{refId}")
  LeadTimelineLog getLeadTimelineLogByRefId(@Param("refId") long refId);

  @Select("select " + COLUMN + " from " + TABLE + " where lead_id = #{leadId} and "
      + "timeline_type=#{timelineType} and timeline_time = #{timelineTime}")
  LeadTimelineLog getLeadTimelineLogByLeadId(LeadTimelineLog leadTimelineLog);

  @Select("select ref_id from " + TABLE + " where lead_id = #{leadId} and "
      + "timeline_type=#{timelineType} and note_id = #{noteId}")
  Long getRefId(@Param("leadId") long leadId, @Param("timelineType") int timelineType,
                @Param("noteId") String noteId);

  @Select("select id from " + TABLE + " where note_id=#{noteId} limit 1")
  Long getIdByNoteId(@Param("noteId")String noteId);
}

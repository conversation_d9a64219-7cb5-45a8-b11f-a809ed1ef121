package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSourceBase
public interface KafkaConsumerRecordDao {
  @Options
  @Insert("<script>insert IGNORE into kfk_rec${prefix}_${p} (topic,offset) values"
      + " <foreach collection = 'offsets' item = 'item' separator=','>"
      + "(#{topic},#{item})</foreach></script>")
  void batchInsert(@Param("topic") String topic, @Param("p") int p, @Param("prefix") String prefix,
                   @Param("offsets") List<Long> offsets);

  @Select("select case when max(id)<min(id)+2020000 then 0 else min(id)+20000 end"
      + " from kfk_rec${prefix}_${p}")
  Long toDeleteId(@Param("p") int p, @Param("prefix") String prefix);

  @Delete("delete from kfk_rec${prefix}_${p} where id<#{from}")
  void delete(@Param("p") int p, @Param("prefix") String prefix, @Param("from") long from);

  @Update("""
      CREATE TABLE if not exists `kfk_rec${prefix}_${p}` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `topic` varchar(80) CHARACTER SET utf8mb4,
        `offset` bigint,
        PRIMARY KEY (`id`),
        UNIQUE KEY `u_id` (`topic`,`offset`)
      ) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4;
      """)
  void createTable(@Param("p") int p, @Param("prefix") String prefix);


  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("select distinct offset from kfk_rec${prefix}_${p} where topic=#{topic} and offset in"
      + "(#{offsets})")
  List<Long> listByTopicPtt(@Param("topic") String topic, @Param("p") int p,
                            @Param("prefix") String prefix,
                            @Param("offsets") List<Long> offsets);
}

package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.EncryptDataSource;
import com.homethy.zillow.model.constant.ZillowEmailLead;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@EncryptDataSource
public interface ZillowEmailLeadDao {
  String table = " zillow_email_lead ";
  String all = " id, zillow_email, zillow_agent_id, contact_id, lead_id, team_id, create_time, "
      + "update_time";

  @Insert(" insert IGNORE into " + table
      + " (zillow_email, zillow_agent_id, contact_id, lead_id, team_id) "
      + " values (#{zillowEmail}, #{zillowAgentId}, #{contactId}, #{leadId}, #{teamId}) ")
  int insert(ZillowEmailLead zillowEmailLead);

  @Select(" select " + all
      + " from " + table
      + " where zillow_agent_id = #{zillowAgentId} "
      + " and team_id = #{teamId} "
      + " and update_time < #{updateTime} "
      + " order by update_time desc limit 1000 ")
  List<ZillowEmailLead> query1000(@Param("zillowAgentId") String zillowAgentId,
                                  @Param("teamId") long teamId,
                                  @Param("updateTime") Date updateTime);

  @Update(" update " + table
      + " set update_time = CURRENT_TIMESTAMP "
      + " where lead_id = #{leadId} "
      + " and contact_id = #{contactId} ")
  void updateUpdateTime(@Param("leadId") long leadId, @Param("contactId") String contactId);
}

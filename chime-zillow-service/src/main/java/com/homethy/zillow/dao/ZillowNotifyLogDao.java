package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.po.ZillowNotifyLog;
import com.homethy.zillow.model.po.ZillowNotifyQuery;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSourceBase
public interface ZillowNotifyLogDao {

  String table = " zillow_notify_log ";
  String all = " id, contact_id, push_contact, result, transaction_id, lead_id, binding_agent_id,"
      + " create_time, update_time, zillow_transaction_id ,status , type,retry_num ";

  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  @Insert({"" + " insert into " + table
      + " (contact_id, push_contact, result, transaction_id, lead_id, binding_agent_id,type) "
      + " values " + " (#{contactId}, #{pushContact}, #{result}, #{transactionId}, #{leadId}, "
      + "#{bindingAgentId},#{type})" + " "})
  int insert(ZillowNotifyLog po);


  @Update("""
      update zillow_notify_log set status=#{status}, result=#{result},retry_num=#{retryNum} where id = #{id}
      """)
  int updateStatus(ZillowNotifyLog zillowNotifyLog);

  @Update("update " + table
      + " set status = #{status}, "
      + " result = #{result} "
      + " where id = #{id} ")
  void updateResult(@Param("id") long id, @Param("status") int status,
                    @Param("result") String result);

  @SelectProvider(type = ZillowNotifyLogSqlProvider.class, method = "listMessages")
  List<ZillowNotifyLog> query(ZillowNotifyQuery zillowNotifyQuery);
}

package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.EncryptDataSource;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@EncryptDataSource
public interface ZillowOauth2TokenEncryptDao {
  String table = " zillow_oauth2_token ";
  String all = " id, access_token, refresh_token, id_token, zillow_email, zillow_agent_id, "
      + "agent_id, base_id, refresh_time, expires_in, delete_flag, create_time, update_time"
      + ",integration_id, ownership_id, ownership_scope ";

  @Select(" select " + all
      + " from " + table
      + " where ownership_id = #{baseId} "
      + " and ownership_scope = 'PERSONAL'"
      + " and delete_flag = 0 ")
  ZillowOauth2Token getTokenByBaseId(@Param("baseId") long baseId);

  @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
  @Insert(" insert into " + table
      + " set access_token = #{accessToken}, "
      + " refresh_token = #{refreshToken}, "
      + " id_token = #{idToken}, "
      + " zillow_email = #{zillowEmail}, "
      + " zillow_agent_id = #{zillowAgentId}, "
      + " agent_id = #{agentId}, "
      + " base_id = #{baseId}, "
      + " refresh_time = #{refreshTime}, "
      + " expires_in = #{expiresIn}, "
      + " integration_id=#{integrationId},"
      + " ownership_id = #{ownershipId}, "
      + " ownership_scope = #{ownershipScope}, "
      + " delete_flag = 0 ")
  void insertToken(ZillowOauth2Token zillowOauth2Token);

  @Update(" update " + table
      + " set access_token = #{accessToken}, "
      + " refresh_time = #{refreshTime}, "
      + " expires_in = #{expiresIn}, "
      + " refresh_token = #{refreshToken}, "
      + " id_token = #{idToken}, "
      + " agent_id = #{agentId}, "
      + " zillow_agent_id = #{zillowAgentId}, "
      + " zillow_email = #{zillowEmail}, "
      + " integration_id=#{integrationId}, "
      + " ownership_id = #{ownershipId}, "
      + " ownership_scope = #{ownershipScope}, "
      + " delete_flag = 0 "
      + " where id = #{id}")
  void updateToken(ZillowOauth2Token zillowOauth2Token);

  @Update(" update " + table
      + " set access_token = #{accessToken}, "
      + " refresh_time = #{refreshTime}, "
      + " expires_in = #{expiresIn} "
      + " where id = #{id}")
  void updateAccessToken(ZillowOauth2Token token);

  @Select(" select " + all
      + " from " + table
      + " where zillow_email = #{zillowEmail} "
      + " and delete_flag = 0")
  ZillowOauth2Token getTokenByZillowEmail(@Param("zillowEmail") String zillowEmail);

  @Select(" select " + all
      + " from " + table
      + " where zillow_email = #{zillowEmail} "
      + " and delete_flag = 1 "
      + " order by update_time desc limit 1 ")
  ZillowOauth2Token getOtherDelTokenOne(@Param("zillowEmail") String zillowEmail);

  @Select(" select " + all
      + " from " + table
      + " where zillow_agent_id = #{zillowAgentId}"
      + " and delete_flag = 0")
  ZillowOauth2Token getByZillowAgentId(@Param("zillowAgentId") String zillowAgentId);

  @Select("select " + all
      + " from " + table
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope} "
      + " and delete_flag = 0 ")
  ZillowOauth2Token queryByOwnership(@Param("ownershipId") long ownershipId,
                                     @Param("ownershipScope") OwnershipScope ownershipScope);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("select " + all
      + " from " + table
      + " where ownership_id in (#{ownershipIds}) "
      + " and ownership_scope in (#{ownershipScopes}) "
      + " and delete_flag = 0 ")
  List<ZillowOauth2Token> queryByOwnerships(@Param("ownershipIds") List<Long> ownershipIds,
                                            @Param("ownershipScopes") List<OwnershipScope> ownershipScopes);

  @Select("select " + all
      + " from " + table
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope} "
      + " order by create_time desc")
  List<ZillowOauth2Token> queryByOwnershipDel(@Param("ownershipId") long ownershipId,
                              @Param("ownershipScope") OwnershipScope ownershipScope);

  @Select("select " + all
      + " from " + table
      + " where integration_id=#{integrationId} and delete_flag=0 limit 1")
  ZillowOauth2Token getByIntegrationId(String integrationId);
}

package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSourceBase
public interface ZillowLeadDao {

  String table = " zillow_lead ";
  String all = " id, contact_id, lead_id, zillow_status, lead_last_email, team_id, "
      + "mobile, work, home, delete_flag, create_time, update_time, ownership_scope, ownership_id ";

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("" +
      " SELECT " +
      " lead_id " +
      " FROM " +
      table +
      " where " +
      " lead_id in (#{leadIds}) and delete_flag = false ")
  List<Long> selectByLeadIds(@Param("leadIds") List<Long> leadIds);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("" +
      " SELECT " +
      all +
      " FROM " +
      table +
      " where " +
      " lead_id in (#{leadIds}) and delete_flag = false ")
  List<ZillowLead> selectLeadsByLeadIds(@Param("leadIds") List<Long> leadIds);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("" +
      " SELECT " +
      all +
      " FROM " +
      table +
      " where " +
      " lead_id in (#{leadIds}) and team_id = #{teamId} and delete_flag = false ")
  List<ZillowLead> selectLeadsByLeadIdsAndTeam(@Param("leadIds") List<Long> leadIds,
                                               @Param("teamId") long teamId);

  @Insert(" insert into " + table
      + " set contact_id = #{contactId}, "
      + " lead_id = #{leadId}, "
      + " zillow_status = #{zillowStatus}, "
      + " lead_last_email = #{leadLastEmail}, "
      + " team_id = #{teamId}, "
      + " mobile = #{mobile}, "
      + " work = #{work}, "
      + " home = #{home}, "
      + " ownership_scope = #{ownershipScope}, "
      + " ownership_id = #{ownershipId}, "
      + " delete_flag = false ")
  void insert(ZillowLead zillowLead);

  @Insert({
      "<script>",
      "INSERT INTO ",
      table,
      " ( ",
      "    contact_id, lead_id, zillow_status, lead_last_email, team_id, ",
      "    mobile, work, home, ownership_scope, ownership_id, delete_flag ",
      ") ",
      "VALUES ",
      "<foreach collection='list' item='item' separator=','>",
      "    (",
      "        #{item.contactId}, #{item.leadId}, #{item.zillowStatus}, #{item.leadLastEmail}, #{item.teamId}, ",
      "        #{item.mobile}, #{item.work}, #{item.home}, #{item.ownershipScope}, #{item.ownershipId}, false",
      "    )",
      "</foreach>",
      "</script>"
  })
  void batchInsert(@Param("list") List<ZillowLead> zillowLeads);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select("" +
      " update " +
      table +
      " set delete_flag = true " +
      " where " +
      " lead_id in (#{leadIds}) and delete_flag = false ")
  void deleteByLeadIds(@Param("leadIds") List<Long> leadIds);


  @Update("update " + table
      + " set delete_flag = true "
      + " where contact_id = #{contactId} "
      + " and team_id = #{teamId} ")
  void deleteByContactId(@Param("contactId") String contactId, @Param("teamId") long teamId);

  @Select(" select " + all
      + " from " + table
      + " where contact_id = #{contactId}"
      + " and team_id = #{teamId}"
      + " and delete_flag = false")
  ZillowLead selectByContactId(@Param("contactId") String contactId, @Param("teamId") long teamId);

  @Select("select zillow_status from " + table + " where lead_id = #{leadId} and delete_flag=0")
  Integer getZillowStatusByLeadId(@Param("leadId") long leadId);

  @Select("select " + all + " from " + table + " where lead_id = #{leadId} and delete_flag=0")
  ZillowLead getZillowLeadByLeadId(@Param("leadId") long leadId);

  @Select("select " + all + " from " + table + " where lead_id = #{leadId} and delete_flag=0")
  List<ZillowLead> getZillowLeadsByLeadId(@Param("leadId") long leadId);

  @Update("update " + table + " set zillow_status=#{statusCode} where lead_id = #{leadId}")
  int updateZillowStatusByLeadId(@Param("leadId") long leadId, @Param("statusCode") int statusCode);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Update(" update " + table
      + " set delete_flag = true "
      + " where ownership_id in (#{ownershipIds}) "
      + " and ownership_scope = #{ownershipScope} "
      + " and delete_flag = false "
      + " limit 1000")
  int deleteByPersonalAgentIdsLimit(@Param("ownershipIds") List<Long> ownershipIds,
                                    @Param("ownershipScope") OwnershipScope ownershipScope);

  @Update(" update " + table
      + " set lead_last_email = #{leadLastEmail}, "
      + " zillow_status = #{zillowStatus}"
      + " where id = #{id}")
  void updateLastEmail(@Param("leadLastEmail") String leadLastEmail,
                       @Param("id") long id,
                       @Param("zillowStatus") int zillowStatus);

  @Update("update " + table
      + " set mobile = #{mobile}, "
      + " work = #{work}, "
      + " home = #{home} "
      + " where id = #{id}")
  void updateLastPhones(@Param("mobile") String mobile,
                        @Param("work") String work,
                        @Param("home") String home,
                        @Param("id") long id);

  @Update(" update " + table
      + " set mobile = #{phone} "
      + " where id = #{id}")
  void updateLastMobile(@Param("phone") String phone, @Param("id") long id);

  @Update(" update " + table
      + " set work = #{phone} "
      + " where id = #{id}")
  void updateLastWork(@Param("phone") String phone, @Param("id") long id);

  @Update(" update " + table
      + " set home = #{phone} "
      + " where id = #{id}")
  void updateLastHome(@Param("phone") String phone, @Param("id") long id);

  @Update(" update " + table
      + " set lead_last_email = #{leadLastEmail} "
      + " where id = #{id}")
  void updateLastEmailByEmail(@Param("leadLastEmail") String leadLastEmail, @Param("id") long id);

  @Select(" select " + all
      + " from " + table
      + " where lead_id = #{leadId} "
      + " and team_id = #{teamId} "
      + " and delete_flag = false ")
  ZillowLead getZillowLead(@Param("leadId") long leadId, @Param("teamId") long teamId);

  @Select("select lead_id from " + table + " where contact_id=#{contactId} and delete_flag=0 limit 1")
  Long leadByContact(@Param("contactId") String contactId);

  @Update(" update " + table
      + " set delete_flag = true "
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope} "
      + " and delete_flag = false "
      + " limit 1000")
  int deleteByOwnership(@Param("ownershipId") long ownershipId,
                        @Param("ownershipScope") OwnershipScope ownershipScope);

  @Select(" select " + all
      + " from " + table
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope} "
      + " and id > #{startId} "
      + " and delete_flag = false "
      + " limit 1000 ")
  List<ZillowLead> queryByOwnershipLimit(@Param("ownershipId") long ownershipId,
                                         @Param("ownershipScope") OwnershipScope ownershipScope,
                                         @Param("startId") long startId);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Select(" select " + all
      + " from " + table
      + " where (lead_id in (#{leadIds}) "
      + " or contact_id in (#{contactIds})) "
      + " and team_id = #{teamId} "
      + " and delete_flag = false ")
  List<ZillowLead> queryByLeadIdsAndContactIds(@Param("leadIds") List<Long> leadIds,
                                               @Param("contactIds") List<String> contactIds,
                                               @Param("teamId") long teamId);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Update(" update " + table
      + " set delete_flag = true "
      + " where id in (#{ids})")
  void deleteByIds(@Param("ids") List<Long> ids);
}

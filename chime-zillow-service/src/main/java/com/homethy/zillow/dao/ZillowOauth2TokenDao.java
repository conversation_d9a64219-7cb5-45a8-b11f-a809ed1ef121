package com.homethy.zillow.dao;

import com.homethy.zillow.annotation.DataSourceBase;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.util.MybatisExtendedLanguageDriver;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataSourceBase
public interface ZillowOauth2TokenDao {

  String table = " zillow_oauth2_token ";
  String all = " id, access_token, refresh_token, id_token, zillow_email, zillow_agent_id, "
      + "agent_id, base_id, refresh_time, expires_in, delete_flag, create_time, update_time"
      + ",integration_id, ownership_id, ownership_scope ";

  @Update(" update " + table
      + " set delete_flag = 1 "
      + " where base_id = #{baseId}")
  void deleteToken(@Param("baseId") long baseId);

  @Update(" update " + table
      + " set delete_flag = 1 "
      + " where ownership_id = #{ownershipId} "
      + " and ownership_scope = #{ownershipScope} ")
  void deleteByOwnership(@Param("ownershipId") long ownershipId,
                         @Param("ownershipScope") OwnershipScope ownershipScope);

  @Lang(MybatisExtendedLanguageDriver.class)
  @Delete(" delete from " + table
      + " where id in (#{ids})")
  void removeTokens(@Param("ids") List<Long> ids);

  @Select("select base_id from " + table + " where integration_id is null and delete_flag=0")
  List<Long> baseIdsWithoutIntegration();

  @Update("update " + table + " set integration_id=#{integrationId} where base_id=#{baseId}")
  int fillIntegration(@Param("baseId") long baseId, @Param("integrationId") String integrationId);
}

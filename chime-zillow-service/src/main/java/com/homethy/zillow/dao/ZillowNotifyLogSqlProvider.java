package com.homethy.zillow.dao;

import com.homethy.zillow.model.po.ZillowNotifyQuery;

import org.apache.commons.lang.StringUtils;

import static org.apache.ibatis.jdbc.SqlBuilder.BEGIN;
import static org.apache.ibatis.jdbc.SqlBuilder.FROM;
import static org.apache.ibatis.jdbc.SqlBuilder.ORDER_BY;
import static org.apache.ibatis.jdbc.SqlBuilder.SELECT;
import static org.apache.ibatis.jdbc.SqlBuilder.SQL;
import static org.apache.ibatis.jdbc.SqlBuilder.WHERE;

public class ZillowNotifyLogSqlProvider {
  public String listMessages(ZillowNotifyQuery query) {
    BEGIN();
    SELECT(ZillowNotifyLogDao.all);
    FROM(ZillowNotifyLogDao.table);
    applyWhere(query);
    ORDER_BY(" id desc ");
    return SQL();
  }

  private void applyWhere(ZillowNotifyQuery query) {
    StringBuilder sb = new StringBuilder();
    sb.append(" 1=1 ");
    if (query.getLeadId() != 0) {
      sb.append(" and lead_id = #{leadId} ");
    }
    if (query.getTransactionId() != 0) {
      sb.append(" and transaction_id = #{transactionId} ");
    }
    if (query.getBindingAgentId() != 0) {
      sb.append(" and binding_agent_id = #{bindingAgentId} ");
    }
    if (StringUtils.isNotEmpty(query.getContactId())) {
      sb.append(" and contact_id = #{contactId}");
    }
    if (!sb.toString().contains("and")) {
      sb.append(" and id = 0");
    }
    WHERE(sb.toString());
  }
}

package com.homethy.zillow.handler;

import com.homethy.microservice.client.model.LeadNoteBo;
import com.homethy.zillow.dao.LeadTimelineLogDao;
import com.homethy.zillow.model.constant.ZillowImportLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class NoteUpdatedHandler extends NoteHandler {
  @Autowired
  LeadTimelineLogDao leadTimelineLogDao;

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    String zillowNoteId = getNoteId(data);
    Long noteId = leadTimelineLogDao.getRefId(data.getZillowLead().getLeadId(), NOTE_TIMELINE_TYPE,
        zillowNoteId);
    if (noteId == null) {
      log.info("no noteId from log. lead:{},zillowNoteId:{}", data.getZillowLead().getLeadId(),
          zillowNoteId);
    }
    LeadNoteBo note = parseNode(data);
    if (note == null) {
      return;
    }
    note.setId(noteId == null ? 0 : noteId);
    addOrUpdate(note);
  }
}

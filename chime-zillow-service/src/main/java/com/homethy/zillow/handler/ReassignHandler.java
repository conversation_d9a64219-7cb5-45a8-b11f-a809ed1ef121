package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.service.ZillowLeadService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReassignHandler implements EventHandler {
  @Autowired
  private ZillowLeadService zillowLeadService;

  @Override
  public boolean leadRequired() {
    return true;
  }

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    zillowLeadService.reassign(data.getToken(), data.getZillowLead().getLeadId(), data.getContact());
  }

  @Override
  public String[] getAgentIdPath() {
    return new String[]{"ownerAgent", "zillowUserId"};
  }

  @Override
  public void fill(ZillowContactInfo contactInfo, JsonNode data) {

  }
}

package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.service.ZillowStatusService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class StatusChangeHandler implements EventHandler {
  @Autowired
  private ZillowStatusService zillowStatusService;

  @Override
  public boolean leadRequired() {
    return true;
  }

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    if (data.getContact().getStatus() == null) {
      log.info("zillow status is null");
      return;
    }
    zillowStatusService.updateZillowStatusByLeadId(
        data.getZillowLead().getLeadId(), data.getContact().getStatus().getId());
  }

  @Override
  public String[] getAgentIdPath() {
    return new String[]{"agent", "zillowUserId"};
  }

  @Override
  public void fill(ZillowContactInfo contactInfo, JsonNode data) {

  }
}

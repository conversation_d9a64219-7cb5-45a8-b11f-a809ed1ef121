package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.model.constant.zillow.ZillowPhone;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.util.JsonUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CreateLeadHandler implements EventHandler {
  @Autowired
  private ZillowLeadService zillowLeadService;

  @Override
  public boolean leadRequired() {
    return false;
  }

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    zillowLeadService.importZillowLead(
        data.getContact(), data.getOwnershipInfo(), importLog.getId(), false, data.getBindTokenUserId());
  }

  @Override
  public String[] getAgentIdPath() {
    return new String[]{"ownerAgent", "zillowUserId"};
  }

  @Override
  public void fill(ZillowContactInfo contactInfo, JsonNode data) {
    JsonNode phoneListNode = JsonUtil.asTargetNode(data, "contactInfo", "phoneNumbers");
    if (phoneListNode == null) {
      log.info("not find phone list");
      return;
    }
    contactInfo.setPhones(JsonUtil.toList(phoneListNode.toString(), ZillowPhone.class));
  }
}

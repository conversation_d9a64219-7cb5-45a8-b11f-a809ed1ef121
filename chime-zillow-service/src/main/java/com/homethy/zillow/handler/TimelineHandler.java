package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.microservice.client.lead.LeadKernelService;
import com.homethy.microservice.client.lead.apiV2.LeadInquiriesService;
import com.homethy.microservice.client.lead.apiV3.LeadPropertyService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadInquireUpdateRequestModelBo;
import com.homethy.microservice.client.model.LeadInquiriesBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadTimelineAddRequestBo;
import com.homethy.microservice.client.model.LocationElemBo;
import com.homethy.microservice.client.timeline.LeadTimelineAddClient;
import com.homethy.microservice.constant.ErrorCodeEnum;
import com.homethy.persistence.domain.LeadTimeline;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.util.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class TimelineHandler implements EventHandler {

  @Autowired
  private ZillowLeadService zillowLeadService;
  @Autowired
  LeadKernelService leadKernelService;

  @Autowired
  LeadPropertyService leadPropertyService;

  @Autowired
  private LeadInquiriesService leadInquiriesService;

  @Autowired
  LeadTimelineAddClient timelineAddClient;
  @Autowired
  UserManager userManager;

  @Override
  public boolean leadRequired() {
    return true;
  }

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    String title = getTimelineTitle();
    LeadBo lead = leadKernelService.getLeadById(data.getZillowLead().getLeadId());
    if (lead == null) {
      log.info("ignore for no lead:{}", data.getZillowLead().getLeadId());
      return;
    }
    log.info("dealProperty for lead:{},contactId:{}", lead.getId(),
        data.getContact().getContactId());
    addTimeline80(lead, data.getRoot(), title, importLog);
    JsonNode pNode = data.getRoot().get("property");
    if (pNode != null && !pNode.isNull()) {
      LeadPropertyVoBo property = new LeadPropertyVoBo();
      String state = JsonUtil.asText(pNode, "state");
      String city = JsonUtil.asText(pNode, "city");
      String zip = JsonUtil.asText(pNode, "zip");
      String address = JsonUtil.asText(pNode, "address");
      property.setCity(city);
      property.setZipcode(zip);
      property.setState(state);
      property.setStreetAddress(address);
      if (StringUtils.isNotBlank(property.getStreetAddress()) || StringUtils.isNotBlank(property.getCity())
          || StringUtils.isNotBlank(property.getState()) || StringUtils.isNotBlank(property.getZipcode())) {
        property.setLabel("High Interest");
        property.setPrice(JsonUtil.getNumber(pNode, "listingPrice").longValue());
        property.setBedRooms(NumberUtils.toInt(JsonUtil.asText(pNode, "beds"), 0));
        property.setBathRooms(NumberUtils.toFloat(JsonUtil.asText(pNode, "baths"), 0));
        property.setNote("MLS number:" + JsonUtil.asText(pNode, "MLSNumber")
            + "\nListing Status:" + JsonUtil.asText(pNode, "ListingStatus")
            + "\nZillow Property URL:" + JsonUtil.asText(pNode, "ZillowPropertyURL"));
        property.setLeadUserId(lead.getLeadUserId());
        String result = leadPropertyService.addNewPropertyInternal(property);
        if (isAlreadyExist(result)) {
          log.info("property already exist. lead:{},address:{},result:{}",
              lead.getId(), property.getStreetAddress(), result);
        }
        if (StringUtils.isNotBlank(city) || StringUtils.isNotBlank(state)) {
          setCriteria(lead, property, state, city, zip);
        }
      }
    }
    long operUserId = userManager.getOperUserId(lead.getTeamId(), data.getOwnershipInfo());

    zillowLeadService.addTagToLead(data.getContact(), data.getZillowLead().getLeadId(),
        userManager.getUserById(operUserId));
  }

  String merge(String... strs) {
    return Arrays.stream(strs).filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
  }

  boolean isAlreadyExist(String result) {
    String code = String.valueOf(ErrorCodeEnum.PROPERTY_ALREADY_EXIST.getErrorCode());
    if (StringUtils.isNotBlank(result) && result.contains(code)) {
      JsonNode resultNode = JacksonUtils.toJsonNode(result);
      return code.equals(JsonUtil.asText(resultNode, "status", "code"));
    }
    return false;
  }


  private void setCriteria(LeadBo lead, LeadPropertyVoBo property, String state, String city,
                           String zip) {

    LeadInquiriesBo inquiry = leadInquiriesService.getByLeadUserId(lead.getLeadUserId());
    if (inquiry == null) {
      inquiry = new LeadInquiriesBo();
    }
    if (property.getBathRooms() > 0) {
      inquiry.setBathroomsMin(String.valueOf(property.getBathRooms()));
    }
    if (property.getBedRooms() > 0) {
      inquiry.setBedroomsMin(property.getBedRooms());
    }
    if (property.getPrice() > 0) {
      int w = 10000;
      inquiry.setPriceMin(property.getPrice() / w * w);
      inquiry.setPriceMax(inquiry.getPriceMin() + w);
    }
    List<LocationElemBo> locations = new ArrayList<>();
    if (inquiry.getLocations() != null) {
      locations.addAll(inquiry.getLocations());
    }
    if (StringUtils.isNotBlank(city)) {
      LocationElemBo locationElemBo = new LocationElemBo(LocationElemBo.TYPECITY, city, state);
      if (!locations.contains(locationElemBo)) {
        locations.add(locationElemBo);
      }
    }
    if (StringUtils.isNotBlank(zip)) {
      LocationElemBo locationElemBo = new LocationElemBo(LocationElemBo.TYPEZIPCODE, zip, state);
      if (!locations.contains(locationElemBo)) {
        locations.add(locationElemBo);
      }
    }
    inquiry.setLocations(locations);
    log.info("property: {}, inquiry: {}", JacksonUtils.toJson(property), JacksonUtils.toJson(inquiry));
    leadInquiriesService.updateLeadInquiries(new LeadInquireUpdateRequestModelBo(lead, inquiry,
        null));
  }

  @Override
  public String[] getAgentIdPath() {
    return new String[]{"ownerAgent", "zillowUserId"};
  }

  @Override
  public void fill(ZillowContactInfo contactInfo, JsonNode data) {
    contactInfo.setChannel(data.get("channel").asText());
    contactInfo.setContactMessage(data.get("contactMessage").asText());
  }

  void addTimeline80(LeadBo lead, JsonNode root, String title, ZillowImportLog importLog) {
    LeadTimelineAddRequestBo timeline = new LeadTimelineAddRequestBo();
    timeline.setLeadId(lead.getId());
    timeline.setAgentId(importLog.getBindingAgentId());
    timeline.setTimelineType(LeadTimeline.LeadTimelineTypeEnum.SystemInfo.getType());
    timeline.setRefId(lead.getId());
    timeline.setFromId(timeline.getAgentId());
    timeline.setToId(timeline.getLeadId());
    timeline.setFromType(LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType());
    timeline.setToType(LeadTimeline.LeadTimelineFromToTypeEnum.Lead.getType());
    timeline.setTimelineTime(new Date());
    timeline.setContentMap(buildContent(root, title));
    try {
      log.debug("add timeline:{}", timeline);
      timelineAddClient.addLeadTimeline(timeline);
    } catch (Exception e) {
      log.warn("failed add timeline:{}", timeline, e);
    }
  }


  @NotNull
  private String buildContent(JsonNode root, String title) {
    StringBuilder sb = new StringBuilder();
    sb.append("The customer has an inquiry " + title + "\n");
    JsonNode property = root.get("property");
    property.fieldNames().forEachRemaining(name -> {
      addByName(sb, property, name);
    });

    for (String name : new String[]{"note", "channel", "contactMessage", "requestedTourDate",
        "creationDate", "modifiedDate"}) {
      addByName(sb, root, name);
    }
    return JacksonUtils.toJson(Map.of("content", sb, "zillow", "true"));
  }

  private void addByName(StringBuilder sb, JsonNode property, String name) {
    String s = JsonUtil.asText(property, name);
    if (StringUtils.isNotBlank(s) && !"0".equals(s) && !"null".equalsIgnoreCase(s)) {
      sb.append(name).append(": ").append(s).append("\n");
    }
  }

  protected abstract String getTimelineTitle();
}

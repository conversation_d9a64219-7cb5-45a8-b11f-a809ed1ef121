package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class EventData {
  private ZillowContactInfo contact;
  private JsonNode root;
  private ZillowLead zillowLead;
  private OwnershipInfo ownershipInfo;
  private ZillowOauth2Token token;
  private long bindTokenUserId;
}

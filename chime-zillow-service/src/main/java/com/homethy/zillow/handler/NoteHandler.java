package com.homethy.zillow.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.microservice.client.lead.LeadKernelService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadNoteBo;
import com.homethy.microservice.client.note.LeadNoteServiceClient;
import com.homethy.persistence.domain.LeadTimeline;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.util.JsonUtil;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class NoteHandler implements EventHandler {
  @Autowired
  LeadNoteServiceClient leadNoteServiceClient;
  @Autowired
  LeadKernelService leadService;
  @Autowired
  UserManager userManager;

  static final int NOTE_TIMELINE_TYPE = LeadTimeline.LeadTimelineTypeEnum.AddNote.getType();

  @Override
  public boolean leadRequired() {
    return true;
  }

  String getNoteId(EventData data) {
    return JsonUtil.asText(data.getRoot(), "noteId");
  }

  long addOrUpdate(LeadNoteBo node) {
    return leadNoteServiceClient.addOrUpdateNode(node, false);
  }

  LeadNoteBo parseNode(EventData data) {
    LeadNoteBo node = new LeadNoteBo();
    node.setNote(data.getContact().getContactMessage());
    node.setCreateTime(new Date());
    node.setLastUpdate(node.getCreateTime());
    node.setLeadId(data.getZillowLead().getLeadId());
    LeadBo lead = leadService.getLeadById(data.getZillowLead().getLeadId());
    if (lead == null) {
      log.info("lead is null, leadId: {}", data.getZillowLead().getLeadId());
      return null;
    }
    long operUserId = userManager.getOperUserId(lead.getTeamId(), data.getOwnershipInfo());
    node.setCreatorId(operUserId);
    node.setLastEditorId(node.getCreatorId());
    node.setTimelineParams(Map.of("zillow", "true"));
    return node;
  }

  @Override
  public String[] getAgentIdPath() {
    return new String[]{"agent", "zillowUserId"};
  }

  @Override
  public void fill(ZillowContactInfo contactInfo, JsonNode data) {
    contactInfo.setContactMessage(JsonUtil.asText(data, "text"));
  }
}

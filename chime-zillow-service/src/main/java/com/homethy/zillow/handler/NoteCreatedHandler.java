package com.homethy.zillow.handler;

import com.homethy.microservice.client.model.LeadNoteBo;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.dao.LeadTimelineLogDao;
import com.homethy.zillow.model.constant.ZillowImportLog;
import com.homethy.zillow.model.dto.LeadTimelineLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class NoteCreatedHandler extends NoteHandler {

  @Autowired
  LeadTimelineLogDao leadTimelineLogDao;

  @Override
  public void handle(EventData data, ZillowImportLog importLog) {
    String noteId = getNoteId(data);
    if (leadTimelineLogDao.getIdByNoteId(noteId) != null) {
      log.info("ignore existed note:{}", noteId);
      return;
    }
    LeadNoteBo note = parseNode(data);
    if (note == null) {
      return;
    }
    long refId = addOrUpdate(note);
    LeadTimelineLog timelineLog = LeadTimelineLog.builder().leadId(note.getLeadId())
        .refId(refId).noteId(noteId)
        .timelineType(NOTE_TIMELINE_TYPE)
        .timelineTime(note.getLastUpdate().getTime()).build();
    if (log.isDebugEnabled()) {
      log.debug("timelineLog: {}, noteId: {}", JacksonUtils.toJson(timelineLog), timelineLog.getNoteId());
    }
    leadTimelineLogDao.insertOrUpdate(timelineLog);
  }


}

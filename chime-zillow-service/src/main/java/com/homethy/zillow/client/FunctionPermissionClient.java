package com.homethy.zillow.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "${user-permission.service.id:user-permission}",
    url = "${user-permission.url:user-permission.crm.svc:8080}")
public interface FunctionPermissionClient {

  @GetMapping("/functions/{userId}")
  Map<String, Boolean> functions(@PathVariable("userId") Long userId,
                                 @RequestParam("functions") List<String> functions);
}

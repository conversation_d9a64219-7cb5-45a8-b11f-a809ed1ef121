package com.homethy.zillow.client;


import com.homethy.microservice.client.agent.model.AgentEsBo;
import com.homethy.microservice.client.agent.model.AgentQueryBo;
import com.homethy.microservice.client.agent.model.SearchResultBo;
import com.homethy.microservice.client.agent.search.AgentSearchClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AgentSearchManager {
  @Autowired
  private AgentSearchClient agentSearchClient;

  public List<AgentEsBo> searchByEmails(long teamId, List<String> emails) {

    AgentQueryBo query = new AgentQueryBo();
    query.setAccountsAccurate(emails);
    query.setTeamId(teamId);
    SearchResultBo search = agentSearchClient.search(query);
    if (search == null || search.getAgentEsVoList() == null || search.getAgentEsVoList().isEmpty()) {
      return null;
    }
    return search.getAgentEsVoList();
  }
}

package com.homethy.zillow.client;

import com.homethy.fub.configuration.RateFeignConfig;
import com.homethy.zillow.model.constant.fub.*;
import com.homethy.zillow.model.po.FubCall;
import com.homethy.zillow.model.po.FubNote;
import com.homethy.zillow.model.po.FubNoteVo;
import com.homethy.zillow.model.po.FubPeopleInfo;
import com.homethy.zillow.model.po.FubTextMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "https://api.followupboss.com", name = "FUB", configuration =
    RateFeignConfig.class)
public interface FubClient {

  @GetMapping("/v1/people?sort=created&includeTrash=false&includeUnclaimed=false")
  FubPeopleQueryResult getPeopleByPage(@RequestParam("limit") int limit,
      @RequestParam("offset") int offset,
      @RequestParam("assignedUserId") long assignedUserId,
      @RequestHeader("Authorization") String token);

  @GetMapping("/v1/people?sort=created&includeTrash=false&includeUnclaimed=false&fields=id")
  FubPeopleQueryResult getPeopleByPage(@RequestParam("limit") int limit,
      @RequestParam("next") String next,
      @RequestParam("assignedUserId") long assignedUserId,
      @RequestHeader("Authorization") String token);

  @GetMapping("/v1/me")
  FubUserInfo getMe(@RequestHeader("Authorization") String token);

  @GetMapping("/v1/users")
  FubUserQueryResult getUsersByEmail(@RequestHeader("Authorization") String token, @RequestParam(
      "email") String email);

  @GetMapping("/v1/users/{userId}")
  FubUserInfo getUserById(@RequestHeader("Authorization") String token,
      @PathVariable("userId") long userId);

  @PostMapping("/v1/webhooks")
  void addWebhook(@RequestHeader("Authorization") String token,
      @RequestBody FubWebHook webhookInfo);

  @GetMapping("/v1/webhooks")
  FubWebhookResult getWebhooks(@RequestHeader("Authorization") String token, @RequestParam("limit"
  ) int limit);

  @DeleteMapping("/v1/webhooks/{webhookId}")
  void deleteWebhook(@RequestHeader("Authorization") String token,
      @PathVariable("webhookId") int webhookId);

  @PostMapping("/v1/appointments")
  FubAppointment createAppointment(@RequestHeader("Authorization") String token,
      @RequestBody FubAppointmentRequest fubAppointment);

  @PutMapping("/v1/appointments/{appointmentId}")
  FubAppointment updateAppointment(@RequestHeader("Authorization") String token,
      @PathVariable("appointmentId") long appointmentId,
      @RequestBody FubAppointmentRequest fubAppointment);

  @GetMapping("/v1/appointments/{appointmentId}")
  FubAppointment getAppointment(@RequestHeader("Authorization") String token,
      @PathVariable("appointmentId") long appointmentId);

  @DeleteMapping("/v1/appointments/{appointmentId}")
  void deleteAppointment(@RequestHeader("Authorization") String token,
      @PathVariable("appointmentId") long appointmentId);

  @GetMapping("/v1/tasks/{taskId}")
  FubTask getTask(@RequestHeader("Authorization") String token,
      @PathVariable("taskId") long taskId);

  @PostMapping("/v1/tasks")
  FubTask createTask(@RequestHeader("Authorization") String token,
                     @RequestBody FubTaskRequest fubTask);

  @PutMapping("/v1/tasks/{taskId}")
  FubTask updateTask(@RequestHeader("Authorization") String token,
                     @PathVariable("taskId") long taskId, @RequestBody FubTaskRequest fubTask);

  @PostMapping("/v1/people")
  FubPeople addLead(@RequestHeader("Authorization") String token,
                    @RequestBody FubPeopleInfo fubPeopleInfo);

  @PutMapping("/v1/people/{id}")
  void updateLead(@RequestHeader("Authorization") String token,
      @PathVariable("id") long id, @RequestBody FubPeopleInfo fubPeopleInfo);

  @DeleteMapping("/v1/tasks/{taskId}")
  void deleteTask(@RequestHeader("Authorization") String token,
      @PathVariable("taskId") long taskId);

  @GetMapping("/v1/peopleRelationships/{id}")
  FubFamilyMember getPeopleRelationship(@RequestHeader("Authorization") String token,
      @PathVariable("id") long id);

  @GetMapping("/v1/stages/{id}")
  FubStage getStage(@RequestHeader("Authorization") String token,
      @PathVariable("id") long id);

  @GetMapping("/v1/stages")
  FubStageResponse getStages(@RequestHeader("Authorization") String token,
      @RequestParam("limit") int limit, @RequestParam("offset") int offset);


  @PutMapping("/v1/people/{id}")
  String updatePerson(@RequestHeader("Authorization") String token,
                    @PathVariable("id") long id,
                    @RequestBody FubPeopleInfo fubPeopleInfo);

  @PostMapping("/v1/textMessages")
  String createTextMessage(@RequestHeader("Authorization") String token,
                                  @RequestBody FubTextMessage fubTextMessage);

  @PostMapping("/v1/calls")
  String createCall(@RequestHeader("Authorization") String token,
                   @RequestBody FubCall fubCall);

  @GetMapping("/v1/notes/{id}")
  FubNoteVo getNote(@RequestHeader("Authorization") String token, @PathVariable("id") Long id);

  @PostMapping("/v1/notes")
  String createNote(@RequestHeader("Authorization") String token,
                   @RequestBody FubNote fubNote);

  @PutMapping("/v1/notes/{id}")
  String updateNote(@RequestHeader("Authorization") String token, @PathVariable("id") long id,
      @RequestBody FubNote fubNote);

  @GetMapping("/v1/deals/{id}")
  FubDeal getDeal(@RequestHeader("Authorization") String token,
      @PathVariable("id") Integer id);

  @PostMapping("/v1/deals")
  FubDeal createDeal(@RequestHeader("Authorization") String token,
      @RequestBody FubDealRequest request);

  @DeleteMapping("/v1/deals/{id}")
  void deleteDeal(@RequestHeader("Authorization") String token,
      @PathVariable("id") Long id);

  @PutMapping("/v1/deals/{id}")
  FubDeal updateDeal(@RequestHeader("Authorization") String token,
      @PathVariable("id") Long id, @RequestBody FubDealRequest request);

  @GetMapping("/v1/people/{id}")
  FubPeople getPeopleDetail(@PathVariable("id") long peopleId,
      @RequestHeader("Authorization") String token);

  @GetMapping("/v1/peopleRelationships")
  FubPeopleRelationshipResult getPeopleRelationships(@RequestParam("personId") long peopleId,
      @RequestHeader("Authorization") String callToken);

  @GetMapping("/v1/timeframes")
  FubTimeframeConfigResult getTimeframeConfig(@RequestHeader("Authorization") String callToken);

  @GetMapping("/v1/calls/{id}")
  FubCallRecord getFubCall(@RequestHeader("Authorization") String callToken,
                           @PathVariable("id") long id);

  @GetMapping("/v1/textMessages/{id}")
  FubTextRecord getFubText(@RequestHeader("Authorization") String callToken,
                           @PathVariable("id") long id);

  @PostMapping("/v1/peopleRelationships")
  String createRelationships(@RequestHeader("Authorization") String token,
      @RequestBody FubPeopleInfo.FubRelationship relationship);

  @PutMapping("/v1/peopleRelationships/{id}")
  String updateRelationships(@RequestHeader("Authorization") String token,
      @PathVariable("id") long peopleId,
      @RequestBody FubPeopleInfo.FubRelationship relationship);

  @GetMapping("/v1/pipelines")
  FubPipelinesResponse getPipelines(@RequestHeader("Authorization") String token);
}

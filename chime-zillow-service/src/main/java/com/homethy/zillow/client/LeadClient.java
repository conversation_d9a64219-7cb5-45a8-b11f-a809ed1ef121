package com.homethy.zillow.client;

import com.homethy.microservice.client.LeadBaseClient;
import com.homethy.microservice.client.leadsearch.lead.VisibleFilterBo;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@LeadBaseClient
public interface LeadClient {

  @GetMapping("/lead/access/agent/{agentId}/visible-filters")
  List<VisibleFilterBo> getAgentVisibleFilters(
      @PathVariable(value = "agentId") long agentId,
      @RequestParam(value = "shared", required = false) boolean shared,
      @RequestParam(value = "otherPrivate", required = false) boolean otherPrivate);
}

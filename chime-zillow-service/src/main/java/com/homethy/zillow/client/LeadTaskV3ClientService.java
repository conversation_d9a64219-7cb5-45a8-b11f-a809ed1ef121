package com.homethy.zillow.client;

import com.homethy.microservice.client.model.DeleteAppointmentRequest;
import com.homethy.microservice.client.model.LeadTask;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    url = "${microservice.smartplan.url:http://smart-plan.crm.svc:8080}",
    name = "${microservice.smartplan.service.id:smart-plan-server}")
public interface LeadTaskV3ClientService {
  @GetMapping("/client/v3/leadtask/{id}")
  LeadTask v3Get(@PathVariable("id") long id);

  @PostMapping("/client/v3/leadtask/{operatorId}")
  LeadTask v3Create(@PathVariable("operatorId") long operatorId, @RequestBody LeadTask leadTask);

  @PutMapping("/client/v3/leadtask/{operatorId}")
  LeadTask v3Update(@PathVariable("operatorId") long operatorId, @RequestBody LeadTask leadTask);

  @PutMapping("/client/v3/leadtask/finish/{operatorId}/{taskId}")
  void v3Finish(@PathVariable("operatorId") long operatorId,
      @PathVariable("taskId") long taskId,
      @RequestParam("isFinish") boolean isFinish);

  @DeleteMapping("/client/v3/leadtask/{id}")
  void v3Delete(@PathVariable("id") long id, @RequestBody DeleteAppointmentRequest request);

}

package com.homethy.zillow.client;

import com.homethy.zillow.model.dto.ZillowTrRespDTO;
import com.homethy.zillow.model.dto.ZillowTransactionDTO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ZillowTransactionClientManager {

  @Autowired
  private ZillowTransactionClient zillowTransactionClient;

  public ZillowTrRespDTO createTransaction(String token, ZillowTransactionDTO dto) {
      return zillowTransactionClient.createTransaction(token, dto);
  }

  public void updateTransaction(String token, String zillowTransactionId, ZillowTransactionDTO dto) {
    zillowTransactionClient.updateTransaction(token, zillowTransactionId, dto);

  }

  public void closeTransaction(String token, String transactionId) {
    zillowTransactionClient.closeTransaction(token, transactionId);
  }

  public void cancelTransaction(String token, String transactionId) {
    zillowTransactionClient.cancelTransaction(token, transactionId);
  }
}

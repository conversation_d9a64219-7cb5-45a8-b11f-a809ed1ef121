package com.homethy.zillow.client;

import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${leadImport.service.id:microservice-leadimport}", url = "${microservice.leadimport.url:http://leadimport.crm.svc:8080}", path = "/emailMarker")
public interface LeadimportClient {

    @PostMapping("to-valid")
    public void toValidate(@RequestBody Set<String> emails);
}

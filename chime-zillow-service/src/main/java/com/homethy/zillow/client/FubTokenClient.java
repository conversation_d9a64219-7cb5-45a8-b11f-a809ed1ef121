package com.homethy.zillow.client;

import com.homethy.fub.configuration.RateFeignConfig;
import com.homethy.zillow.model.constant.fub.FubRefreshToken;
import com.homethy.zillow.model.constant.fub.FubTokenBody;
import com.homethy.zillow.model.constant.fub.FubTokenResult;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(url = "https://app.followupboss.com", name = "FUB", configuration =
    RateFeignConfig.class)
public interface FubTokenClient {

  @PostMapping("/oauth/token")
  FubTokenResult getToken(@RequestHeader("Authorization") String baseToken,
                          @RequestBody FubTokenBody tokenBody);

  @PostMapping("/oauth/token")
  FubTokenResult refreshToken(@RequestHeader("Authorization") String baseToken,
                              @RequestBody FubRefreshToken fubRefreshToken);
}

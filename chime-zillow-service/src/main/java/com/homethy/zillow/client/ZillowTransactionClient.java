package com.homethy.zillow.client;

import com.homethy.zillow.model.dto.ZillowTrRespDTO;
import com.homethy.zillow.model.dto.ZillowTransactionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(url = "https://api.premieragent.com/pa-crm", name = "zillow")
public interface ZillowTransactionClient {

  @PostMapping("/v1/transactions")
  ZillowTrRespDTO createTransaction(
      @RequestHeader("Authorization") String token,
      @RequestBody ZillowTransactionDTO dto);

  @RequestMapping(name = "/v1/transactions/{transactionId}", method = {RequestMethod.GET})
  ZillowTrRespDTO getTransaction(
      @RequestHeader("Authorization") String token,
      @PathVariable("transactionId") String zillowTransactionId);

  @PutMapping("/v1/transactions/{transactionId}")
  ZillowTrRespDTO updateTransaction(
      @RequestHeader("Authorization") String token,
      @PathVariable("transactionId") String zillowTransactionId,
      @RequestBody ZillowTransactionDTO dto);

  @PutMapping("/v1/transactions/{transactionId}/closure")
  ZillowTrRespDTO closeTransaction(
      @RequestHeader("Authorization") String token,
      @PathVariable("transactionId") String zillowTransactionId);

  @PutMapping("/v1/transactions/{transactionId}/cancellation")
  ZillowTrRespDTO cancelTransaction(
      @RequestHeader("Authorization") String token,
      @PathVariable("transactionId") String zillowTransactionId);
}

package com.homethy.zillow.client;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.homethy.i18n.util.MsgException;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.configuration.ZillowConfig;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubCallRecord;
import com.homethy.zillow.model.constant.fub.FubDeal;
import com.homethy.zillow.model.constant.fub.FubDealRequest;
import com.homethy.zillow.model.constant.fub.FubPeople;
import com.homethy.zillow.model.constant.fub.FubPeople.FubPeopleRelationship;
import com.homethy.zillow.model.constant.fub.FubPeopleQueryResult;
import com.homethy.zillow.model.constant.fub.FubPeopleRelationshipResult;
import com.homethy.zillow.model.constant.fub.FubPipelinesResponse;
import com.homethy.zillow.model.constant.fub.FubRefreshToken;
import com.homethy.zillow.model.constant.fub.FubStage;
import com.homethy.zillow.model.constant.fub.FubStageResponse;
import com.homethy.zillow.model.constant.fub.FubTask;
import com.homethy.zillow.model.constant.fub.FubTextRecord;
import com.homethy.zillow.model.constant.fub.FubTimeframeConfigResult;
import com.homethy.zillow.model.constant.fub.FubTimeframeConfigResult.TimeframeConfig;
import com.homethy.zillow.model.constant.fub.FubToken;
import com.homethy.zillow.model.constant.fub.FubTokenBody;
import com.homethy.zillow.model.constant.fub.FubTokenResult;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import com.homethy.zillow.model.constant.fub.FubUserQueryResult;
import com.homethy.zillow.model.constant.fub.FubWebHook;
import com.homethy.zillow.model.constant.fub.FubWebhookInfo;
import com.homethy.zillow.model.constant.fub.FubWebhookResult;
import com.homethy.zillow.model.po.FubLeadImportPageContext;
import com.homethy.zillow.model.po.FubNoteVo;
import com.homethy.zillow.model.po.FubPeopleInfo;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FubClientManager implements InitializingBean {
  LoadingCache<String, Map<Integer,String>> timeFrameCache;

  @Override
  public void afterPropertiesSet() {
    timeFrameCache = CacheBuilder.newBuilder().concurrencyLevel(8)
        .expireAfterWrite(120, TimeUnit.SECONDS).maximumSize(2000)
        .build(new CacheLoader<>() {
          @Override
          public Map<Integer, String> load(String token) {
            return getTimeframeConfig(token);
          }
        });
  }

  private static final int STAGES_MAX_LIMIT = 100;
  private static final String FUB_SYNC_TRANSACTION_KEY = "FUB_SYNC_TRANSACTION";

  @Autowired
  private FubClient fubClient;
  @Autowired
  private FubTokenClient fubTokenClient;
  @Autowired
  private RedisService redisService;


  public FubTokenResult getFubToken(String code, String state) {
    FubTokenBody tokenBody = FubTokenBody.builder().grantType("auth_code").code(code)
        .redirectUrl(ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
        .state(state).build();
    try {
      return fubTokenClient.getToken(getBasicToken(), tokenBody);
    } catch (Exception e) {
      return null;
    }
  }

  private String getBasicToken() {
    String clientId = ZillowConfig.getProperties("chime-zillow.fub.clientId", "");
    String clientSecret = ZillowConfig.getProperties("chime-zillow.fub.clientSecret", "");
    String basic = clientId + ":" + clientSecret;
    return "Basic " + Base64.getEncoder().encodeToString(basic.getBytes());
  }

  public FubTokenResult refreshToken(FubToken token) {
    try {
      return fubTokenClient.refreshToken(getBasicToken(),
          FubRefreshToken.builder().grantType("refresh_token").refreshToken(token.getRefreshToken())
              .build());
    } catch (MsgException e) {
      log.info("call fub refresh token rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("refresh token error, token: {}, exception: ", JacksonUtils.toJson(token), e);
      return null;
    }
  }

  public FubUserInfo getMe(String token) {
    try {
      return fubClient.getMe(token);
    } catch (MsgException e) {
      log.info("call fub user info rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("call fub user info error, token: {}, exception: ", token, e);
      return null;
    }
  }

  public void addAndOpenWebhook(String event, String callToken, String state) {
    String webhookUrl = ZillowConfig.getProperties("fub.webhook.url",
        "https://dialertest.chime.me/api/fub/webhook");
    String url = webhookUrl + "?state=" + state;
    log.info("open event: {}", event);
    try {
      fubClient.addWebhook(callToken, FubWebHook.builder().event(event).url(url).build());
    } catch (MsgException e) {
      log.warn("open webhook rate limit, event: {}, state: {}, exception: ", event, state, e);
      throw e;
    } catch (Exception e) {
      log.warn("open webhook fail, event: {}, state: {}, exception: ", event, state, e);
    }
  }

  public List<Integer> queryWebhookIds(String callToken) {
    try {
      FubWebhookResult webhookResult = fubClient.getWebhooks(callToken, 100);
      return webhookResult.getWebhooks().stream().map(FubWebhookInfo::getId).toList();
    } catch (MsgException e) {
      log.info("get webhook rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get webhook fail, exception: ", e);
      return null;
    }
  }

  public void deleteWebhook(int webhookId, String callToken) {
    try {
      fubClient.deleteWebhook(callToken, webhookId);
    } catch (MsgException e) {
      log.info("delete webhook rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("delete webhook fail, exception: ", e);
    }
  }

  public FubPeopleQueryResult getFubLeadsByPage(FubLeadImportPageContext context) {
    try {
      FubToken token = context.getToken();
      int pageSize = context.getFubLeadInit().getPageSize();
      if (context.getFubLeadInit().hasNext()) {
        return fubClient.getPeopleByPage(pageSize, context.getFubLeadInit().getNext(),
            token.getFubAgentId(), token.getCallToken());
      } else {
        return fubClient.getPeopleByPage(pageSize, 0, token.getFubAgentId(), token.getCallToken());
      }
    } catch (MsgException e) {
      log.info("delete webhook rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub leads fail, context: {}", JacksonUtils.toJson(context), e);
      return FubPeopleQueryResult.empty();
    }
  }

  public FubPeople addLead(FubPeopleInfo fubPeopleInfo, String token) {
    try {
      return fubClient.addLead(token, fubPeopleInfo);
    } catch (MsgException e) {
      log.info("add fub lead rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("add fub lead fail, exception: ", e);
    }
    return null;
  }

  public void updateLead(long leadId, FubPeopleInfo fubPeopleInfo, String token) {
    try {
      fubClient.updateLead(token, leadId, fubPeopleInfo);
    } catch (MsgException e) {
      log.info("update fub lead rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("update fub lead fail, exception: ", e);
    }
  }

  public FubUserInfo getUserInfo(String token, String email) {
    String key = "getUserInfo-" + token.replaceAll(" ", "-") + "-" + email;
    String info = redisService.get(ZillowConstant.ZILLOW_AREA, key);
    if ("".equals(info)) {
      return null;
    }
    if (StringUtils.isNotBlank(info)) {
      try {
        return JacksonUtils.fromJson(info, FubUserInfo.class);
      } catch (Exception e) {
        log.warn("get fub user info fail, exception: {} {}", email, info, e);
      }
    }
    FubUserQueryResult result = fubClient.getUsersByEmail(token, email);
    FubUserInfo fubUserInfo = Optional.ofNullable(result)
        .map(r -> r.getUsers())
        .filter(r -> CollectionUtils.isNotEmpty(r))
        .map(r -> r.get(0))
        .orElse(null);
    info = fubUserInfo == null ? "" : JacksonUtils.toJson(fubUserInfo);
    redisService.set(ZillowConstant.ZILLOW_AREA, key, info, 60 * 60 * 1);
    return fubUserInfo;
  }

  public FubStage getStage(String token, long stageId) {
    try {
      return fubClient.getStage(token, stageId);
    } catch (MsgException e) {
      log.info("get fub lead stage rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub lead stage fail, exception: ", e);
      throw ZillowErrorCodeEnum.SERVICE_UNAVAILABLE.exp();
    }
  }

  public FubStageResponse getFubLeadStagesByPage(String token, int page, int size) {
    if (size > STAGES_MAX_LIMIT) {
      throw new MsgException(ZillowErrorCodeEnum.FUB_STAGES_OVER_LIMIT);
    }
    try {
      return fubClient.getStages(token, size, (page - 1) * size);
    } catch (MsgException e) {
      log.info("get fub lead stages rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub lead stages fail, exception: ", e);
      throw new MsgException(ZillowErrorCodeEnum.SERVICE_UNAVAILABLE);
    }
  }

  public FubPeople getPeopleDetail(long peopleId, String callToken) {
    try {
      return fubClient.getPeopleDetail(peopleId, callToken);
    } catch (MsgException e) {
      log.info("get fub people detail rate limit or fub token invalid, exception: ", e);
      throw e;
    } catch (Exception e) {
      if (e.getMessage() != null && e.getMessage().contains("Requested resource was not found")){
        log.info("get fub people detail fail:{}: ", peopleId, e);
        return null;
      }
      log.warn("get fub people detail fail:{}: ", peopleId, e);
      return null;
    }
  }

  public List<FubPeopleRelationship> getPeopleRelationships(long peopleId, String callToken) {
    FubPeopleRelationshipResult result = fubClient.getPeopleRelationships(peopleId, callToken);
    if (result == null || CollectionUtils.isEmpty(result.getPeoplerelationships())) {
      return new ArrayList<>();
    }
    return result.getPeoplerelationships();
  }

  public Map<Integer, String> getTimeframeConfig(String callToken) {
    if (callToken == null) {
      log.info("callToken is null, cannot fetch timeframe config");
      return new HashMap<>();
    }

    FubTimeframeConfigResult result = fubClient.getTimeframeConfig(callToken);
    if (result == null || CollectionUtils.isEmpty(result.getTimeframes())) {
      return new HashMap<>();
    }
    return result.getTimeframes().stream()
        .collect(Collectors.toMap(TimeframeConfig::getId,TimeframeConfig::getTimeframe,(a,b)->b));
  }

  public Map<Integer, String> getTimeframeConfigCache(String callToken) {
    // Add null check to prevent NPE in Guava Cache
    if (callToken == null) {
      log.info("callToken is null, returning empty timeframe config");
      return new HashMap<>();
    }

    Map<Integer, String> result = null;
    try {
      result = timeFrameCache.get(callToken);
    } catch (ExecutionException ignore) {
      log.info("get timeframe config cache fail");
    }
    if (result == null) {
      result = getTimeframeConfig(callToken);
    }
    return result;
  }

  public FubCallRecord getFubCall(String callToken, long callId) {
    try {
      return fubClient.getFubCall(callToken, callId);
    } catch (MsgException e) {
      log.info("get fub call rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("call fub fail, exception: ", e);
    }
    return null;
  }

  public FubTextRecord getFubText(String callToken, long textId) {
    try {
      return fubClient.getFubText(callToken, textId);
    } catch (MsgException e) {
      log.info("get fub text rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("call fub fail, exception: ", e);
    }
    return null;
  }

  public FubUserInfo getUserById(String token, long userId) {
    String info;
    String key = "getUserById-" + token.replaceAll(" ", "-") + "-" + userId;
    try {
      info = redisService.get(ZillowConstant.ZILLOW_AREA, key);
      if ("".equals(info)) {
        return null;
      }
      if (StringUtils.isNotBlank(info)) {
        return JacksonUtils.fromJson(info, FubUserInfo.class);
      }
    } catch (Exception e) {
      log.info("get fub user fail:{}: ", userId, e);
    }
    try {
      FubUserInfo fubUserInfo = fubClient.getUserById(token, userId);
      info = fubUserInfo == null ? "" : JacksonUtils.toJson(fubUserInfo);
      redisService.set(ZillowConstant.ZILLOW_AREA, key, info, 60 * 60 * 1);
      return fubUserInfo;
    } catch (MsgException e) {
      log.info("get fub user rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub user fail:{}: ", userId, e);
      return null;
    }
  }

  public FubTask getFubTask(String token, long fubTaskId) {
    try {
      return fubClient.getTask(token, fubTaskId);
    } catch (MsgException e) {
      log.info("get fub task rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub task fail:{}: ", fubTaskId, e);
      return null;
    }
  }

  public FubNoteVo getFubNote(String token, long fubNoteId) {
    try {
      return fubClient.getNote(token, fubNoteId);
    } catch (MsgException e) {
      log.info("get fub note rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      if (e.getMessage() != null && e.getMessage().contains("Requested resource was not found")){
        log.info("get fub note fail:{}: ", fubNoteId, e);
      }else {
        log.warn("get fub note fail:{}: ", fubNoteId, e);
      }
      return null;
    }
  }

  public FubDeal createFubDeal(FubDealRequest request, String token) {
    try {
      return fubClient.createDeal(token, request);
    } catch (MsgException e) {
      log.info("add fub deal rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("add fub deal fail, exception: ", e);
    }
    return null;
  }

  public FubDeal updateFubDeal(long fubDealId, FubDealRequest request, String token) {
    try {
      return fubClient.updateDeal(token, fubDealId, request);
    } catch (MsgException e) {
      log.info("update fub deal rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("update fub deal fail, exception: ", e);
    }
    return null;
  }

  public void deleteFubDeal(long dealId, String token) {
    try {
      fubClient.deleteDeal(token, dealId);
    } catch (MsgException e) {
      log.info("delete fub deal rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("delete fub deal fail, exception: ", e);
    }
  }


  public String updatePerson(String token, long id, FubPeopleInfo fubPeopleInfo) {
    try {
      return fubClient.updatePerson(token, id, fubPeopleInfo);
    } catch (MsgException e) {
      log.info("update fub lead rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("update fub lead fail, exception: ", e);
      throw e;
    }
  }


  public FubPipelinesResponse getPipelines(String token) {
    try {
      return fubClient.getPipelines(token);
    } catch (MsgException e) {
      log.info("get fub pipelines rate limit, exception: ", e);
      throw e;
    } catch (Exception e) {
      log.warn("get fub pipelines fail, exception: ", e);
      throw e;
    }
  }

  public void cacheTransactionSync(long transactionId) {
    redisService.set(ZillowConstant.ZILLOW_AREA, FUB_SYNC_TRANSACTION_KEY + transactionId,
        String.valueOf(System.currentTimeMillis()), 30);
  }

  public long getTrUpdateTimeFromCache(long transactionId) {
    return NumberUtils.toLong(
        redisService.get(ZillowConstant.ZILLOW_AREA, FUB_SYNC_TRANSACTION_KEY + transactionId));
  }

}

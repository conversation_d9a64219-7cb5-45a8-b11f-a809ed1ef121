package com.homethy.zillow.client;

import com.homethy.microservice.client.agent.model.AgentEsBo;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "${microservice.lender.service.id:lender}",
    url = "${microservice.lender.url:lender.crm.svc:8080}")
public interface LenderFeignClient {
  @PostMapping("/lender/team/by-email")
  List<AgentEsBo> getLenderListByTeamIdAndEmails(@RequestParam("teamId") long teamId,
      @RequestBody List<String> emails);

}

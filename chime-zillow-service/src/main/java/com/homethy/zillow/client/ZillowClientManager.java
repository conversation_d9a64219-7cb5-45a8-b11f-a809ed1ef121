package com.homethy.zillow.client;

import com.homethy.zillow.model.constant.zillow.ZillowContactInfoResp;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ZillowClientManager {

  @Autowired
  private ZillowClient zillowClient;

  public ZillowContactInfoResp getContact(String token, String contactId) {
    try {
      return this.zillowClient.getContact(token, contactId);
    } catch (FeignException e) {
      if (e.status() == 404) {
        log.info("Zillow contact not found: contactId={}", contactId);
        return null;
      } else if (e.status() == 403) {
        log.info("status 403, Zillow Introspect token error: contactId={}", contactId);
        return null;
      } else if (e.status() == 401) {
        log.info("status 401, Zillow Introspect token error: contactId={}", contactId);
        return null;
      }
      log.warn("Zillow API error: contactId={}, status={}, exception: ", contactId, e.status(), e);
      return null;
    } catch (Exception e) {
      log.warn("failed get zillow contact", e);
      return null;
    }
  }
}

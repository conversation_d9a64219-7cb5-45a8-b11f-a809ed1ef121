package com.homethy.zillow.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.homethy.zillow.model.constant.ZillowUserInfo;
import com.homethy.zillow.model.constant.zillow.Assignment;
import com.homethy.zillow.model.constant.zillow.ContactRequest;
import com.homethy.zillow.model.constant.zillow.ContactStatus;
import com.homethy.zillow.model.constant.zillow.Insight;
import com.homethy.zillow.model.constant.zillow.Note;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfo;
import com.homethy.zillow.model.constant.zillow.ZillowContactInfoResp;
import com.homethy.zillow.model.constant.zillow.ZillowInitResult;
import com.homethy.zillow.model.constant.zillow.ZillowInsight;
import com.homethy.zillow.model.constant.zillow.ZillowIntegration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "https://api.premieragent.com/pa-crm", name = "zillow")
public interface ZillowClient {

  @GetMapping("/v1/userinfo")
  ZillowUserInfo getUserInfo(@RequestHeader("Authorization") String token);

  @DeleteMapping("/v1/integrations")
  String disConnect(@RequestHeader("Authorization") String token);

  @GetMapping("/v1/contacts/{contactId}/insights")
  ZillowInsight getInsight(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId);

  @PutMapping("/v1/integrations")
  JsonNode startIntegration(@RequestHeader("Authorization") String token);

  @GetMapping("/v1/integrations")
  ZillowIntegration getIntegration(@RequestHeader("Authorization") String token);


  @GetMapping("/v1/contacts/{contactId}")
  ZillowContactInfoResp getContact(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId);

  /**
   * update contactInfo field:firstName,lastName,emailAddresses,phoneNumbers,address
   *
   * @param token
   * @param contactId
   * @param request
   * @return
   */
  @PutMapping("/v1/contacts/{contactId}/info")
  ZillowContactInfo updateContact(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @RequestBody ContactRequest request);


  /**
   * update contact status 500	New 505	Attempted contact 510	Spoke with customer 515	Appointment set
   * 520	Met with customer 530	Submitting offers [Buyer Only] 535	Listing agreement [Seller Only]
   * 540	Active listing [Seller Only] 545	Under contract 550	Sale closed 555	Nurture 560	Rejected
   * 565 Showing homes [Buyer Only]
   *
   * @param token
   * @param contactId
   * @param statusId
   */
  @PutMapping("/v1/contacts/{contactId}/status")
  String updateContactStatus(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @RequestBody ContactStatus contactStatus);

  /**
   * add note
   *
   * @param token
   * @param contactId
   * @param note
   * @return
   */
  @PostMapping("/v1/contacts/{contactId}/notes")
  String addContactNote(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @RequestBody Note note);

  /**
   * update note
   *
   * @param token
   * @param contactId
   * @param noteId
   * @param note
   * @return
   */
  @PutMapping("/v1/contacts/{contactId}/notes/{noteId}")
  String updateContactNote(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @PathVariable("noteId") String noteId,
      @RequestBody Note note);

  /**
   * update assignment
   *
   * @param token
   * @param contactId
   * @param assignment
   * @return
   */
  @PutMapping("/v2/contacts/{contactId}/assignment")
  String reassign(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @RequestBody Assignment assignment);


  @PutMapping("/v1/contacts/{contactId}/insights")
  String updateInsight(@RequestHeader("Authorization") String token,
      @PathVariable("contactId") String contactId, @RequestBody Insight insight);

  @GetMapping("/v1/contacts")
  ZillowInitResult pullContacts(@RequestHeader("Authorization") String token,
                                @RequestParam("limit") int limit,
//                                @RequestParam("createdAfter") String createdAfter,
                                @RequestParam("nextCursor") String nextCursor);
}

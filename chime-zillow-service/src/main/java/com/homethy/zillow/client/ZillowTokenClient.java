package com.homethy.zillow.client;

import com.homethy.zillow.model.constant.zillow.ZillowTokenResult;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(url = "https://authv2.zillow.com", name = "zillow")
public interface ZillowTokenClient {

  @PostMapping("/oauth/token")
  ZillowTokenResult refreshToken(@RequestHeader("Authorization") String baseToken,
                                 @RequestParam("grant_type") String grantType,
                                 @RequestParam("refresh_token") String refreshToken,
                                 @RequestParam("client_id") String clientId);

  @PostMapping("/oauth/token")
  ZillowTokenResult getToken(@RequestHeader("Authorization") String baseToken,
                             @RequestParam("grant_type") String grantType,
                             @RequestParam("code") String code,
                             @RequestParam("redirect_uri") String redirectUri);
}

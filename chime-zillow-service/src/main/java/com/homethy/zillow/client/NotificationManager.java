package com.homethy.zillow.client;

import com.google.common.collect.Maps;
import com.homethy.microservice.client.NotificationService;
import com.homethy.util.jackson.JacksonUtils;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NotificationManager {

  private static final int LEAD_NOTIFY_TYPE_ID = 141;

  public static final String FUB_IMPORT_CHANNEL = "Follow Up Boss";
  public static final String ZILLOW_IMPORT_CHANNEL = "Zillow";

  @Autowired
  NotificationService notificationService;


  public void addNotification(long userId, long leadNumber, String importChannel) {
    Map<String, Object> contentMap = Maps.newHashMap();
    contentMap.put("leadNumber", leadNumber);
    contentMap.put("importChannel", importChannel);
    log.info("addNotification,userId:{},leadNumber:{},importChannel:{}", userId, leadNumber,
        importChannel);
    notificationService.addNotification(userId, LEAD_NOTIFY_TYPE_ID,
        JacksonUtils.toJson(contentMap));
  }
}

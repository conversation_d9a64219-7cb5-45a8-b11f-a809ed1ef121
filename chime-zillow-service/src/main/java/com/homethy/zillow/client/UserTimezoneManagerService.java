package com.homethy.zillow.client;

import com.homethy.microservice.client.user.UserPreferenceService;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserTimezoneManagerService {

  @Autowired
  private UserPreferenceService userPreferenceService;

  public TimeZone getUserTimeZone(long userId) {
    try {
      TimeZone zone = userPreferenceService.getUserTimeZone(userId);
      return zone;
    } catch (Exception e) {
      log.warn("getAgentTimeZone userId={}", userId, e);
      return TimeZone.getTimeZone("GMT-7:00");
    }
  }
}

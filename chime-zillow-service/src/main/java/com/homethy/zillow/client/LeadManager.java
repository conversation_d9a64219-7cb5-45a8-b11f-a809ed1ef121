package com.homethy.zillow.client;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.homethy.fub.service.FubLeadService;
import com.homethy.microservice.client.lead.LeadKernelService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.fub.FubEmail;
import com.homethy.zillow.model.constant.fub.FubLead;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class LeadManager {
  @Autowired
  private LeadKernelService leadKernelService;

  @Autowired
  private FubLeadService fubLeadService;

  @Autowired
  private LeadimportClient leadimportClient;

  public LeadBo getLeadById(long leadId) {
    try {
      return leadKernelService.getLeadById(leadId);
    } catch (Exception e) {
      log.warn("getLeadById,leadId:{}", leadId, e);
      return null;
    }
  }

  public OwnershipInfo getOwnershipFromLead(LeadBo lead) {
    FubLead fubLead = fubLeadService.getLatestFubLead(lead.getId());
    if (fubLead == null) {
      return null;
    }
    return OwnershipInfo.builder()
        .ownershipId(fubLead.getOwnershipId())
        .ownershipScope(fubLead.getOwnershipScope())
        .build();
  }

  public void toValidate(List<FubEmail> emails) {
    try {
      if (CollectionUtils.isEmpty(emails)) {
        return;
      }
      Set<String> emailSet = emails.stream()
          .filter(e -> "Valid".equalsIgnoreCase(e.getStatus()))
          .map(e -> e.getValue()).collect(Collectors.toSet());
      if (emailSet.isEmpty()) {
        return;
      }
      leadimportClient.toValidate(emailSet);
    } catch (Exception e) {
      log.info("toValidate,emails:{}", emails, e);
    }
  }
}

package com.homethy.zillow.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Slf4j
@Component
public class ContextUtil implements ApplicationContextAware {

  public static ApplicationContext context;

  private ContextUtil() {
  }

  private static synchronized ApplicationContext newXMLContext() {
    if (context == null) {
      log.error("Re-instantiated the ApplicationContext, this is a wrong operation");
      log.info("Loads another context in a XML way.");
    }
    return context;
  }

  public static <T> T getBean(Class<T> clazz) {
    if (context == null) {
      newXMLContext();
    }
    return context.getBean(clazz);
  }

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    if (applicationContext == null) {
      newXMLContext();
      return;
    }
    ContextUtil.context = applicationContext;
  }
}

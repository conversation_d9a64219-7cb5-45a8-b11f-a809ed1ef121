package com.homethy.zillow.util;

import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by hong<PERSON><PERSON>.z<PERSON> on 4/3 2018 6:06 PM
 */
@Slf4j
public class ThreadPoolUtil {
  /**
   * Thread pool maintains the minimum number of threads
   */
  private static final int CORE_THREAD = 3;
  /**
   * The maximum number of thread pool maintenance threads
   */
  private static final int MAX_THREAD = 50;
  /**
   * The buffer queue used by the thread pool
   */
  private static BlockingQueue<Runnable> threadQueue = new LinkedBlockingQueue<Runnable>(1000
  );

  public static final ExecutorService executorService = new ThreadPoolExecutor(
    CORE_THREAD, MAX_THREAD, 10L, TimeUnit.SECONDS, threadQueue,
    new ThreadPoolExecutor.DiscardOldestPolicy());

  public static <T> Future<T> submitTask(Callable<T> callable) {
    return executorService.submit(callable);
  }

  public static void executeTask(Runnable task) {
    executorService.execute(task);
  }

  public static void sleep(long milSeconds) {
    try {
      TimeUnit.MILLISECONDS.sleep(milSeconds);
    } catch (Exception e) {
      log.warn("", e);
    }
  }

  public static <T> T getResult(Future<T> future) {
    return getResult(future, null);
  }

  public static <T> T getResult(Future<T> future, T defaultValue) {
    try {
      return Optional.ofNullable(
              future.get(3, TimeUnit.MINUTES))
          .orElse(defaultValue);
    } catch (Exception e) {
      log.warn("getResult: ", e);
    }
    return defaultValue;
  }
}

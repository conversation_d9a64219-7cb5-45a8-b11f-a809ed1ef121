package com.homethy.zillow.util;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateTimeUtil {

  public static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss XXX";
  public static final String DATE_TIME_FORMAT_Z = "yyyy-MM-dd'T'HH:mm:ssXXX";


  public static String toFubFormat(Date date) {
    return formatDate(date, DATE_TIME_FORMAT);
  }

  public static String formatDate(Date date, String format) {
    ZoneId zoneId = ZoneId.of("America/New_York");
    ZonedDateTime zonedDateTime = date.toInstant().atZone(zoneId);

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return zonedDateTime.format(formatter);
  }

  public static String parseTimestamp(long timestamp, String format) {
    ZonedDateTime date = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of("UTC"));
    return DateTimeFormatter.ofPattern(format).format(date);
  }

  public static String toFubFormat(long timestamp) {
    return parseTimestamp(timestamp, DATE_TIME_FORMAT);
  }


  public static Date parseToTimestampFubFormat(String date) {
    return new Date(parseFubFormat(date, DATE_TIME_FORMAT));
  }

  public static Timestamp parseToTimestamp(String date) {
    return new Timestamp(parseFubFormat(date));
  }

  public static long parseFubFormat(String date) {
    if (StringUtils.isBlank(date)) {
      return 0L;
    }
    if (date.contains(" ")) {
      return parseFubFormat(date, DATE_TIME_FORMAT);
    }
    return parseFubFormat(date, DATE_TIME_FORMAT_Z);
  }

  public static long parseFubFormat(String date, String format) {
    return DateTimeFormatter.ofPattern(format).parse(date, ZonedDateTime::from).toInstant().toEpochMilli();
  }


}
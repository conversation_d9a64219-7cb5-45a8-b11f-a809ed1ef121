package com.homethy.zillow.util;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;

public class JwtUtils {

    /**
     * Generate a JWT using HS256.
     * Adds iat/exp if not provided.
     */
    public static String generateToken(Map<String, Object> claims, String secret, long ttlSeconds) {
        Algorithm alg = Algorithm.HMAC256(secret.getBytes(StandardCharsets.UTF_8));
        JWTCreator.Builder builder = JWT.create();

        long nowMillis = System.currentTimeMillis();
        builder.withIssuedAt(new Date(nowMillis));
        if (ttlSeconds > 0) {
            builder.withExpiresAt(new Date(nowMillis + ttlSeconds * 1000));
        }

        if (claims != null) {
            for (Map.Entry<String, Object> e : claims.entrySet()) {
                Object v = e.getValue();
                String k = e.getKey();
                if (v == null)
                    continue;
                if (v instanceof Boolean)
                    builder.withClaim(k, (Boolean) v);
                else if (v instanceof Integer)
                    builder.withClaim(k, (Integer) v);
                else if (v instanceof Long)
                    builder.withClaim(k, (Long) v);
                else if (v instanceof Double)
                    builder.withClaim(k, (Double) v);
                else
                    builder.withClaim(k, String.valueOf(v));
            }
        }

        return builder.sign(alg);
    }

    /**
     * Convenience: generate JWT with a subject.
     */
    public static String generateToken(String subject, String secret, long ttlSeconds) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", subject);
        return generateToken(claims, secret, ttlSeconds);
    }

    /**
     * Verify signature and exp (when present). Returns true if valid.
     */
    public static boolean verify(String token, String secret) {
        try {
            Algorithm alg = Algorithm.HMAC256(secret.getBytes(StandardCharsets.UTF_8));
            JWTVerifier verifier = JWT.require(alg).build();
            verifier.verify(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Parse payload without verification (useful after verify=true).
     */
    public static Map<String, Object> parsePayload(String token) {
        Map<String, Object> out = new HashMap<>();
        try {
            DecodedJWT jwt = JWT.decode(token);
            Map<String, Claim> claims = jwt.getClaims();
            for (Map.Entry<String, Claim> e : claims.entrySet()) {
                Claim c = e.getValue();
                if (c == null || c.isNull())
                    continue;
                if (c.asBoolean() != null)
                    out.put(e.getKey(), c.asBoolean());
                else if (c.asInt() != null)
                    out.put(e.getKey(), c.asInt());
                else if (c.asLong() != null)
                    out.put(e.getKey(), c.asLong());
                else if (c.asDouble() != null)
                    out.put(e.getKey(), c.asDouble());
                else if (c.asString() != null)
                    out.put(e.getKey(), c.asString());
            }
        } catch (Exception ignore) {
        }
        return out;
    }
}

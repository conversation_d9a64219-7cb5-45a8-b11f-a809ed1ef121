package com.homethy.zillow.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author:daoping.liu
 * @Date:2021-03-10 14:52
 */
@Slf4j
public class HmacSHA256Util {
  public static String sha256(String key, String data) {
    try {
      Mac sha256 = Mac.getInstance("HmacSHA256");
      SecretKeySpec secretKey = new SecretKeySpec(
          key.getBytes("UTF-8"), "HmacSHA256");
      sha256.init(secretKey);
      byte[] bytes = sha256.doFinal(data.getBytes("UTF-8"));
      return byteArrayToHexString(bytes);
    } catch (Exception e) {
      log.error("sha256_HMAC", e);
    }
    return "";
  }

  private static String byteArrayToHexString(byte[] bytes) {
    StringBuilder hs = new StringBuilder();
    String hexStr;
    for (int n = 0; bytes != null && n < bytes.length; n++) {
      hexStr = Integer.toHexString(bytes[n] & 0XFF);
      if (hexStr.length() == 1)
        hs.append('0');
      hs.append(hexStr);
    }
    return hs.toString().toLowerCase();
  }
}

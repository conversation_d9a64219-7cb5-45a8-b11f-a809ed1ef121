package com.homethy.zillow.util;

import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

public class MdcUtil {
  private static String TRACE_ID = "traceId";
  private static String SPAN_ID = "spanId";

  public static String getTraceId() {
    String traceId = MDC.get(TRACE_ID);
    if (StringUtils.isEmpty(traceId)) {
      traceId = getUUid(true);
      setTrace(traceId);
    }
    return traceId;
  }

  public static void setTrace(String traceId) {
    MDC.put(TRACE_ID, traceId);
  }

  public static void setSpanId(String spanId) {
    MDC.put(SPAN_ID, spanId);
  }

  public static String getSpanId() {
    String spanId = MDC.get(SPAN_ID);
    if (StringUtils.isEmpty(spanId)) {
      spanId = getUUid(false);
      MDC.put(SPAN_ID, spanId);
    }
    return spanId;
  }

  private static String getUUid(boolean traceFlag) {
    UUID uuid = UUID.randomUUID();
    String uniqueId = uuid.toString().replace("-", "");
    if (traceFlag) {
      return uniqueId.substring(0, 16);
    } else {
      return uniqueId.substring(16);
    }
  }

  public static String addTraceId() {
    String trace = getUUid(true);
    MDC.put(TRACE_ID, trace);
    return trace;
  }

  public static String addSpanId() {
    String spanId = getUUid(false);
    MDC.put(SPAN_ID, spanId);
    return spanId;
  }

  public static void clearTrace() {
    MDC.remove(TRACE_ID);
    MDC.remove(SPAN_ID);
  }
}

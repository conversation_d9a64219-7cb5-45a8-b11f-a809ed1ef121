package com.homethy.zillow.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonUtil {

  private static final ObjectMapper objMapper;

  static {
    objMapper = new ObjectMapper();
    objMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
  }

  public static <T> T fromJson(String jsonText, Class<T> clazz) throws IOException {
    return jsonText != null && !"".equals(jsonText) ? objMapper.readValue(jsonText, clazz) : null;
  }


  public static <T> List<T> toList(String json, Class<T> clazz) {
    if (StringUtils.isBlank(json)) {
      return Collections.emptyList();
    }
    JavaType t = objMapper.getTypeFactory().constructParametricType(List.class, new Class[]{clazz});
    try {
      return objMapper.readValue(json, t);
    } catch (Exception e) {
      log.info("invalid json:{} to {}: {}", json, clazz, e.getMessage());
      return Collections.emptyList();
    }

  }

  public static JsonNode asTargetNode(JsonNode contact, String... names) {
    JsonNode node = contact;
    for (String name : names) {
      node = node.get(name);
      if (node == null || node.isNull()) {
        return null;
      }
    }
    return node;
  }

  public static String asText(JsonNode contact, String... names) {
    JsonNode targetNode = asTargetNode(contact, names);
    return targetNode == null ? null : targetNode.asText();
  }

  public static Number getNumber(JsonNode pNode, String name) {
    JsonNode n = pNode.get(name);
    if (n != null && n.isNumber()) {
      return n.numberValue();
    }
    return 0;
  }
}

package com.homethy.zillow.util;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ThreadUtil {

  private static final int CORE_THREAD = 20;
  private static final int MAX_THREAD = 20;

  private static BlockingQueue<Runnable> threadQueue = new LinkedBlockingQueue<>(200);
  private static ThreadFactory defaultThreadFactory = new ThreadFactory() {
    private final ThreadFactory threadFactory = Executors.defaultThreadFactory();

    @Override
    public Thread newThread(Runnable r) {
      Thread thread = threadFactory.newThread(r);
      thread.setName("zillow-common" + thread.getName());
      thread.setUncaughtExceptionHandler((t, e) -> log.error("ThreadExecutorUtil"
          + " throws exception.{} ", t, e));
      return thread;
    }
  };

  private static final ExecutorService executorService = new ThreadPoolExecutor(
      CORE_THREAD, MAX_THREAD, 60, TimeUnit.SECONDS, threadQueue,
      defaultThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());

  public static void executeTask(Runnable task) {
    final String traceId = MdcUtil.getTraceId();
    executorService.execute(() -> {
      try {
        MdcUtil.setTrace(traceId);
        MdcUtil.addSpanId();
        task.run();
      } catch (Exception e) {
        log.warn("executeTask failed", e);
      } finally {
        MdcUtil.clearTrace();
      }
    });
  }

  public static <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
    return executorService.invokeAll(tasks);
  }

  public static <T> Future<T> submit(Callable<T> callable) {
    String spanId = MdcUtil.getSpanId();
    if (threadQueue.size() > CORE_THREAD) {
      log.info("threadQueue size ={}", threadQueue.size());
    }
    return executorService.submit(() -> {
      try {
        MdcUtil.addTraceId();
        MdcUtil.setSpanId(spanId);
        return callable.call();
      } finally {
        MdcUtil.clearTrace();
      }
    });
  }

  public static void sleep(long millis) {
    try {
      Thread.sleep(millis);
    } catch (Exception e) {
      log.warn("millis={}", millis, e);
    }
  }

  public static <T> T getResult(Future<T> future) {
    return getResult(future, null);
  }

  public static <T> T getResult(Future<T> future, T defaultValue) {
    try {
      return Optional.ofNullable(
              future.get(3, TimeUnit.MINUTES))
          .orElse(defaultValue);
    } catch (Exception e) {
      log.warn("getResult: ", e);
    }
    return defaultValue;
  }

  public static int getQueueLimit() {
    return threadQueue.remainingCapacity();
  }

  public static boolean hasQueue(long size) {
    return size < threadQueue.remainingCapacity();
  }
}

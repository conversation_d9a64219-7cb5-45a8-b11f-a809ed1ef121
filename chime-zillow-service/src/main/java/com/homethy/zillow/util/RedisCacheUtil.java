package com.homethy.zillow.util;

import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.config.GlobalConfig;
import com.homethy.util.jackson.JacksonUtils;
import java.io.IOException;
import java.util.Collection;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Slf4j
public class RedisCacheUtil {

  public static RedisService getRedisService() {
    return ContextUtil.getBean(RedisService.class);
  }

  public static <T> void setData(String area, String key, T data) {
    if (data == null) {
      return;
    }
    try {
      String value = (data instanceof String) ? (String) data : JacksonUtils.toJson(data);
      getRedisService().set(area, key, value, getExpireTime());
    } catch (Exception e) {
      log.warn("setData key={},data={}", key, data, e);
    }
  }

  public static int getExpireTime() {
    return NumberUtils.toInt(GlobalConfig.getProperties("user-service.redis.cache.time"),
        RedisService.MINUTE * 1);
  }

  public static <T> Optional<T> getData(String area, String key, Class<T> clazz) {
    return Optional.ofNullable(getRedisService().get(area, key))
        .filter(e -> StringUtils.isNoneEmpty(e))
        .map(e -> {
          try {
            return clazz == String.class ? (T) e : JacksonUtils.fromJson(e, clazz);
          } catch (IOException ex) {
            log.warn("getData key={},clazz={}", key, clazz, e);
          }
          return null;
        });
  }

  public static <T> T getData(String area, String key, Class<T> clazz, Supplier<T> supplier) {
    return getData(area, key, clazz).orElseGet(() -> {
      T data = supplier.get();
      setData(area, key, data);
      return data;
    });
  }

  public static <T> void updateData(String area, String key, T target, Consumer<T> consumer) {
    // update db
    consumer.accept(target);
    // del cache
    if (getRedisService().exists(area, key)) {
      getRedisService().del(area, key);
    }
  }

  public static <T> void batchUpdateData(String area, Function<T, String> function,
      Collection<T> collection,
      Consumer<T> consumer) {
    if (CollectionUtils.isEmpty(collection)) {
      return;
    }
    collection.forEach(e -> {
      updateData(area, function.apply(e), e, consumer);
    });
  }
}

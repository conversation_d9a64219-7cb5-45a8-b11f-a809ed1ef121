package com.homethy.zillow.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.LanguageDriver;
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver;
import org.apache.ibatis.session.Configuration;

/**
 * <AUTHOR>
 * @createTime 15-10-31 7:23 PM
 */
public class MybatisExtendedLanguageDriver extends XMLLanguageDriver implements LanguageDriver {

  private final Pattern inPattern = Pattern.compile("\\(#\\{(\\w+)}\\)");

  private final Pattern cdataPattern = Pattern.compile("( [^ ]+? (<|>|<=|>=|<>) [^ ]+? )");

  @Override
  public SqlSource createSqlSource(
      Configuration configuration, String script, Class<?> parameterType) {
    Matcher matcher = inPattern.matcher(script);
    if (matcher.find()) {
      script = matcher.replaceAll("(<foreach collection=\"$1\" item=\"__item\" "
          + "separator=\",\" >#{__item}</foreach>)");
    }
    Matcher cdataMatcher = cdataPattern.matcher(script);
    if (cdataMatcher.find()) {
      script = cdataMatcher.replaceAll(" <![CDATA[$1]]> ");
    }
    script = "<script>" + script + "</script>";
    return super.createSqlSource(configuration, script, parameterType);
  }
}

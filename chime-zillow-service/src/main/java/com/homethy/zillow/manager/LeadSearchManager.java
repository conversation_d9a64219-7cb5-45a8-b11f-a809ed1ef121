package com.homethy.zillow.manager;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.leadsearch.api.LeadSearchService;
import com.homethy.microservice.client.leadsearch.lead.FilterRoleAssigneeBo;
import com.homethy.microservice.client.leadsearch.lead.LeadBo;
import com.homethy.microservice.client.leadsearch.lead.LeadDetailBo;
import com.homethy.microservice.client.leadsearch.lead.LeadQueryBo;
import com.homethy.microservice.client.leadsearch.lead.SearchResultBo;
import com.homethy.microservice.client.leadsearch.lead.VisibleFilterBo;
import com.homethy.zillow.client.LeadClient;
import com.homethy.zillow.model.vo.VisibleQuery;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class LeadSearchManager {

  @Autowired
  LeadSearchService leadSearchService;
  @Autowired
  LeadClient leadClient;

  public SearchResultBo leadIdsByAssign(User agent,int size) {
    LeadQueryBo query = new LeadQueryBo();
    query.setCreateTimeMin(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(730));
    FilterRoleAssigneeBo fra = new FilterRoleAssigneeBo();
    fra.setRoleId(10);
    fra.setAssigneeToIds(List.of(agent.getId()));
    query.setRoleAssigneeList(List.of(fra));
    query.setTeamId(agent.getTeamId());
    query.setSize(50);
    query.setReturnField(new String[]{"data.lead.leadId"});
    query.setUseScroll(true);
    return leadSearchService.search(query);
  }
  public SearchResultBo scroll(String scrollId) {
    LeadQueryBo query = new LeadQueryBo();
    query.setScrollId(scrollId);
    return leadSearchService.search(query);
  }

  public LeadDetailBo queryByLeadIdTeamId(long leadId, long teamId, List<String> returnFields) {
    LeadQueryBo query = new LeadQueryBo();
    query.setLeadId(leadId);
    query.setTeamId(teamId);
    if (CollectionUtils.isEmpty(returnFields)) {
      query.setReturnField(new String[]{"data.lead", "data.leadAgentGroupMember", "data.phoneList",
          "data.assignedUser"});
    } else {
      query.setReturnField(returnFields.toArray(new String[0]));
    }
    SearchResultBo result = leadSearchService.search(query);
    if (result == null || result.getLeadDetailModels() == null || result.getLeadDetailModels().isEmpty()) {
      return null;
    }
    return result.getLeadDetailModels().get(0);
  }

  public List<LeadDetailBo> queryByVisible(VisibleQuery visibleQuery) {
    if (visibleQuery == null || visibleQuery.getAgentId() <= 0 || visibleQuery.getTeamId() <= 0) {
      return Collections.emptyList();
    }
    List<VisibleFilterBo> visibleFilters = leadClient.getAgentVisibleFilters(visibleQuery.getAgentId(), false, false);
    if (CollectionUtils.isEmpty(visibleFilters)) {
      return Collections.emptyList();
    }
    LeadQueryBo leadQueryBo = new LeadQueryBo();
    leadQueryBo.setVisibleFilters(visibleFilters);
    leadQueryBo.setTeamId(visibleQuery.getTeamId());
    if (visibleQuery.isOnlyId()) {
      leadQueryBo.setReturnField(new String[]{"data.lead.leadId"});
    } else {
      leadQueryBo.setReturnField(new String[]{"data.lead"});
    }
    if (StringUtils.isNotEmpty(visibleQuery.getLeadEmail())) {
      leadQueryBo.setEmail(visibleQuery.getLeadEmail());
    }
    if (CollectionUtils.isNotEmpty(visibleQuery.getLeadIds())) {
      leadQueryBo.setLeadIds(visibleQuery.getLeadIds().stream().mapToLong(Long::longValue).toArray());
    }
    if (visibleQuery.getLeadId() > 0) {
      leadQueryBo.setLeadId(visibleQuery.getLeadId());
    }
    if (visibleQuery.getReturnSize() > 0) {
      leadQueryBo.setSize(visibleQuery.getReturnSize());
    }
    return leadSearchService.search(leadQueryBo).getLeadDetailModels();
  }

  public List<Long> queryByVisibleLeadIds(VisibleQuery visibleQuery) {
    return queryByVisible(visibleQuery).stream()
        .map(LeadDetailBo::getLead)
        .map(LeadBo::getLeadId)
        .collect(Collectors.toList());
  }

  public SearchResultBo getAssignRoleUserIds(long leadId, long teamId) {
    LeadQueryBo query = new LeadQueryBo();
    query.setTeamId(teamId);
    query.setLeadId(leadId);
    query.setSize(1);
    query.setReturnField(new String[]{"data.assignRoleUserIds"});
    return leadSearchService.search(query);
  }
}

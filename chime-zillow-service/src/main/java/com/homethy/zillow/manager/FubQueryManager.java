package com.homethy.zillow.manager;

import com.google.common.collect.Lists;
import com.homethy.fub.dao.FubLeadDao;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.client.FubClientManager;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubUserInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class FubQueryManager {
  @Autowired
  private FubLeadDao fubLeadDao;
  @Autowired
  private UserManager userManager;
  @Autowired
  private FubClientManager fubClientManager;


  public long getFubUserId(long agentId, String token) {
    User user = userManager.getUserById(agentId);
    if (user == null) {
      return -1;
    }
    FubUserInfo fubUserInfo = fubClientManager.getUserInfo(token, user.getAccount());
    if (fubUserInfo != null) {
      return fubUserInfo.getId();
    }
    return 0;
  }

  public long getFubPersonId(long leadId) {
    FubLead fubLead = fubLeadDao.getLatestFubLeadByLeadId(leadId);
    if (fubLead != null) {
      return fubLead.getPeopleId();
    }
    return 0;
  }

  public Map<Long, FubLead> getFubLeadsByLeadIds(List<Long> leadIds) {
    List<FubLead> list = fubLeadDao.getFubLeads(leadIds);
    return Optional.ofNullable(list).orElse(Lists.newArrayList())
        .stream()
        .collect(Collectors.toMap(FubLead::getLeadId, Function.identity()));
  }


  public FubUserInfo queryFubUser(String token, String email) {
    if (email == null) {
      return null;
    }
    return fubClientManager.getUserInfo(token, email);
  }

}

package com.homethy.zillow.manager;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.homethy.zillow.dao.ZillowLeadDao;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.service.ZillowStatusService;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/6/6
 */
@Service
@Slf4j
public class ZillowLeadManager implements InitializingBean {

  /**
   * Static marker object to represent null ZillowLead in cache
   * Avoids creating new objects and CacheLoader$InvalidCacheLoadException
   */
  public static final ZillowLead NULL_LEAD = new ZillowLead();

  private static int cacheSeconds = 120;
  LoadingCache<Long, ZillowLead> zillowLeadCache;

  @Resource
  ZillowLeadDao zillowLeadDao;
  @Autowired
  ZillowStatusService zillowStatusService;


  @Override
  public void afterPropertiesSet() {
    zillowLeadCache = CacheBuilder.newBuilder().concurrencyLevel(8)
        .expireAfterWrite(cacheSeconds, TimeUnit.SECONDS).maximumSize(2000)
        .build(new CacheLoader<Long, ZillowLead>() {
          @Override
          public ZillowLead load(Long leadId) {
            ZillowLead zillowLead = zillowStatusService.getZillowLead(leadId);
            // Return static marker object instead of null to avoid CacheLoader$InvalidCacheLoadException
            return zillowLead != null ? zillowLead : NULL_LEAD;
          }
        });
  }

  public ZillowLead getZillowLeadByLeadId(long leadId) {
    try {
      ZillowLead zillowLead = zillowLeadCache.get(leadId);
      // Return null if it's the static marker object
      return zillowLead == NULL_LEAD ? null : zillowLead;
    } catch (ExecutionException e) {
      log.warn("getZillowLeadByLeadId failed", e);
    }
    return null;
  }

  public void batchUpdate(Map<Long, ZillowLead> zillowLeadMap) {
    zillowLeadCache.putAll(zillowLeadMap);
  }

}

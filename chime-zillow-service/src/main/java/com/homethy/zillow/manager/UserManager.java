package com.homethy.zillow.manager;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableList;
import com.homethy.microservice.client.agent.AgentUserService;
import com.homethy.microservice.client.agent.model.AgentEsBo;
import com.homethy.microservice.client.bo.office.OfficeBo;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.team.vendor.Vendor;
import com.homethy.microservice.client.model.OwnershipScopeEnumBo;
import com.homethy.microservice.client.model.zillow.ZillowImportLeadSetting;
import com.homethy.microservice.client.office.OfficeService;
import com.homethy.microservice.client.offline.AgentOfflineService;
import com.homethy.microservice.client.team.TeamService;
import com.homethy.microservice.client.user.UserService;
import com.homethy.microservice.client.vendor.VendorService;
import com.homethy.zillow.client.LenderFeignClient;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserManager {

  @Autowired
  private OfficeService officeService;
  @Autowired
  private UserService userService;
  @Autowired
  private TeamService teamService;
  @Autowired
  private AgentUserService agentUserService;
  @Autowired
  private AgentOfflineService agentOfflineService;
  @Autowired
  private VendorService vendorService;
  @Autowired
  private LenderFeignClient lenderFeignClient;

  public User getUserById(long userId) {
    if (userId <= 0) {
      return null;
    }
    return userService.getUserById(userId);
  }

  public boolean checkOffice(long officeId) {
    if (officeId <= 0) {
      return false;
    }
    return getOffice(officeId) != null;
  }

  public boolean checkOffice(long officeId, long teamId) {
    if (officeId <= 0) {
      return false;
    }
    OfficeBo office = getOffice(officeId);
    if (office == null) {
      return false;
    }
    return office.getTeamId() == teamId;
  }

  public OfficeBo getOffice(long officeId) {
    List<OfficeBo> offices = officeService.getOfficeByIds(List.of(officeId));
    if (CollectionUtils.isNotEmpty(offices)) {
      return offices.get(0);
    }
    return null;
  }

  public long getTeamId(ZillowImportLeadSetting zillowImportSetting) {
    if (zillowImportSetting == null) {
      return -1;
    }
    if (zillowImportSetting.getOwnershipScope() == OwnershipScopeEnumBo.PERSONAL) {
      return userService.getUserById(zillowImportSetting.getInstanceUserId()).getTeamId();
    }
    if (zillowImportSetting.getOwnershipScope() == OwnershipScopeEnumBo.OFFICE) {
      OfficeBo office = getOffice(zillowImportSetting.getOwnershipId());
      return office != null ? office.getTeamId() : -2;
    }
    if (zillowImportSetting.getOwnershipScope() == OwnershipScopeEnumBo.TEAM) {
      return checkTeam(zillowImportSetting.getTeamId()) ? zillowImportSetting.getOwnershipId() : -3;
    }
    return -4;
  }

  public boolean checkTeam(long teamId) {
    if (teamId <= 0) {
      return false;
    }
    return teamService.getTeamById(teamId) != null;
  }

  public long getOperUserId(long teamId, OwnershipInfo ownershipInfo) {
    long operUserId = 0;
    if (teamId <= 0 || ownershipInfo == null) {
      return operUserId;
    }
    if (ownershipInfo.getOwnershipScope() == OwnershipScope.PERSONAL) {
      operUserId = agentUserService.getAgentUser(ownershipInfo.getOwnershipId(), teamId).getId();
    } else if (ownershipInfo.getOwnershipScope() == OwnershipScope.OFFICE) {
      OfficeBo office = getOffice(ownershipInfo.getOwnershipId());
      operUserId = office.getWebsiteOwnerId() > 0 ? office.getWebsiteOwnerId() :
          teamService.getTeamById(teamId).getTeamOwnerId();
    } else {
      operUserId = teamService.getTeamById(teamId).getTeamOwnerId();
    }
    log.info("teamId: {}, ownership: {}, operUserId: {}", teamId, ownershipInfo, operUserId);
    return operUserId;
  }

  public long getLeadOwnershipId(OwnershipInfo ownershipInfo, long teamId) {
    if (ownershipInfo == null || ownershipInfo.getOwnershipScope() == null) {
      return 0;
    }
    if (ownershipInfo.getOwnershipScope() == OwnershipScope.PERSONAL) {
      User agentUser = agentUserService.getAgentUser(ownershipInfo.getOwnershipId(), teamId);
      return agentUser == null ? -1 : agentUser.getId();
    }
    return ownershipInfo.getOwnershipId();
  }

  public List<Long> getUserIds(long baseId) {
    try {
      return agentOfflineService.getUserIds(baseId);
    } catch (Exception e) {
      log.warn("call agent offline fail, baseId: {}, exception: ", baseId, e);
      return Collections.EMPTY_LIST;
    }
  }

  public long getTeamUserId(long teamId, long baseId) {
    try {
      User agentUser = agentUserService.getAgentUser(baseId, teamId);
      if (agentUser == null) {
        return -1;
      }
      return agentUser.getId();
    } catch (Exception e) {
      log.warn("call user fail, teamId: {}, baseId: {}, exception: ", teamId, baseId, e);
    }
    return -2;
  }

  public long getTeamIdNotPersonal(OwnershipScope ownershipScope, long ownershipId) {
    if (ownershipScope == null) {
      return 0;
    }
    if (ownershipScope == OwnershipScope.OFFICE) {
      List<OfficeBo> offices =
          officeService.getOfficeByIds(ImmutableList.of(ownershipId));
      return CollectionUtils.isEmpty(offices) ? 0 : offices.get(0).getTeamId();
    }
    if (ownershipScope == OwnershipScope.TEAM) {
      User user = userService.getUserById(ownershipId);
      return user == null ? 0 : user.getTeamId();
    }
    return -1;
  }

  public Map<Long, Vendor> getVendorMap(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Collections.emptyMap();
    }
    return Optional.ofNullable(vendorService.queryVendorByIds(ids)).orElse(Collections.emptyList())
        .stream()
        .collect(Collectors.toMap(Vendor::getId, v -> v));
  }

  public Map<Long, User> getUserMap(List<Long> ids) {
    return getUsers(ids).stream()
        .collect(Collectors.toMap(User::getId, v -> v));
  }

  public List<AgentEsBo> getLenders(long teamId, List<String> emails) {
    return lenderFeignClient.getLenderListByTeamIdAndEmails(teamId, emails);
  }
  public List<User> getUsers(List<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Collections.emptyList();
    }
    return Optional.ofNullable(agentOfflineService.getUserByIds(userIds)).orElse(Collections.emptyList());
  }
  public List<Vendor> getVendors(long teamId, List<String> emails) {
    return vendorService.getVendorByEmail(teamId, emails);
  }

  public Optional<User> getAgentByEmail(String email, long teamId) {
    List<User> allAgentsByAccount = userService.getAllAgentsByAccount(email);
    if (CollectionUtils.isEmpty(allAgentsByAccount)) {
      return Optional.empty();
    }
    return allAgentsByAccount.stream()
        .filter(user -> user.getTeamId() == teamId).findFirst();
  }
}

package com.homethy.zillow.configuration;

import com.homethy.configcenter.client.AppConfigIdentifier;
import com.homethy.configcenter.client.AppReloadableProperties;
import com.homethy.configcenter.client.ApplicationNameReader;
import com.homethy.configcenter.client.SingleProfileReader;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ZillowConfig {

  private static final String APPLICATION_NAME = "chime-zillow";

  private static final AppReloadableProperties appProperties =
      new AppReloadableProperties(
          new AppConfigIdentifier(
              (new ApplicationNameReader()).getApplicationNameWithDefault(APPLICATION_NAME),
              (new SingleProfileReader()).getActiveProfile()
          )
      );

  public static String getProperties(String key, String defaultValue) {
    return appProperties.getProperty(key, defaultValue);
  }
}

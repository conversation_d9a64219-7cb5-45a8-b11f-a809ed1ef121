package com.homethy.zillow.configuration;

import com.homethy.persistence.service.base.impl.RedisServiceImpl;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Primary
@Service
public class RedisLockService extends RedisServiceImpl {

  public boolean lock(String area, String key, int times) {
    if (setnx(area, key, "1") > 0) {
      expire(area, key, times);
      return true;
    }
    return false;
  }

  public boolean unlock(String area, String key) {
    return del(area, key) > 0;
  }
}

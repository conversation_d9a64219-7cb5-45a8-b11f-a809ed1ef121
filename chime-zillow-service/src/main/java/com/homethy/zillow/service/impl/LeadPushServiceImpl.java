package com.homethy.zillow.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.homethy.i18n.util.MsgException;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.lead.LeadUserKernelService;
import com.homethy.microservice.client.lead.UserEmailPhoneService;
import com.homethy.microservice.client.lead.apiV3.LeadPropertyService;
import com.homethy.microservice.client.leadsearch.api.LeadSearchService;
import com.homethy.microservice.client.leadsearch.lead.LeadDetailBo;
import com.homethy.microservice.client.leadsearch.lead.LeadQueryBo;
import com.homethy.microservice.client.leadsearch.lead.SearchResultBo;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadUserBo;
import com.homethy.microservice.client.model.UserEmailBo;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.microservice.client.offline.AgentOfflineService;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.persistence.domain.LeadTimeline;
import com.homethy.persistence.domain.LeadTimeline.LeadTimelineFromToTypeEnum;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.client.ZillowClient;
import com.homethy.zillow.dao.LeadTimelineLogDao;
import com.homethy.zillow.dao.ZillowLeadDao;
import com.homethy.zillow.dao.ZillowNotifyLogDao;
import com.homethy.zillow.manager.ZillowLeadManager;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.constant.zillow.Assignment;
import com.homethy.zillow.model.constant.zillow.ContactRequest;
import com.homethy.zillow.model.constant.zillow.ContactStatus;
import com.homethy.zillow.model.constant.zillow.ContactType;
import com.homethy.zillow.model.constant.zillow.Insight;
import com.homethy.zillow.model.constant.zillow.Note;
import com.homethy.zillow.model.constant.zillow.Note.Body;
import com.homethy.zillow.model.constant.zillow.ZillowAddress;
import com.homethy.zillow.model.constant.zillow.ZillowPhone;
import com.homethy.zillow.model.dto.LeadTimelineLog;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.zillow.model.po.LeadEmailChange;
import com.homethy.zillow.model.po.ZillowNotifyLog;
import com.homethy.zillow.model.po.ZillowNotifyLog.Status;
import com.homethy.zillow.model.po.ZillowNotifyLog.Type;
import com.homethy.zillow.model.po.ZillowRequest;
import com.homethy.zillow.service.LeadPushService;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowOauth2TokenService;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.Jsoup;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Nonnull;
import jakarta.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import static com.homethy.microservice.client.model.LeadBo.LeadListingTypeEnum;

/**
 * <AUTHOR>
 * @Date 2023/5/22
 */
@Slf4j
@Service
public class LeadPushServiceImpl implements LeadPushService {

  public static final List<Integer> TIMELINETYPELIST = List.of(21, 80, 31, 26, 124, 6, 128, 96, 130,
      7, 8, 25, 42, 54, 55, 87, 28, 43, 56, 125, 24, 16);

  @Resource
  LeadOfflineService leadOfflineService;
  @Resource
  AgentOfflineService agentOfflineService;
  @Resource
  LeadSearchService leadSearchService;
  @Resource
  LeadTimelineLogDao leadTimelineLogDao;
  @Resource
  ZillowLeadDao zillowLeadDao;
  @Resource
  ZillowNotifyLogDao zillowNotifyLogDao;
  @Resource
  ZillowClient zillowClient;
  @Resource
  LeadUserKernelService leadUserKernelService;
  @Resource
  LeadPropertyService leadPropertyService;
  @Resource
  ZillowLeadService zillowLeadService;
  @Resource
  ZillowLeadManager zillowLeadManager;
  @Resource
  ZillowOauth2TokenService zillowOauth2TokenService;
  @Resource
  RedisService redisService;
  @Resource
  UserEmailPhoneService userEmailPhoneService;

  @Override
  public boolean pushLead(@Nonnull ZillowRequest request, Type type) {
    String token = request.getToken();
    String contactId = request.getContactId();

    ZillowNotifyLog zillowNotifyLog = ZillowNotifyLog.builder().contactId(contactId)
        .leadId(request.getLeadId()).pushContact(JacksonUtils.toJson(request))
        .bindingAgentId(request.getAgentId()).type(type.getType()).build();
    if (StringUtils.isEmpty(token)) {
      zillowNotifyLog.setResult("can't find token");
      log.info("zillowNotifyLog:{}", zillowNotifyLog);
      zillowNotifyLogDao.insert(zillowNotifyLog);
      return false;
    }
    log.info("zillowNotifyLog:{}", zillowNotifyLog);
    zillowNotifyLogDao.insert(zillowNotifyLog);
    try {
      Object result = null;
      switch (type) {
        case Contact -> {
          log.info("sync Contact");
          ContactRequest contactRequest = (ContactRequest) request.getBodyParam();
          result = zillowClient.updateContact(token, contactId, contactRequest);
        }
        case ContactStatus -> {
          log.info("sync ContactStatus");
          ContactStatus contactStatus = (ContactStatus) request.getBodyParam();
          result = zillowClient.updateContactStatus(token, contactId, contactStatus);
        }
        case Note -> {
          log.info("sync Note");
          String noteId = (String) request.getQueryParam();
          Note note = (Note) request.getBodyParam();
          String noteJson = JacksonUtils.toJson(note);
          log.info("note:{}", noteJson);
          if (StringUtils.isNotEmpty(noteId)) {
            result = zillowClient.updateContactNote(token, contactId, noteId, note);
          } else {
            result = zillowClient.addContactNote(token, contactId, note);
            String resultNoteId = getNoteId((String) result);
            if (StringUtils.isNotEmpty(resultNoteId)) {
              leadTimelineLogDao.updateTimelineLogByTimelineId(note.getLogId(), resultNoteId);
            }
          }
        }
        case Assign -> {
          log.info("sync Assign");
          Assignment assignment = (Assignment) request.getBodyParam();
          result = zillowClient.reassign(token, contactId, assignment);
        }
        case Insight -> {
          log.info("sync insight");
          Insight insight = (Insight) request.getBodyParam();
          result = zillowClient.updateInsight(token, contactId, insight);
        }
        default -> log.info("not support type:{}", type);
      }
      zillowNotifyLog.setStatus(Status.SUCCESS.getStatus());
      zillowNotifyLog.setRetryNum(zillowNotifyLog.getRetryNum() + 1);
      log.info("result:" + result);
      zillowNotifyLog.setResult(request != null ? JacksonUtils.toJson(result) : "null");
      zillowNotifyLogDao.updateStatus(zillowNotifyLog);
      return true;
    } catch (Exception e) {
      log.info("pushLead failed,request:{}", request, e);
      zillowNotifyLog.setStatus(Status.FAILED.getStatus());
      zillowNotifyLog.setResult("failed");
      zillowNotifyLog.setRetryNum(zillowNotifyLog.getRetryNum() + 1);
      zillowNotifyLogDao.updateStatus(zillowNotifyLog);
    }
    return false;
  }

  String getNoteId(String result) {
    try {
      Map<String, Object> map = JacksonUtils.fromJson(result, Map.class);
      Map<String, String> note = (Map<String, String>) map.get("data");
      return note.get("noteId");
    } catch (IOException e) {
      log.warn("result:{}", result, e);
    }
    return "";
  }

  @Override
  public boolean mergeLead(List<Long> leadIds) {
    log.info("mergeLead:{}", leadIds);
    if (CollectionUtils.isEmpty(leadIds)) {
      return false;
    }

    Map<Long, ZillowLead> zillowLeadMap = filterSyncedLead(leadIds);
    if (MapUtils.isEmpty(zillowLeadMap)) {
      log.info("lead was not zillow lead");
      return false;
    }
    leadIds = leadIds.stream().filter(leadId -> zillowLeadMap.containsKey(leadId))
        .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(leadIds)) {
      log.info("leadIds is empty");
      return false;
    }
    log.info("handle leadIds:{}", leadIds);
    List<LeadBo> leadBoList = leadOfflineService.getLeadsInIdList(leadIds);
    if (CollectionUtils.isEmpty(leadBoList)) {
      return false;
    }
    leadBoList.forEach(leadBo -> {
      try {
        ZillowLead zillowLead = zillowLeadManager.getZillowLeadByLeadId(leadBo.getId());
        if (zillowLead == null) {
          log.info("lead:{},zillowLead was null", leadBo.getId());
          return;
        }
        pushLeadDetail(leadBo, zillowLead);
      } catch (Exception e) {
        log.warn("push zillow error, leadId: {}, exception: ", leadBo.getId(), e);
      }
    });
    return true;
  }

  @Override
  public void assignLead(List<KafkaTimeline> timelineList) {
    log.debug("edit assignLead:{}", timelineList);
    timelineList.forEach(kafkaTimeline -> {
      ZillowLead zillowLead = zillowLeadManager.getZillowLeadByLeadId(kafkaTimeline.getLeadId());
      if (!agentAssignChange(kafkaTimeline)) {
        return;
      }
      if (zillowLead == null) {
        log.info("cant find zillowLead:{}", kafkaTimeline);
      }
      long assignAgentId = kafkaTimeline.getToId();
      List<User> userList = agentOfflineService.getAgentUserByIds(List.of(assignAgentId));
      if (CollectionUtils.isEmpty(userList)) {
        return;
      }
      User assignAgent = userList.get(0);
      String assignEmail = assignAgent.getAccount();
      ZillowOauth2Token token = zillowOauth2TokenService.getToken(assignAgent.getBaseId());
      if (token != null) {
        assignEmail = token.getZillowEmail();
      }
      ZillowRequest request = constructZillow(zillowLead);
      Assignment assignment = Assignment.builder().email(assignEmail).build();
      request.setBodyParam(assignment);
      pushLead(request, Type.Assign);
    });
  }

  @Override
  public boolean agentAssignChange(KafkaTimeline kafkaTimeline) {
    if (StringUtils.isEmpty(kafkaTimeline.getTimelineContent())
        || !kafkaTimeline.getTimelineContent().contains("role\":\"Agent\"")) {
      log.info("not assign agent");
      return false;
    }

    if (kafkaTimeline.getToType() == 6) {
      //assign to pond not sync to zillow
      log.info("assign to pond");
      return false;
    }
    if (kafkaTimeline.getToType() == 1) {
      return true;
    }
    return false;
  }

  @Override
  public void editLeadDetail(List<KafkaTimeline> timelineList) {
    if (CollectionUtils.isEmpty(timelineList)) {
      return;
    }
    //disconnect link when team lead change to private lead
    List<Long> changeToPrivateLeads = timelineList.stream().filter(this::checkLeadPrivacyChange)
        .map(KafkaTimeline::getLeadId).toList();
    if (CollectionUtils.isNotEmpty(changeToPrivateLeads)) {
      log.info("need to unlink:{}", changeToPrivateLeads);
      zillowLeadService.disconnectZillowLead(changeToPrivateLeads);
    }
    // filter lead attribute change
    List<Long> leadIds = timelineList.stream().filter(
            kafkaTimeline -> CollectionUtils.isEmpty(changeToPrivateLeads)
                || !changeToPrivateLeads.contains(kafkaTimeline.getLeadId()))
        .map(KafkaTimeline::getLeadId).distinct().collect(Collectors.toList());
    if (CollectionUtils.isEmpty(leadIds)) {
      log.info("leadIds is empty");
      return;
    }
    log.info("handle leadIds:{}", leadIds);
    List<LeadBo> leadBoList = leadOfflineService.getLeadsInIdList(leadIds);
    if (CollectionUtils.isEmpty(leadBoList)) {
      return;
    }
    leadBoList.forEach(leadBo -> {
      try {
        ZillowLead zillowLead = zillowLeadManager.getZillowLeadByLeadId(leadBo.getId());
        if (zillowLead == null) {
          log.info("lead:{},zillowLead was null", leadBo.getId());
          return;
        }
        pushLeadDetail(leadBo, zillowLead);
      } catch (Exception e) {
        log.warn("push zillow error, leadId: {}, exception: ", leadBo.getId(), e);
      }
    });
  }

  boolean checkLeadPrivacyChange(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    log.info("kafkaTimeline:{}", kafkaTimeline);
    String contentMap = kafkaTimeline.getTimelineContent();
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }
    if (contentMap.contains("Privacy was changed ") && contentMap.contains(
        "to \\\"Private Lead\\\"")) {
      return true;
      /*Pattern pattern = Pattern.compile("Privacy was changed from (.*) to (.*)");
      Matcher matcher = pattern.matcher(contentMap);
      if (matcher.find()) {
        String before = matcher.group(1);
        String after = matcher.group(2);
        log.info("before:{},after:{}", before, after);
        return StringUtils.containsAnyIgnoreCase(before, "team")
            && StringUtils.containsAnyIgnoreCase(after, "private");
      }*/
    }
    return false;
  }

  @Override
  public boolean checkLeadDetailChange(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    String contentMap = StringUtils.isNotEmpty(kafkaTimeline.getTimelineContent())
        ? kafkaTimeline.getTimelineContent() : kafkaTimeline.getContentMap();
    boolean change = false;
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }
    if (contentMap.contains("Lead Type was changed")) {
      change = true;
    }
    if (contentMap.contains("First Name") || contentMap.contains("Last Name")
        || contentMap.contains("Full Name was changed")) {
      change = true;
    }
    if (contentMap.contains("Email Address") && contentMap.contains("was changed to")) {
      // resotre email's change
      storeLeadEmailChange(kafkaTimeline.getLeadId(), contentMap);
      change = true;
    }
    if (contentMap.contains("Added new Email Address")) {
      // add primary email
      storeAddPrimaryEmail(kafkaTimeline.getLeadId(), contentMap);
      change = true;
    }
    if (contentMap.contains("Phone Number")) {
      change = true;
    }
    if (contentMap.contains("Mailing Address")) {
      change = true;
    }
    if (contentMap.contains("Privacy was changed")) {
      change = true;
    }
    return change;
  }

  void storeLeadEmailChange(long leadId, String contentMap) {
    try {
      List<LeadEmailChange> emailChangeList = Lists.newArrayList();
      Map map = JacksonUtils.fromJson(contentMap, Map.class);
      List<String> detail = (List<String>) map.get("detail");
      if (CollectionUtils.isEmpty(detail)) {
        return;
      }
      Pattern pattern = Pattern.compile("""
          Email Address \"(\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*)(\s\\(Primary\\))*\"\s+was changed to\s+\"(\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*)(\s\\(Primary\\))*\"""");
      for (String s : detail) {
        if (StringUtils.isEmpty(s) || !s.contains("Email Address")) {
          continue;
        }
        Matcher matcher = pattern.matcher(s);
        if (!matcher.find()) {
          continue;
        }
        String before = matcher.group(1);
        String after = matcher.group(6);
        log.info("email change before:{},after:{}", before, after);
        emailChangeList.add(LeadEmailChange.builder().before(before).after(after).build());
      }

      this.redisService.set(ZillowConstant.ZILLOW_AREA, getEmailChangeRedisKey(leadId),
          JacksonUtils.toJson(emailChangeList), (int) TimeUnit.MINUTES.toSeconds(30));
    } catch (Exception e) {
      log.info("storeLeadEmailChange failed,contentMap:{}", contentMap, e);
    }
  }

  void storeAddPrimaryEmail(long leadId, String contentMap) {
    try {
      Map map = JacksonUtils.fromJson(contentMap, Map.class);
      List<String> detail = (List<String>) map.get("detail");
      if (CollectionUtils.isEmpty(detail)) {
        log.info("log detail is empty");
        return;
      }
      for (String s : detail) {
        log.info("detail s:{}", s);
        if (StringUtils.isNotEmpty(s) && s.contains("Added new Email Address")) {
          this.redisService.set(ZillowConstant.ZILLOW_AREA, getAddPrimaryEmailRedisKey(leadId),
              leadId, (int) TimeUnit.MINUTES.toSeconds(30));
          return;
        }
      }
    } catch (Exception e) {
      log.info("storeAddPrimaryEmail failed,contentMap:{}", contentMap, e);
    }

  }

  String getEmailChangeRedisKey(long leadId) {
    return "email.change:" + leadId;
  }

  String getAddPrimaryEmailRedisKey(long leadId) {
    return "add.primary.email:" + leadId;
  }

  @Override
  public boolean checkLeadAttributeChangeBySystemLog(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    String contentMap = kafkaTimeline.getContentMap();
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }
    return contentMap.contains("The system auto-updated lead info");
  }


  @Override
  public void communicationLogs(List<KafkaTimeline> timelineList, CommunicationEnum communication) {
    log.debug("print {}:{}", communication, timelineList);
    switch (communication) {
      case Note -> noteLog(timelineList);
      case Call, Email, Txt -> communicationLog(timelineList, communication);
    }
  }

  void noteLog(List<KafkaTimeline> timelineList) {
    timelineList.forEach(kafkaTimeline -> {
      try {
        pushNote(kafkaTimeline);
      } catch (Exception e) {
        log.warn("push zillow, leadId: {}, exception: ", kafkaTimeline.getLeadId(), e);
      }
    });
  }

  /**
   * communication log for email; call ;txt ; note
   *
   * @param timelineList
   * @param communication
   */
  void communicationLog(List<KafkaTimeline> timelineList, CommunicationEnum communication) {
    if (CollectionUtils.isEmpty(timelineList)) {
      return;
    }
    Pair<Map<Long, String>, Map<Long, String>> nameMaps = collectNamesFromTimelines(timelineList);
    Map<Long, String> agentNames = nameMaps.getLeft();
    Map<Long, String> leadNames = nameMaps.getRight();
    
    timelineList.forEach(kafkaTimeline -> {
      try {
        String communicationLog = buildCommunicationLog(kafkaTimeline, communication, agentNames, leadNames);
        if (StringUtils.isEmpty(communicationLog)) {
          log.info("communicationLog is empty");
          return;
        }
        log.info("push communication log:{} to zillow", communicationLog);
        pushCommunicationToZillow(kafkaTimeline, communication, communicationLog);
      } catch (MsgException he) {
        log.info("error msg: {}", he.getMessage());
      } catch (Exception e) {
        log.warn("push to zillow error, leadId: {}, exception: ", kafkaTimeline.getLeadId(), e);
      }
    });
  }

  /**
   * Collect agent and lead names from timeline list
   * @param timelineList List of Kafka timelines
   * @return Pair containing agentNames (left) and leadNames (right)
   */
  @Override
  public Pair<Map<Long, String>, Map<Long, String>> collectNamesFromTimelines(List<KafkaTimeline> timelineList) {
    List<Long> agentIds = Lists.newArrayList();
    List<Long> leadIds = Lists.newArrayList();
    
    timelineList.forEach(kafkaTimeline -> {
      if (LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType() == kafkaTimeline.getFromType()) {
        agentIds.add(kafkaTimeline.getFromId());
      } else {
        leadIds.add(kafkaTimeline.getFromId());
      }
      if (LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType() == kafkaTimeline.getToType()) {
        agentIds.add(kafkaTimeline.getToId());
      } else {
        leadIds.add(kafkaTimeline.getToId());
      }
    });
    
    Map<Long, String> agentNames = getName(agentIds,
        LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType());
    Map<Long, String> leadNames = getName(leadIds, LeadTimelineFromToTypeEnum.Lead.getType());
    
    return Pair.of(agentNames, leadNames);
  }

  /**
   * Build communication log message for timeline
   * @param kafkaTimeline Kafka timeline
   * @param communication Communication type
   * @param agentNames Agent names map
   * @param leadNames Lead names map
   * @return Formatted communication log message
   */
  @Override
  public String buildCommunicationLog(KafkaTimeline kafkaTimeline, CommunicationEnum communication,
                                     Map<Long, String> agentNames, Map<Long, String> leadNames) {
    String fromName = getNameByType(agentNames, leadNames, kafkaTimeline.getFromType(),
        kafkaTimeline.getFromId());
    String toName = getNameByType(agentNames, leadNames, kafkaTimeline.getToType(),
        kafkaTimeline.getToId());
    
    if (StringUtils.isEmpty(fromName) && StringUtils.isEmpty(toName)) {
      log.info("kafkaTimeline:{},all name is empty", kafkaTimeline);
      return "";
    }
    
    // Handle lead call agent case (timeline type 86)
    if (kafkaTimeline.getTimelineType() == 86) {
      String tempName = fromName;
      fromName = toName;
      toName = tempName;
    }
    
    String logTemplate = "%s %s %s on %s";
    return String.format(logTemplate, fromName, communication.getOperation(),
        toName, formatTime(kafkaTimeline.getTimelineTime()));
  }

  String formatTime(long time) {
    Instant instant = Instant.ofEpochMilli(time);
    ZoneId zoneId = ZoneId.of("GMT-7");
    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM d, yy, h:mma");
    try {
      return formatter.format(localDateTime) + " (" + zoneId.getId() + ")";
    } catch (Exception e) {
      log.info("foramt time failed:{}", time, e);
      return formatter.format(LocalDateTime.now()) + " (" + zoneId.getId() + ")";
    }
  }

  void pushNote(KafkaTimeline timeline) throws IOException {
    Map<String, Object> map = JacksonUtils.json2Map(timeline.getTimelineContent());
    if ("true".equals(map.get("zillow"))) {
      log.info("ignore note from zillow. timeline:{},ref:{}", timeline.getId(),
          timeline.getRefId());
      return;
    }
    String note = Jsoup.parse((String) map.get("note")).text();
    pushCommunicationToZillow(timeline, CommunicationEnum.Note, note);
  }

  String getNameByType(Map<Long, String> agentNames, Map<Long, String> leadNames, int type,
                       long id) {
    return LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType() == type
        ? StringUtils.defaultIfEmpty(agentNames.get(id), "")
        : StringUtils.defaultIfEmpty(leadNames.get(id), "");
  }

  Map<Long, String> getName(List<Long> ids, int type) {
    if (LeadTimeline.LeadTimelineFromToTypeEnum.Agent.getType() == type) {
      List<User> userList = agentOfflineService.getAgentUserByIds(ids);
      if (CollectionUtils.isEmpty(userList)) {
        return Maps.newHashMap();
      }
      return userList.stream().collect(Collectors.toMap(User::getId, User::getFullName));
    } else {
      LeadQueryBo leadQueryBo = new LeadQueryBo();
      leadQueryBo.setSize(ids.size());
      leadQueryBo.setLeadIds(ids.stream().mapToLong(Long::longValue).toArray());
      leadQueryBo.setReturnField(
          new String[]{"data.lead.leadId", "data.lead.firstName", "data.lead.lastName"});
      SearchResultBo searchResultBo;
      try {
        searchResultBo = leadSearchService.search(leadQueryBo);
      } catch (Exception e) {
        log.warn("lead search:{} warn", leadQueryBo, e);
        return Maps.newHashMap();
      }
      if (searchResultBo == null || CollectionUtils.isEmpty(searchResultBo.getLeadDetailModels())) {
        log.info("getLeadDetailModels is empty");
        return Maps.newHashMap();
      }
      List<LeadDetailBo> leadDetailBoList = searchResultBo.getLeadDetailModels();
      if (CollectionUtils.isEmpty(leadDetailBoList)) {
        return Maps.newHashMap();
      }
      return leadDetailBoList.stream().map(LeadDetailBo::getLead).collect(
          Collectors.toMap(com.homethy.microservice.client.leadsearch.lead.LeadBo::getLeadId,
              (leadBo) -> leadBo.getFirstName() + " " + leadBo.getLastName()));
    }
  }

  void pushCommunicationToZillow(KafkaTimeline kafkaTimeline, CommunicationEnum communication,
                                 String content) {
    if (kafkaTimeline.getId() == 0 && kafkaTimeline.getRefId() == 0) {
      log.info("kafkaTimeline id and refId is 0, kafkaTimeline: {}", JacksonUtils.toJson(kafkaTimeline));
      return;
    }
    ZillowLead zillowLead = zillowLeadManager.getZillowLeadByLeadId(kafkaTimeline.getLeadId());
    if (zillowLead == null) {
      log.info("cant find zillow lead:{}", kafkaTimeline);
      return;
    }
    LeadTimelineLog leadTimelineLog = null;
    if (kafkaTimeline.getTimelineType() == 86) {
      //lead call agent timeline can not be edited,to handle muti lead has same phone number
    } else {
      if (kafkaTimeline.getRefId() != 0) {
        leadTimelineLog = leadTimelineLogDao.getLeadTimelineLogByRefId(kafkaTimeline.getRefId());
        if (!CommunicationEnum.Note.equals(communication) && leadTimelineLog != null) {
          log.info("log email call txt change not sync,kafkaTimeline:{},leadTimelineLog:{}",
              kafkaTimeline, leadTimelineLog);
          return;
        }
      } else if (leadTimelineLog == null && kafkaTimeline.getId() != 0) {
        leadTimelineLog = leadTimelineLogDao.getLeadTimelineLogByLeadId(
            LeadTimelineLog.builder().leadId(kafkaTimeline.getLeadId()).timelineType(
                    kafkaTimeline.getTimelineType()).timelineTime(kafkaTimeline.getTimelineTime())
                .build());
        if (leadTimelineLog != null) {
          log.info("log email call txt change not sync");
          return;
        }

      }
    }
    log.info("build");
    ZillowRequest request = constructZillow(zillowLead);
    request.setBodyParam(new Note(new Body(content)));
    if (leadTimelineLog != null) {
      log.info("update leadTimelineLog:{},content:{}", leadTimelineLog, content);
      request.setQueryParam(leadTimelineLog.getNoteId());
      request.setBodyParam(new Note(0L, leadTimelineLog.getRefId(), new Body(content)));
    } else {
      LeadTimelineLog timelineLog = LeadTimelineLog.builder().leadId(kafkaTimeline.getLeadId())
          .timelineId(kafkaTimeline.getId()).refId(kafkaTimeline.getRefId())
          .timelineType(kafkaTimeline.getTimelineType())
          .timelineTime(kafkaTimeline.getTimelineTime()).build();
      if (log.isDebugEnabled()) {
        log.debug("push note, timelineLog: {}", JacksonUtils.toJson(timelineLog));
      }
      int i = leadTimelineLogDao.insertOrUpdate(timelineLog);
      if (i <= 0) {
        log.info("replaced timeline:{},not sync to zillow", timelineLog);
        return;
      }
      request.setBodyParam(
          new Note(timelineLog.getId(), kafkaTimeline.getRefId(), new Body(content)));
      log.info("add leadTimelineLog:{},content:{}", timelineLog, content);
    }
    log.info("push");
    pushLead(request, Type.Note);
  }

  @Override
  public Map<Long, ZillowLead> filterSyncedLead(List<Long> leadIds) {
    List<ZillowLead> zillowLeads = zillowLeadDao.selectLeadsByLeadIds(leadIds);
    if (CollectionUtils.isEmpty(zillowLeads)) {
      return Maps.newHashMap();
    }
    Map<Long, ZillowLead> zillowLeadMap = zillowLeads.stream()
        .collect(Collectors.toMap(ZillowLead::getLeadId, Function.identity(), (a, b) -> a));
    zillowLeadManager.batchUpdate(zillowLeadMap);
    return zillowLeadMap;
  }

  @Override
  public boolean filterTimelineType(KafkaTimeline kafkaTimeline) {
    if (!TIMELINETYPELIST.contains(kafkaTimeline.getTimelineType())) {
      log.info("not support timeline type");
      return false;
    }
    if ((StringUtils.isNotEmpty(kafkaTimeline.getTimelineContent())
        && kafkaTimeline.getTimelineContent().contains("\"source\":\"ZILLOW_OAUTH\"")) || (
        StringUtils.isNotEmpty(kafkaTimeline.getContentMap()) && kafkaTimeline.getContentMap()
            .contains("\"source\":\"ZILLOW_OAUTH\""))) {
      log.info("zillow sync to chime");
      return false;
    }

    return isValidTimeline(kafkaTimeline);

  }

  @Override
  public boolean isValidTimeline(KafkaTimeline kafkaTimeline) {
    switch (kafkaTimeline.getTimelineType()) {
      case 21 -> {
        return checkLeadDetailChange(kafkaTimeline);
      }
      case 93 -> {//zapier update lead
        return true;
      }
      case 80 -> {
        // exists lead attribute change by third-party (such as open-api,mail-parser..)
        return checkLeadAttributeChangeBySystemLog(kafkaTimeline);
      }
      case 31 -> {
        //assign
        return agentAssignChange(kafkaTimeline);
      }
      case 26, 124, 6, 128, 96, 130, 7, 8, 25, 42, 54, 55, 87, 28, 43, 56, 125, 24, 16 -> {
        return true;
      }
      default -> {
        return false;
      }
    }
  }


  @Override
  public void pushZillowStatus(long leadId, int status) {
    ZillowLead zillowLead = zillowLeadManager.getZillowLeadByLeadId(leadId);
    if (zillowLead == null || status == zillowLead.getZillowStatus()) {
      log.info("status not change");
      return;
    }
    ZillowRequest request = constructZillow(zillowLead);
    request.setBodyParam(new ContactStatus(status));
    pushLead(request, Type.ContactStatus);
  }

  @Override
  public void pushLeadDetail(LeadBo leadBo, ZillowLead zillowLead) {
    LeadUserBo leadUserBo = leadUserKernelService.getLeadUserById(leadBo.getLeadUserId());
    String email = getEmail(leadUserBo, zillowLead);
    LeadPropertyVoBo mailingProperty = leadPropertyService.getMailingProperty(
        leadBo.getLeadUserId());
    log.info("mail address:{}", mailingProperty);
    pushLeadDetail(leadBo, leadUserBo, mailingProperty, email, zillowLead);
  }

  @Override
  public void pushLeadDetail(LeadBo leadBo, LeadUserBo leadUserBo, LeadPropertyVoBo mailingProperty,
      String email, ZillowLead zillowLead) {
    ContactRequest contactRequest = ContactRequest.builder().firstName(leadUserBo.getFirstName())
        .lastName(leadUserBo.getLastName()).email(email)
        .build();
    log.info("lead id:{},leadUserId:{},info:{}", leadBo.getId(), leadBo.getLeadUserId(),
        contactRequest);
    if (mailingProperty != null) {
      ZillowAddress zillowAddress = new ZillowAddress();
      zillowAddress.setStateCode(mailingProperty.getState());
      zillowAddress.setAddress(mailingProperty.getStreetAddress());
      zillowAddress.setCity(mailingProperty.getCity());
      zillowAddress.setZip(mailingProperty.getZipcode());
      contactRequest.setAddress(zillowAddress);
    }
    ZillowRequest request = constructZillow(zillowLead);
    List<ContactRequest> pushRequest = getPhonePush(contactRequest, leadUserBo.getId(), zillowLead);
    pushRequest.forEach(e -> {
      request.setBodyParam(e);
      log.info("request:{}", request);
      boolean result = pushLead(request, Type.Contact);
      if (result) {
        zillowLeadService.updateLastPhone(e.getPhone(), zillowLead.getId());
      }
    });
    if (StringUtils.isNotEmpty(email)) {
      zillowLeadDao.updateLastEmailByEmail(email, zillowLead.getId());
    }
    Insight insight = Insight.builder().contactType(getContactType(leadBo.getLeadTypes())).build();
    request.setBodyParam(insight);
    pushLead(request, Type.Insight);
  }

  private List<ContactRequest> getPhonePush(ContactRequest contactRequest, long leadUserId,
                                            ZillowLead zillowLead) {
    List<ContactRequest> requestList = new LinkedList<>();
    List<UserPhoneBo> phones =
        userEmailPhoneService.getUserPhonesByUserId(leadUserId);
    if (CollectionUtils.isEmpty(phones)) {
      phones = new LinkedList<>();
    }
    List<String> userPhones =
        phones.stream().map(UserPhoneBo::getPhone).collect(Collectors.toList());
    List<String> oldZillowPhone = new LinkedList<>();
    if (StringUtils.isNotEmpty(zillowLead.getMobile())) {
      oldZillowPhone.add(zillowLead.getMobile());
    }
    if (StringUtils.isNotEmpty(zillowLead.getWork())) {
      oldZillowPhone.add(zillowLead.getWork());
    }
    if (StringUtils.isNotEmpty(zillowLead.getHome())) {
      oldZillowPhone.add(zillowLead.getHome());
    }
    log.info("userPhones: {}, zillowLead: {}", JacksonUtils.toJson(userPhones),
        JacksonUtils.toJson(zillowLead));
    if (!userPhones.contains(zillowLead.getMobile())) {
      ZillowPhone mobile =
          ZillowPhone.builder().type(ZillowPhone.PhoneType.mobile.getType()).phoneNumber("").build();
      for (String phone : userPhones) {
        if (!oldZillowPhone.contains(phone)) {
          mobile.setPhoneNumber(phone);
          userPhones.remove(phone);
          break;
        }
      }
      ContactRequest mobileRequest = new ContactRequest();
      BeanUtils.copyProperties(contactRequest, mobileRequest);
      mobileRequest.setPhone(mobile);
      requestList.add(mobileRequest);
    }
    if (!userPhones.contains(zillowLead.getWork())) {
      ZillowPhone work =
          ZillowPhone.builder().type(ZillowPhone.PhoneType.work.getType()).phoneNumber("").build();
      for (String phone : userPhones) {
        if (!oldZillowPhone.contains(phone)) {
          work.setPhoneNumber(phone);
          userPhones.remove(phone);
          break;
        }
      }
      ContactRequest workRequest = new ContactRequest();
      BeanUtils.copyProperties(contactRequest, workRequest);
      workRequest.setPhone(work);
      requestList.add(workRequest);
    }
    if (!userPhones.contains(zillowLead.getHome())) {
      ZillowPhone home =
          ZillowPhone.builder().type(ZillowPhone.PhoneType.home.getType()).phoneNumber("").build();
      for (String phone : userPhones) {
        if (!oldZillowPhone.contains(phone)) {
          home.setPhoneNumber(phone);
          break;
        }
      }
      contactRequest.setPhone(home);
      requestList.add(contactRequest);
    }
    if (CollectionUtils.isEmpty(requestList)) {
      requestList.add(contactRequest);
    }
    return requestList;
  }

  String getEmail(LeadUserBo leadUserBo, ZillowLead zillowLead) {
    if (this.redisService.exists(ZillowConstant.ZILLOW_AREA,
        getAddPrimaryEmailRedisKey(zillowLead.getLeadId()))) {
      this.redisService.del(ZillowConstant.ZILLOW_AREA,
          getAddPrimaryEmailRedisKey(zillowLead.getLeadId()));

      List<UserEmailBo> userEmailBoList = userEmailPhoneService.getUserEmailsByUserId(
          leadUserBo.getId());
      log.info("userEmailBoList:{}", userEmailBoList);
      if (CollectionUtils.isEmpty(userEmailBoList)) {
        log.info("cant find userEmail lead:{}", zillowLead.getLeadId());
      }
      if (StringUtils.isNotEmpty(zillowLead.getLeadLastEmail())) {
        UserEmailBo userEmailBo =
            userEmailBoList.stream().filter(e -> zillowLead.getLeadLastEmail().equals(e.getEmail())).findAny().orElse(null);
        if (userEmailBo != null) {
          return userEmailBo.getEmail();
        }
      }
      UserEmailBo userEmailBo =
          userEmailBoList.stream().filter(UserEmailBo::isPrimary).findAny().orElse(null);
      if (userEmailBo != null) {
        return userEmailBo.getEmail();
      }
      return userEmailBoList.get(0).getEmail();
    }

    String emailChangeCache = this.redisService.get(ZillowConstant.ZILLOW_AREA,
        getEmailChangeRedisKey(zillowLead.getLeadId()));
    if (StringUtils.isEmpty(emailChangeCache)) {
      return null;
    }
    try {
      List<LeadEmailChange> emailChangeList = JacksonUtils.fromJson2List(emailChangeCache,
          LeadEmailChange.class);
      log.info("emailChangeList:{}", emailChangeList);
      if (CollectionUtils.isEmpty(emailChangeList)) {
        return null;
      }
      String findEmail = "";
      for (LeadEmailChange leadEmailChange : emailChangeList) {
        if (leadEmailChange.getBefore().equalsIgnoreCase(zillowLead.getLeadLastEmail())) {
          findEmail = leadEmailChange.getAfter();
          break;
        }
      }
      log.info("findEmail:{}", findEmail);
      this.redisService.del(ZillowConstant.ZILLOW_AREA,
          getEmailChangeRedisKey(zillowLead.getLeadId()));
      return findEmail;
    } catch (IOException e) {
      log.info("getEmail failed:{}", emailChangeCache, e);
      return null;
    }
  }

  String getContactType(List<Integer> leadTypes) {
    if (leadTypes.contains(LeadListingTypeEnum.Buy.getType()) && leadTypes.contains(LeadListingTypeEnum.Sale.getType())) {
      return ContactType.Both.getType();
    }
    if (leadTypes.contains(LeadListingTypeEnum.Buy.getType())) {
      return ContactType.Buyer.getType();
    }
    if (leadTypes.contains(LeadListingTypeEnum.Sale.getType())) {
      return ContactType.Seller.getType();
    }
    return ContactType.Unknown.getType();
  }

  ZillowRequest constructZillow(ZillowLead zillowLead) {
    String token = zillowOauth2TokenService.getBearerTokenByZillowLead(zillowLead);
    return ZillowRequest.builder().token(token).contactId(zillowLead.getContactId())
        .agentId(zillowLead.getOwnershipId()).leadId(zillowLead.getLeadId()).build();
  }


}

package com.homethy.zillow.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.ZillowConnect;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.constant.ZillowOauth2Token;
import com.homethy.zillow.model.constant.ZillowStateInfo;

public interface ZillowOauth2TokenService {
  ZillowOauth2Token getToken(long baseId);

  ZillowOauth2Token getToken(OwnershipInfo ownershipInfo);

  ZillowOauth2Token getToken(ZillowLead zillowLead);

//  void fillIntegrationId();

  boolean isBinding(long userId);

  void disconnect(long userId);
  void disconnect(OwnershipInfo ownershipInfo);

//  void disconnectBaseId(long baseId);

  void disconnectAndTimeline(String integrationId);

  String getBearerTokenByAgentId(long agentId);

  String getBearerTokenByZillowLead(ZillowLead zillowLead);

  boolean checkZillowLead(ZillowLead zillowLead);

  User getUser(long userId);

  int binding(String code, String state);

  ZillowStateInfo parserState(String state);

  ZillowConnect getConnect(OwnershipInfo ownershipInfo, long operUserId);

  boolean exiestsToken(long baseId);
}

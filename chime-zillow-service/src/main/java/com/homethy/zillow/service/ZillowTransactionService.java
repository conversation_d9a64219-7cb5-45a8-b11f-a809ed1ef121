package com.homethy.zillow.service;

import com.homethy.zillow.model.vo.ZillowTrVO;
import java.util.List;

public interface ZillowTransactionService {

  long saveOrUpdate(long transactionId, long userId);

  List<Long> checkZillowTrs(List<Long> ids);

  void deleteByTrIds(List<Long> ids);

  boolean processTrForMergeLead(long fromLeadId, long toLeadId);

  boolean deleteTrForLeadMerge(List<Long> mergedLeadIds);

  List<ZillowTrVO> getZillowTrLinks(List<Long> trIds);

   List<ZillowTrVO> getFubTrLinks(List<Long> trIds) ;

  List<ZillowTrVO> mergeList(List<ZillowTrVO> zillowIds,List<ZillowTrVO> fubIds);


}

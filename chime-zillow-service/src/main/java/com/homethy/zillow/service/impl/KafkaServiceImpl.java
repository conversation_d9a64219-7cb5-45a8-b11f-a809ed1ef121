package com.homethy.zillow.service.impl;

import com.homethy.kafka.producer.KafkaProducerFactory;
import com.homethy.zillow.service.KafkaService;
import com.homethy.zillow.util.EnvUtil;
import com.homethy.zillow.util.MdcUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.stereotype.Service;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class KafkaServiceImpl implements KafkaService {

  private Producer<Long, String> producer;
  private Producer<Long, String> mskProducer;

  public Producer<Long, String> getMskProducer() {
    if (mskProducer == null) {
      mskProducer = KafkaProducerFactory.getMskProducer();
    }
    return mskProducer;
  }
  public Producer<Long, String> getProducer() {
    if (producer == null) {
      producer = KafkaProducerFactory.getDefaultProducer();
    }
    return producer;
  }

  @Override
  public void sendMskMessage(String topic, long key, String content) {
    sendMessage(topic, key, content, true);
  }

  public void sendMessage(String topic, long key, String content, boolean isMsk) {
    log.info("send kafka message send:{},{}", key, topic);
    if (StringUtils.isEmpty(topic) || key < 0) {
      return;
    }
    Producer<Long, String> produce = isMsk ? getMskProducer() : getProducer();
    String traceId = MdcUtil.getTraceId();
    String spanId = MdcUtil.getSpanId();
    ProducerRecord<Long, String> producerRecord =
        new ProducerRecord<>(topic, key, content);

    Future<RecordMetadata> future = produce.send(producerRecord,
        (RecordMetadata metadata, Exception e) -> {
          MdcUtil.setTrace(traceId);
          MdcUtil.setSpanId(spanId);
          if (e != null) {
            log.error("send fail, key: {}, topic:{},content:{}"
                , key, topic, content, e);
          } else {
            log.info("message send to partition {},offset: {}, key: {}, "
                    + "topic:{}",
                metadata.partition(), metadata.offset(), key, topic);
          }
        });
    if (isSync()) {
      try {
        future.get(1, TimeUnit.MINUTES);
      } catch (Exception e) {
        log.error("send fail, key: {}, topic:{},content:{}", key, topic, content, e);
      }
    }
  }

  private boolean isSync() {
    return EnvUtil.isLambda();
  }
}

package com.homethy.leadsync.business;

import com.google.common.collect.Lists;
import com.homethy.microservice.client.lead.LeadUserKernelService;
import com.homethy.microservice.client.lead.apiV3.LeadPropertyService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadUserBo;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.zillow.model.po.LeadEmailChange;
import com.homethy.zillow.service.LeadPushService;
import com.homethy.zillow.util.ContextUtil;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public abstract class AbstractLeadSyncHandler<T> implements BusinessTypeHandler {

  /**
   * Default timeline types that can be processed
   * Subclasses can override this method to provide custom timeline types
   */
  protected List<Integer> getTimelineTypes() {
    return List.of(21, 80, 31, 26, 124, 6, 128, 96, 130,
        7, 8, 25, 42, 54, 55, 87, 28, 43, 56, 125, 24, 16);
  }

  /**
   * Default communication timeline types that can be processed
   * Subclasses can override this method to provide custom communication timeline types
   */
  protected List<Integer> getCommunicationTimelineTypes() {
    return List.of(93, 26, 62, 96, 124, 6,
        128, 96, 130, 7, 8, 25, 42, 54, 55, 87, 86, 28, 43, 56, 125, 24, 80);
  }

  protected static LeadOfflineService leadOfflineService;
  protected static LeadUserKernelService leadUserKernelService;
  protected static LeadPropertyService leadPropertyService;
  protected static LeadPushService leadPushService;

  // Lazy initialization methods
  protected static LeadOfflineService getLeadOfflineService() {
    if (leadOfflineService == null) {
      leadOfflineService = ContextUtil.getBean(LeadOfflineService.class);
    }
    return leadOfflineService;
  }

  protected static LeadUserKernelService getLeadUserKernelService() {
    if (leadUserKernelService == null) {
      leadUserKernelService = ContextUtil.getBean(LeadUserKernelService.class);
    }
    return leadUserKernelService;
  }

  protected static LeadPropertyService getLeadPropertyService() {
    if (leadPropertyService == null) {
      leadPropertyService = ContextUtil.getBean(LeadPropertyService.class);
    }
    return leadPropertyService;
  }

  protected static LeadPushService getLeadPushService() {
    if (leadPushService == null) {
      leadPushService = ContextUtil.getBean(LeadPushService.class);
    }
    return leadPushService;
  }

  private interface TimelineHandler<T> {

    void handle(int timelineType, List<KafkaTimeline> list, Map<Long, T> leadMap);
  }

  private void batchHandle(List<KafkaTimeline> timelineList,
      Function<List<KafkaTimeline>, List<KafkaTimeline>> typeFilter, TimelineHandler<T> handler) {
    List<KafkaTimeline> filtered = typeFilter.apply(timelineList);
    if (CollectionUtils.isEmpty(filtered)) {
      return;
    }
    Map<Long, T> leadMap = filterSyncedLead(filtered);
    List<KafkaTimeline> valid = filterValidLeads(filtered, leadMap);
    if (CollectionUtils.isEmpty(valid)) {
      return;
    }
    Map<Integer, List<KafkaTimeline>> grouped = valid.stream()
        .collect(Collectors.groupingBy(KafkaTimeline::getTimelineType));
    for (Map.Entry<Integer, List<KafkaTimeline>> entry : grouped.entrySet()) {
      handler.handle(entry.getKey(), entry.getValue(), leadMap);
    }
  }

  @Override
  public void batchHandleTimeLine(List<KafkaTimeline> timelineList) {
    try {
      batchHandle(timelineList, this::filterTimelineType, this::handleByType);
    } catch (Exception e) {
      log.warn("batchHandleTimeLine failed", e);
    }
  }

  @Override
  public void batchHandleCommunicationTimeLine(List<KafkaTimeline> timelineList) {
    try {
      batchHandle(timelineList, this::filterCommunicationTimelineType,
          this::handleCommunicationByType);
    } catch (Exception e) {
      log.warn("batchHandleCommunicationTimeLine failed", e);
    }
  }

  protected List<KafkaTimeline> filterTimelineType(List<KafkaTimeline> list) {
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newArrayList();
    }
    return list.stream()
        .filter(timeline -> getTimelineTypes().contains(timeline.getTimelineType()))
        .filter(this::supports)
        .toList();
  }

  protected List<KafkaTimeline> filterCommunicationTimelineType(List<KafkaTimeline> list) {
    if (CollectionUtils.isEmpty(list)) {
      return Lists.newArrayList();
    }
    return list.stream()
        .filter(timeline -> getCommunicationTimelineTypes().contains(timeline.getTimelineType()))
        .toList();
  }

  protected Map<Long, T> filterSyncedLead(List<KafkaTimeline> list) {
    Map<Long, T> result = new java.util.HashMap<>();
    for (KafkaTimeline timeline : list) {
      T platformLead = getPlatformLead(timeline.getLeadId());
      if (platformLead != null) {
        result.put(timeline.getLeadId(), platformLead);
      }
    }
    return result;
  }

  protected List<KafkaTimeline> filterValidLeads(List<KafkaTimeline> list, Map<Long, T> leadMap) {
    if (MapUtils.isEmpty(leadMap)) {
      return Lists.newArrayList();
    }
    return list.stream().filter(t -> leadMap.containsKey(t.getLeadId())).toList();
  }

  protected void handleByType(int timelineType, List<KafkaTimeline> list, Map<Long, T> leadMap) {
    switch (timelineType) {
      case 21: {
        editLeadDetail(list, leadMap);
        break;
      }
      case 31: {
        assignLead(list, leadMap);
        break;
      }
      case 26, 124, 6, 128, 96, 130, 7: {
        communicationLogs(list, leadMap, CommunicationEnum.Email);
        break;
      }
      case 8, 25, 42, 54, 55, 87: {
        communicationLogs(list, leadMap, CommunicationEnum.Call);
        break;
      }
      case 28, 43, 56, 125, 24: {
        communicationLogs(list, leadMap, CommunicationEnum.Txt);
        break;
      }
      case 16: {
        communicationLogs(list, leadMap, CommunicationEnum.Note);
        break;
      }
      default: {
        log.info("not support type");
      }
    }
  }

  protected void handleCommunicationByType(int timelineType, List<KafkaTimeline> list,
      Map<Long, T> leadMap) {
    switch (timelineType) {
      case 80, 93 ->
        // exists lead attribute change by third-party (such as open-api,mail-parser,zapier.)
          editLeadDetail(list, leadMap);
      case 26, 6, 124, 62, 96 ->
        //email
          communicationLogs(list, leadMap, CommunicationEnum.Email);
      case 8, 25, 42, 54, 55, 87, 86 ->
        //call
          communicationLogs(list, leadMap, CommunicationEnum.Call);
      case 28, 43, 56, 125, 24 ->
        //txt
          communicationLogs(list, leadMap, CommunicationEnum.Txt);
      case 16 ->
        //note
          communicationLogs(list, leadMap, CommunicationEnum.Note);
      default -> log.info("not support timeline type");
    }
  }

  public void editLeadDetail(List<KafkaTimeline> timelineList,Map<Long, T> leadMap) {
    if (timelineList == null || timelineList.isEmpty()) {
      return;
    }
    List<Long> platformSpecificLeads = getPlatformSpecificLeadsForDisconnect(timelineList);
    if (platformSpecificLeads != null && !platformSpecificLeads.isEmpty()) {
      doDisconnectLeads(platformSpecificLeads);
    }
    List<Long> leadIds = timelineList.stream()
        .map(KafkaTimeline::getLeadId)
        .filter(leadId -> platformSpecificLeads == null || platformSpecificLeads.isEmpty()
            || !platformSpecificLeads.contains(leadId))
        .distinct()
        .collect(java.util.stream.Collectors.toList());
    if (leadIds.isEmpty()) {
      return;
    }
    List<LeadBo> leadBoList = getLeadOfflineService().getLeadsInIdList(leadIds);
    if (leadBoList == null || leadBoList.isEmpty()) {
      return;
    }
    for (LeadBo leadBo : leadBoList) {
      try {
        T platformLead = leadMap.get(leadBo.getId());
        if (platformLead == null) {
          continue;
        }
        doPushLeadDetail(leadBo, platformLead);
      } catch (Exception e) {
        log.warn("push platform lead detail error, exception: ", e);
      }
    }
  }

  protected List<Long> getPlatformSpecificLeadsForDisconnect(List<KafkaTimeline> timelineList) {
    if (timelineList == null || timelineList.isEmpty()) {
      return Collections.emptyList();
    }
    return timelineList.stream()
        .filter(this::checkLeadPrivacyChange)
        .map(KafkaTimeline::getLeadId)
        .toList();
  }

  protected abstract void doDisconnectLeads(List<Long> leadIds);

  protected abstract T getPlatformLead(Long leadId);

  protected void doPushLeadDetail(LeadBo leadBo, T platformLead) {
    pushLeadToPlatform(leadBo, platformLead);
  }

  protected String getLeadEmail(LeadUserBo leadUserBo, T platformLead) {
    if (leadUserBo == null || platformLead == null) {
      return null;
    }
    if (!(platformLead instanceof ZillowLead zillowLead)) {
      return null;
    }
    RedisService redisService = ContextUtil.getBean(RedisService.class);
    String addPrimaryKey = "add.primary.email:" + zillowLead.getLeadId();
    if (redisService.exists(ZillowConstant.ZILLOW_AREA, addPrimaryKey)) {
      redisService.del(ZillowConstant.ZILLOW_AREA, addPrimaryKey);
      if (StringUtils.isNotEmpty(zillowLead.getLeadLastEmail())) {
        return zillowLead.getLeadLastEmail();
      }
    }
    String emailChangeKey = "email.change:" + zillowLead.getLeadId();
    String emailChangeCache = redisService.get(ZillowConstant.ZILLOW_AREA, emailChangeKey);
    if (StringUtils.isEmpty(emailChangeCache)) {
      return null;
    }
    try {
      List<LeadEmailChange> emailChangeList = JacksonUtils.fromJson2List(emailChangeCache,
          LeadEmailChange.class);
      if (emailChangeList == null || emailChangeList.isEmpty()) {
        return null;
      }
      for (LeadEmailChange leadEmailChange : emailChangeList) {
        if (leadEmailChange.getBefore().equalsIgnoreCase(zillowLead.getLeadLastEmail())) {
          redisService.del(ZillowConstant.ZILLOW_AREA, emailChangeKey);
          String after = leadEmailChange.getAfter();
          log.info("findEmail:{}", after);
          return after;
        }
      }
    } catch (IOException e) {
      log.info("getEmail failed:{}", emailChangeCache, e);
      return null;
    }
    return null;
  }

  protected abstract void pushLeadToPlatform(LeadBo leadBo, T platformLead);


  protected abstract void assignLead(List<KafkaTimeline> list, Map<Long, T> leadMap);

  protected abstract void communicationLogs(List<KafkaTimeline> list, Map<Long, T> leadMap,
      CommunicationEnum type);

  @Override
  public abstract boolean supports(KafkaTimeline timeline);

  protected boolean checkLeadPrivacyChange(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    String contentMap = kafkaTimeline.getTimelineContent();
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }
    return contentMap.contains("Privacy was changed ") && contentMap.contains(
        "to \\\"Private Lead\\\"");
  }


  // Timeline type constants for better readability
  private static final int TIMELINE_TYPE_LEAD_DETAIL_CHANGE = 21;
  private static final int TIMELINE_TYPE_ZAPIER_UPDATE = 93;
  private static final int TIMELINE_TYPE_SYSTEM_LOG = 80;
  private static final int TIMELINE_TYPE_ASSIGN = 31;


  
  // Other timeline types that are always valid
  private static final Set<Integer> ALWAYS_VALID_TIMELINE_TYPES = Set.of(
      26, 124, 6, 128, 96, 130, 7, 8, 25, 42, 54, 55, 87, 28, 43, 56, 125, 24, 16
  );
  
  // Content change keywords for lead detail changes
  private static final String[] LEAD_DETAIL_CHANGE_KEYWORDS = {
      "Lead Type was changed",
      "First Name", "Last Name", "Full Name was changed",
      "Email Address",
      "Phone Number",
      "Mailing Address",
      "Privacy was changed"
  };
  
  private static final String SYSTEM_AUTO_UPDATE_MESSAGE = "The system auto-updated lead info";
  private static final String AGENT_ROLE_INDICATOR = "role\":\"Agent\"";
  private static final int ASSIGN_TO_POND_TYPE = 6;
  private static final int ASSIGN_TO_AGENT_TYPE = 1;

  /**
   * Check if timeline is valid for processing
   * 
   * @param kafkaTimeline timeline to validate
   * @return true if timeline should be processed
   */
  public boolean isValidTimeline(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    
    int timelineType = kafkaTimeline.getTimelineType();
    // Check if it's an always valid timeline type
    if (ALWAYS_VALID_TIMELINE_TYPES.contains(timelineType)) {
      return true;
    }
    // Handle specific timeline types
    return switch (timelineType) {
      case TIMELINE_TYPE_LEAD_DETAIL_CHANGE -> {
        String contentMap = getTimelineContent(kafkaTimeline);
        yield checkLeadDetailChange(kafkaTimeline, contentMap);
      }
      case TIMELINE_TYPE_ZAPIER_UPDATE -> true; // zapier update lead
      case TIMELINE_TYPE_SYSTEM_LOG -> checkLeadAttributeChangeBySystemLog(kafkaTimeline);
      case TIMELINE_TYPE_ASSIGN -> agentAssignChange(kafkaTimeline);
      default -> false;
    };
  }

  /**
   * Get timeline content with fallback to contentMap
   */
  private String getTimelineContent(KafkaTimeline kafkaTimeline) {
    return StringUtils.isNotEmpty(kafkaTimeline.getTimelineContent()) 
        ? kafkaTimeline.getTimelineContent() 
        : kafkaTimeline.getContentMap();
  }

  /**
   * Check if lead detail has changed based on content
   * 
   * @param kafkaTimeline timeline object
   * @param contentMap content to analyze
   * @return true if lead detail has changed
   */
  public boolean checkLeadDetailChange(KafkaTimeline kafkaTimeline, String contentMap) {
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }
    // Check for other changes
    for (String keyword : LEAD_DETAIL_CHANGE_KEYWORDS) {
      if (contentMap.contains(keyword)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if lead attribute was changed by system log
   * 
   * @param kafkaTimeline timeline to check
   * @return true if system auto-updated lead info
   */
  public boolean checkLeadAttributeChangeBySystemLog(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    
    String contentMap = kafkaTimeline.getContentMap();
    return StringUtils.isNotEmpty(contentMap) && 
           contentMap.contains(SYSTEM_AUTO_UPDATE_MESSAGE);
  }

  /**
   * Check if agent assignment has changed
   * 
   * @param kafkaTimeline timeline to check
   * @return true if assignment changed to agent
   */
  public boolean agentAssignChange(KafkaTimeline kafkaTimeline) {
    if (kafkaTimeline == null) {
      return false;
    }
    
    String timelineContent = kafkaTimeline.getTimelineContent();
    if (StringUtils.isEmpty(timelineContent) || 
        !timelineContent.contains(AGENT_ROLE_INDICATOR)) {
      log.info("Timeline does not contain agent assignment");
      return false;
    }

    int toType = kafkaTimeline.getToType();
    if (toType == ASSIGN_TO_POND_TYPE) {
      log.info("Assignment is to pond, not agent");
      return false;
    }
    return toType == ASSIGN_TO_AGENT_TYPE;
  }

}
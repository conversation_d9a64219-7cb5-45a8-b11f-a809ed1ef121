package com.homethy.leadsync.business;

import com.google.common.collect.Lists;
import com.homethy.fub.dao.FubTimelineRefDao;
import com.homethy.fub.dao.LeadNoteDao;
import com.homethy.fub.service.FubLeadSyncService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubTimelineRefBo;
import com.homethy.zillow.model.constant.fub.LeadNoteBo;
import com.homethy.zillow.model.po.KafkaTimeline;
import java.util.List;
import java.util.Map;

import com.homethy.zillow.service.ZillowLeadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FubLeadSyncHandler extends AbstractLeadSyncHandler<FubLead> {

  private static final String ORIGINAL_SOURCE_FUB = "\"originalSource\":\"FUB\"";
  private static final String SOURCE_FUB_OAUTH = "\"source\":\"FUB_OAUTH\"";

  private List<Integer> getNoteTimelineTypes() {
    return List.of( 8, 25, 42, 54, 55, 87, 28, 43, 56, 125, 24, 16);
  }

  @Autowired
  FubLeadSyncService fubLeadSyncService;
  @Autowired
  FubTimelineRefDao fubTimelineRefDao;
  @Autowired
  LeadNoteDao leadNoteDao;
  @Autowired
  ZillowLeadService zillowLeadService;

  @Override
  protected void doDisconnectLeads(List<Long> leadIds) {
    // Implementation for disconnecting leads
  }

  @Override
  protected FubLead getPlatformLead(Long leadId) {
    return fubLeadSyncService.getFubLead(leadId);
  }

  @Override
  protected void pushLeadToPlatform(LeadBo leadBo, FubLead platformLead) {
    // Create a timeline object to pass to the service method
    KafkaTimeline timeline = new KafkaTimeline();
    timeline.setLeadId(leadBo.getId());

    // Use the service method that handles all the lead information setting
    fubLeadSyncService.pushLeadToPlatform(timeline, platformLead);
  }

  @Override
  protected void assignLead(List<KafkaTimeline> list, Map<Long, FubLead> leadMap) {
    fubLeadSyncService.assignLead(list, leadMap);
  }

  @Override
  protected void communicationLogs(List<KafkaTimeline> list, Map<Long, FubLead> leadMap,
      CommunicationEnum type) {
    List<Long> timelineIds = list.stream().map(KafkaTimeline::getId).filter(id -> id > 0).toList();
    List<FubTimelineRefBo> timelineRefs = CollectionUtils
        .isNotEmpty(timelineIds) ? fubTimelineRefDao.getTimelineRefs(timelineIds)
        : Lists.newArrayList();
    List<Long> existsTimelineIds = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(timelineRefs)) {
      existsTimelineIds = timelineRefs.stream().map(FubTimelineRefBo::getTimelineId).toList();
    }
    for (KafkaTimeline kafkaTimeline : list) {
      if (!leadMap.containsKey(kafkaTimeline.getLeadId())) {
        log.info("not fubLead:{}", JacksonUtils.toJson(kafkaTimeline));
        continue;
      }
      if (kafkaTimeline.getId() > 0 && existsTimelineIds.contains(kafkaTimeline.getId())) {
        log.info("already exists:{}",JacksonUtils.toJson(kafkaTimeline));
        continue;
      }
      FubLead fubLead = leadMap.get(kafkaTimeline.getLeadId());
      if(fubLead != null){
        long leadId = fubLead.getLeadId();
        if (zillowLeadService.checkZillowLead(leadId)) {
           log.info("LeadId: {} is a Zillow lead, skipping FUB push to avoid conflicts", leadId);
           continue;
        }
      }else{
        continue;
      }
      fubLeadSyncService.communicationLogs(kafkaTimeline, fubLead, type);
    }
  }

  @Override
  protected List<KafkaTimeline> filterValidLeads(List<KafkaTimeline> list, Map<Long, FubLead> leadMap) {
    if (MapUtils.isEmpty(leadMap)) {
      recordTimeline(list);
      return Lists.newArrayList();
    }
    
    List<KafkaTimeline> validTimelines = list.stream()
        .filter(t -> leadMap.containsKey(t.getLeadId()))
        .toList();
    
    if (!validTimelines.isEmpty()) {
      List<KafkaTimeline> invalidTimelines = list.stream()
          .filter(t -> !leadMap.containsKey(t.getLeadId()))
          .toList();
      if (!invalidTimelines.isEmpty()) {
        recordTimeline(invalidTimelines);
      }
    } else {
      recordTimeline(list);
    }
    
    return validTimelines;
  }

  protected void recordTimeline(List<KafkaTimeline> list) {
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    List<Integer> noteTimelineTypes = getNoteTimelineTypes();
    for (KafkaTimeline timeline : list) {
      if (!noteTimelineTypes.contains(timeline.getTimelineType())) {
        continue;
      }
      try {
        LeadNoteBo leadNoteBo = LeadNoteBo.builder()
            .timelineId(timeline.getId())
            .timelineType(timeline.getTimelineType())
            .refId(timeline.getRefId())
            .leadId(timeline.getLeadId())
            .timeline(JacksonUtils.toJson(timeline))
            .build();

        leadNoteDao.insertLeadNote(leadNoteBo);
        log.info("Successfully recorded timeline to lead_note: timelineId={}, leadId={}",
            timeline.getId(), timeline.getLeadId());
      } catch (Exception e) {
        log.warn("Failed to record timeline to lead_note: timelineId={}, leadId={}, error={}",
            timeline.getId(), timeline.getLeadId(), e.getMessage(), e);
      }
    }
  }

  @Override
  public boolean supports(KafkaTimeline timeline) {
    if (timeline == null) {
      return false;
    }

    // Check if timeline content contains FUB source indicators
    if (containsFubSource(timeline.getTimelineContent()) ||
        containsFubSource(timeline.getContentMap())) {
      return false;
    }

    return isValidTimeline(timeline);
  }

  /**
   * Check if content contains FUB source indicators
   */
  private boolean containsFubSource(String content) {
    if (StringUtils.isEmpty(content)) {
      return false;
    }
    return content.contains(ORIGINAL_SOURCE_FUB) || content.contains(SOURCE_FUB_OAUTH);
  }

  @Override
  protected List<Integer> getTimelineTypes() {
    // FUB supports all default timeline types except some specific ones
    return super.getTimelineTypes().stream()
        .filter(type -> !List.of(7, 25, 28, 26, 6, 124, 62, 96).contains(type))
        .toList();
  }

  @Override
  protected List<Integer> getCommunicationTimelineTypes() {

    // FUB supports all default communication timeline types except some specific ones
    return super.getCommunicationTimelineTypes().stream()
        .filter(type -> !List.of(7, 25, 28, 26, 6, 124, 62, 96).contains(type))
        .toList();
  }

  @Override
  public boolean checkLeadDetailChange(KafkaTimeline kafkaTimeline, String contentMap) {
    if (super.checkLeadDetailChange(kafkaTimeline, contentMap)) {
      return true;
    }

    // Check for FUB-specific changes
    return hasFubSpecificChanges(contentMap);
  }

  /**
   * Check for FUB-specific lead detail changes
   */
  public boolean hasFubSpecificChanges(String contentMap) {
    if (StringUtils.isEmpty(contentMap)) {
      return false;
    }

    String[] fubChangeKeywords = {
        "Family Member", "Mailing Address", "Property Address",
        "CRITERIA", "Time Frame", "Tag"
    };

    for (String keyword : fubChangeKeywords) {
      if (contentMap.contains(keyword)) {
        return true;
      }
    }
    return false;
  }
}
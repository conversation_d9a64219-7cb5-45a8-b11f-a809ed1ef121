package com.homethy.leadsync.business;

import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadUserBo;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.zillow.dao.ZillowLeadDao;
import com.homethy.zillow.manager.ZillowLeadManager;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.zillow.service.LeadPushService;
import com.homethy.zillow.service.ZillowLeadService;
import com.homethy.zillow.service.ZillowOauth2TokenService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ZillowLeadSyncHandler extends AbstractLeadSyncHandler<ZillowLead> {

  @Resource
  LeadPushService leadPushService;
  @Resource
  ZillowLeadManager zillowLeadManager;
  @Resource
  ZillowLeadService zillowLeadService;
  @Resource
  LeadOfflineService leadOfflineService;
  @Resource
  ZillowOauth2TokenService zillowOauth2TokenService;
  @Resource
  ZillowLeadDao zillowLeadDao;

  @Override
  protected Map<Long, ZillowLead> filterSyncedLead(List<KafkaTimeline> list) {
    return leadPushService.filterSyncedLead(list.stream().map(KafkaTimeline::getLeadId).toList());
  }

  @Override
  protected void assignLead(List<KafkaTimeline> list, Map<Long, ZillowLead> leadMap) {
    leadPushService.assignLead(list);
  }

  @Override
  protected void communicationLogs(List<KafkaTimeline> list, Map<Long, ZillowLead> leadMap,
      CommunicationEnum type) {
    leadPushService.communicationLogs(list, type);
  }

  @Override
  public boolean supports(KafkaTimeline timeline) {
    if (timeline == null) {
      return false;
    }
    if (StringUtils.isNotEmpty(timeline.getTimelineContent()) &&
        timeline.getTimelineContent().contains("\"source\":\"ZILLOW_OAUTH\"")) {
      return false;
    }
    if (StringUtils.isNotEmpty(timeline.getContentMap()) &&
        timeline.getContentMap().contains("\"source\":\"ZILLOW_OAUTH\"")) {
      return false;
    }
    return getLeadPushService().isValidTimeline(timeline);
  }

  @Override
  protected List<Long> getPlatformSpecificLeadsForDisconnect(List<KafkaTimeline> timelineList) {
    return timelineList.stream()
        .map(KafkaTimeline::getLeadId)
        .toList();
  }

  @Override
  protected void doDisconnectLeads(List<Long> leadIds) {
    zillowLeadService.disconnectZillowLead(leadIds);
  }


  @Override
  protected ZillowLead getPlatformLead(Long leadId) {
    return zillowLeadManager.getZillowLeadByLeadId(leadId);
  }


  @Override
  protected List<Integer> getTimelineTypes() {
    // Zillow supports all default timeline types
    return super.getTimelineTypes();
  }

  @Override
  protected List<Integer> getCommunicationTimelineTypes() {
    // Zillow supports all default communication timeline types
    return super.getCommunicationTimelineTypes();
  }

  @Override
  protected void pushLeadToPlatform(LeadBo leadBo, ZillowLead platformLead) {
    LeadUserBo leadUserBo = getLeadUserKernelService().getLeadUserById(leadBo.getLeadUserId());
    String email = getLeadEmail(leadUserBo, platformLead);
    LeadPropertyVoBo mailingProperty = getLeadPropertyService().getMailingProperty(
        leadBo.getLeadUserId());
    leadPushService.pushLeadDetail(leadBo, leadUserBo, mailingProperty, email, platformLead);
  }

}
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="dataSource.chime-zillow"
          class="com.homethy.configcenter.client.HomethyBasicDataSource"
          init-method="afterPropertiesSet">
        <property name="business" value="chime_zillow"/>
    </bean>

    <bean id="sessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="configLocation" value="classpath:mybatis-conf.xml"/>
        <property name="dataSource" ref="dataSource.chime-zillow"/>
    </bean>

    <!-- mybaits.spring  Automatic mapping  -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage"
                  value="com.homethy.zillow.dao,com.homethy.fub.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sessionFactory"/>
        <property name="annotationClass"
                  value="com.homethy.zillow.annotation.DataSourceBase"/>
    </bean>

    <bean id="encryptSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="configLocation" value="classpath:mybatis-conf.xml"/>
        <property name="dataSource" ref="encryptPiiDataSource"/>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.homethy.zillow.dao,com.homethy.fub.dao"/>
        <property name="sqlSessionFactoryBeanName" value="encryptSqlSessionFactory"/>
        <property name="annotationClass"
                  value="com.homethy.zillow.annotation.EncryptDataSource"/>
    </bean>

    <bean id="tm"
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource.chime-zillow"/>
    </bean>

    <tx:annotation-driven transaction-manager="tm"/>
</beans>

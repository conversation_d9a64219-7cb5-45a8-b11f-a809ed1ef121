<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!--  Global mapper enables caching  -->
        <setting name="cacheEnabled" value="true"/>

        <!--  When querying, turn off immediate loading of associated objects to improve performance  -->
        <setting name="lazyLoadingEnabled" value="true"/>

        <!--  Set the loading form of the associated object, here is the on-demand loading field ( The loading field is SQL Specify ) , Will not load all the fields of the associated table to improve performance  -->
        <setting name="aggressiveLazyLoading" value="false"/>

        <!--  For the unknown SQL Query, allowing to return different result sets in order to achieve universal effects  -->
        <setting name="multipleResultSetsEnabled" value="true"/>

        <!--  Allow the use of column labels instead of column names  -->
        <setting name="useColumnLabel" value="true"/>

        <!--  Allow the use of custom primary key values ( For example, generated by the program UUID 32 Bit code as key value ) , Of the data sheet PK The generation strategy will be overwritten  -->
        <!--
                <setting name="useGeneratedKeys" value="true" />
        -->

        <!--  Give nested resultMap By field - Attribute mapping support  -->
        <setting name="autoMappingBehavior" value="FULL"/>

        <!--  No batch update operation  -->
        <setting name="defaultExecutorType" value="REUSE"/>

        <!--  Database exceeds 25000 Timeout if there is no response in seconds  -->
        <setting name="defaultStatementTimeout" value="25000"/>

        <!--  Whether to enable automatic camel case naming rules ( camel case ) Mapping, that is, column names from the classic database  A_COLUMN  To classic  Java  Attribute name  aColumn
             The similar mapping.  -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

</configuration>  

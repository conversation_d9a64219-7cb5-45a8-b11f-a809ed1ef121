package com.homethy.leadsync.business;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.homethy.microservice.client.lead.LeadUserKernelService;
import com.homethy.microservice.client.lead.apiV3.LeadPropertyService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadUserBo;
import com.homethy.microservice.client.offline.LeadOfflineService;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.ZillowLead;
import com.homethy.zillow.model.po.KafkaTimeline;
import com.homethy.zillow.model.po.LeadEmailChange;
import com.homethy.zillow.service.LeadPushService;
import com.homethy.zillow.util.ContextUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractLeadSyncHandlerTest {

  @Mock
  private LeadOfflineService leadOfflineService;

  @Mock
  private LeadUserKernelService leadUserKernelService;

  @Mock
  private LeadPropertyService leadPropertyService;

  @Mock
  private LeadPushService leadPushService;

  @Mock
  private RedisService redisService;

  @InjectMocks
  private TestAbstractLeadSyncHandler testHandler;

  private KafkaTimeline testTimeline;
  private LeadBo testLeadBo;
  private LeadUserBo testLeadUserBo;
  private LeadPropertyVoBo testMailingProperty;
  private ZillowLead testZillowLead;

  @BeforeEach
  void setUp() {
    testTimeline = new KafkaTimeline();
    testTimeline.setId(1L);
    testTimeline.setLeadId(100L);
    testTimeline.setTimelineType(21);
    testTimeline.setTimelineContent("Lead Type was changed");
    testTimeline.setContentMap("{\"change\":\"test\"}");

    testLeadBo = new LeadBo();
    testLeadBo.setId(100L);
    testLeadBo.setLeadUserId(200L);

    testLeadUserBo = new LeadUserBo();
    testLeadUserBo.setId(200L);
    testLeadUserBo.setAccount("<EMAIL>");

    testMailingProperty = new LeadPropertyVoBo();
    testMailingProperty.setId(300L);

    testZillowLead = new ZillowLead();
    testZillowLead.setLeadId(100L);
    testZillowLead.setLeadLastEmail("<EMAIL>");
  }

  @Test
  void testGetTimelineTypes() {
    List<Integer> timelineTypes = testHandler.getTimelineTypes();
    assertNotNull(timelineTypes);
    assertTrue(timelineTypes.contains(21));
    assertTrue(timelineTypes.contains(80));
    assertTrue(timelineTypes.contains(31));
  }

  @Test
  void testGetCommunicationTimelineTypes() {
    List<Integer> communicationTypes = testHandler.getCommunicationTimelineTypes();
    assertNotNull(communicationTypes);
    assertTrue(communicationTypes.contains(93));
    assertTrue(communicationTypes.contains(26));
    assertTrue(communicationTypes.contains(62));
  }

  @Test
  void testBatchHandleTimeLine_EmptyList() {
    testHandler.batchHandleTimeLine(Collections.emptyList());
    // Should not throw any exception
  }

  @Test
  void testBatchHandleTimeLine_NullList() {
    testHandler.batchHandleTimeLine(null);
    // Should not throw any exception - the method handles null gracefully
  }

  @Test
  void testBatchHandleTimeLine_ValidTimeline() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadOfflineService.class))
          .thenReturn(leadOfflineService);
      when(leadOfflineService.getLeadsInIdList(anyList())).thenReturn(Arrays.asList(testLeadBo));

      testHandler.batchHandleTimeLine(timelineList);

      // The method should be called when processing lead detail changes (timeline type 21)
      verify(leadOfflineService, times(1)).getLeadsInIdList(anyList());
    }
  }

  @Test
  void testBatchHandleCommunicationTimeLine_EmptyList() {
    testHandler.batchHandleCommunicationTimeLine(Collections.emptyList());
    // Should not throw any exception
  }

  @Test
  void testBatchHandleCommunicationTimeLine_NullList() {
    testHandler.batchHandleCommunicationTimeLine(null);
    // Should not throw any exception - the method handles null gracefully
  }

  @Test
  void testFilterTimelineType_ValidType() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<KafkaTimeline> filtered = testHandler.filterTimelineType(timelineList);

    assertNotNull(filtered);
    assertEquals(1, filtered.size());
  }

  @Test
  void testFilterTimelineType_InvalidType() {
    testTimeline.setTimelineType(999); // Invalid type
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<KafkaTimeline> filtered = testHandler.filterTimelineType(timelineList);

    assertNotNull(filtered);
    assertEquals(0, filtered.size());
  }

  @Test
  void testFilterCommunicationTimelineType_ValidType() {
    testTimeline.setTimelineType(93); // Valid communication type
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<KafkaTimeline> filtered = testHandler.filterCommunicationTimelineType(timelineList);

    assertNotNull(filtered);
    assertEquals(1, filtered.size());
  }

  @Test
  void testFilterCommunicationTimelineType_InvalidType() {
    testTimeline.setTimelineType(999); // Invalid type
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<KafkaTimeline> filtered = testHandler.filterCommunicationTimelineType(timelineList);

    assertNotNull(filtered);
    assertEquals(0, filtered.size());
  }

  @Test
  void testFilterSyncedLead_ValidLeads() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    Map<Long, ZillowLead> result = testHandler.filterSyncedLead(timelineList);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsKey(100L));
  }

  @Test
  void testFilterSyncedLead_NoValidLeads() {
    testTimeline.setLeadId(999L); // Lead that doesn't exist
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    Map<Long, ZillowLead> result = testHandler.filterSyncedLead(timelineList);

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void testFilterValidLeads_ValidLeads() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    List<KafkaTimeline> result = testHandler.filterValidLeads(timelineList, leadMap);

    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void testFilterValidLeads_NoValidLeads() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(999L, testZillowLead); // Different lead ID

    List<KafkaTimeline> result = testHandler.filterValidLeads(timelineList, leadMap);

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void testHandleByType_LeadDetailChange() {
    testTimeline.setTimelineType(21);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadOfflineService.class))
          .thenReturn(leadOfflineService);
      when(leadOfflineService.getLeadsInIdList(anyList())).thenReturn(Arrays.asList(testLeadBo));

      testHandler.handleByType(21, timelineList, leadMap);

      verify(leadOfflineService, times(1)).getLeadsInIdList(anyList());
    }
  }

  @Test
  void testHandleByType_AssignLead() {
    testTimeline.setTimelineType(31);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(31, timelineList, leadMap);

    // Should call assignLead
    assertTrue(testHandler.isAssignLeadCalled());
  }

  @Test
  void testHandleByType_EmailCommunication() {
    testTimeline.setTimelineType(26);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(26, timelineList, leadMap);

    // Should call communicationLogs with Email type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Email, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleByType_CallCommunication() {
    testTimeline.setTimelineType(8);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(8, timelineList, leadMap);

    // Should call communicationLogs with Call type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Call, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleByType_TextCommunication() {
    testTimeline.setTimelineType(28);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(28, timelineList, leadMap);

    // Should call communicationLogs with Txt type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Txt, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleByType_NoteCommunication() {
    testTimeline.setTimelineType(16);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(16, timelineList, leadMap);

    // Should call communicationLogs with Note type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Note, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleByType_UnsupportedType() {
    testTimeline.setTimelineType(999);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleByType(999, timelineList, leadMap);

    // Should not call any specific handler
    assertFalse(testHandler.isAssignLeadCalled());
    assertFalse(testHandler.isCommunicationLogsCalled());
  }

  @Test
  void testHandleCommunicationByType_LeadDetailChange() {
    testTimeline.setTimelineType(80);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadOfflineService.class))
          .thenReturn(leadOfflineService);
      when(leadOfflineService.getLeadsInIdList(anyList())).thenReturn(Arrays.asList(testLeadBo));

      testHandler.handleCommunicationByType(80, timelineList, leadMap);

      verify(leadOfflineService, times(1)).getLeadsInIdList(anyList());
    }
  }

  @Test
  void testHandleCommunicationByType_EmailCommunication() {
    testTimeline.setTimelineType(26);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleCommunicationByType(26, timelineList, leadMap);

    // Should call communicationLogs with Email type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Email, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleCommunicationByType_CallCommunication() {
    testTimeline.setTimelineType(8);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleCommunicationByType(8, timelineList, leadMap);

    // Should call communicationLogs with Call type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Call, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleCommunicationByType_NoteCommunication() {
    testTimeline.setTimelineType(16);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleCommunicationByType(16, timelineList, leadMap);

    // Should call communicationLogs with Note type
    assertTrue(testHandler.isCommunicationLogsCalled());
    assertEquals(CommunicationEnum.Note, testHandler.getLastCommunicationType());
  }

  @Test
  void testHandleCommunicationByType_UnsupportedType() {
    testTimeline.setTimelineType(999);
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);
    Map<Long, ZillowLead> leadMap = new HashMap<>();
    leadMap.put(100L, testZillowLead);

    testHandler.handleCommunicationByType(999, timelineList, leadMap);

    // Should not call any specific handler
    assertFalse(testHandler.isCommunicationLogsCalled());
  }

  @Test
  void testEditLeadDetail_EmptyList() {
    testHandler.editLeadDetail(Collections.emptyList(), Maps.newHashMap());
    // Should not throw any exception
  }

  @Test
  void testEditLeadDetail_NullList() {
    testHandler.editLeadDetail(null, Maps.newHashMap());
    // Should not throw any exception
  }

  @Test
  void testEditLeadDetail_ValidLeads() {
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadOfflineService.class))
          .thenReturn(leadOfflineService);
      when(leadOfflineService.getLeadsInIdList(anyList())).thenReturn(Arrays.asList(testLeadBo));

      testHandler.editLeadDetail(timelineList, Maps.newHashMap());

      verify(leadOfflineService, times(1)).getLeadsInIdList(anyList());
    }
  }

  @Test
  void testEditLeadDetail_PrivacyChange() {
    testTimeline.setTimelineContent(
        "Privacy was changed from \"Team Lead\" to \\\"Private Lead\\\"");
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadOfflineService.class))
          .thenReturn(leadOfflineService);
      when(leadOfflineService.getLeadsInIdList(anyList())).thenReturn(Arrays.asList(testLeadBo));

      testHandler.editLeadDetail(timelineList, Maps.newHashMap());

      // Should call doDisconnectLeads for privacy change
      assertTrue(testHandler.isDisconnectLeadsCalled());
    }
  }

  @Test
  void testEditLeadDetail_NoValidLeads() {
    testTimeline.setLeadId(999L); // Lead that doesn't exist
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    testHandler.editLeadDetail(timelineList, Maps.newHashMap());

    // Should not call getLeadsInIdList since no valid leads
    // Note: This test doesn't need mocking since getPlatformLead returns null for leadId 999L
  }

  @Test
  void testGetPlatformSpecificLeadsForDisconnect_PrivacyChange() {
    testTimeline.setTimelineContent(
        "Privacy was changed from \"Team Lead\" to \\\"Private Lead\\\"");
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<Long> result = testHandler.getPlatformSpecificLeadsForDisconnect(timelineList);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(100L, result.get(0));
  }

  @Test
  void testGetPlatformSpecificLeadsForDisconnect_NoPrivacyChange() {
    testTimeline.setTimelineContent("Other change");
    List<KafkaTimeline> timelineList = Arrays.asList(testTimeline);

    List<Long> result = testHandler.getPlatformSpecificLeadsForDisconnect(timelineList);

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void testGetPlatformSpecificLeadsForDisconnect_EmptyList() {
    List<Long> result = testHandler.getPlatformSpecificLeadsForDisconnect(Collections.emptyList());

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void testGetPlatformSpecificLeadsForDisconnect_NullList() {
    List<Long> result = testHandler.getPlatformSpecificLeadsForDisconnect(null);

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void testDoPushLeadDetail_ValidData() {
    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadUserKernelService.class))
          .thenReturn(leadUserKernelService);
      contextUtilMock.when(() -> ContextUtil.getBean(LeadPropertyService.class))
          .thenReturn(leadPropertyService);
      contextUtilMock.when(() -> ContextUtil.getBean(RedisService.class)).thenReturn(redisService);

      when(leadUserKernelService.getLeadUserById(anyLong())).thenReturn(testLeadUserBo);
      when(leadPropertyService.getMailingProperty(anyLong())).thenReturn(testMailingProperty);
      when(redisService.exists(anyString(), anyString())).thenReturn(false);
      when(redisService.get(anyString(), anyString())).thenReturn(null);

      testHandler.doPushLeadDetail(testLeadBo, testZillowLead);

      verify(leadUserKernelService, times(1)).getLeadUserById(200L);
      verify(leadPropertyService, times(1)).getMailingProperty(200L);
      assertTrue(testHandler.isPushLeadToPlatformCalled());
    }
  }

  @Test
  void testDoPushLeadDetail_NullLeadUser() {
    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(LeadUserKernelService.class))
          .thenReturn(leadUserKernelService);
      contextUtilMock.when(() -> ContextUtil.getBean(LeadPropertyService.class))
          .thenReturn(leadPropertyService);

      when(leadUserKernelService.getLeadUserById(anyLong())).thenReturn(null);
      when(leadPropertyService.getMailingProperty(anyLong())).thenReturn(testMailingProperty);

      testHandler.doPushLeadDetail(testLeadBo, testZillowLead);

      verify(leadUserKernelService, times(1)).getLeadUserById(200L);
      verify(leadPropertyService, times(1)).getMailingProperty(200L);
      assertTrue(testHandler.isPushLeadToPlatformCalled());
    }
  }

  @Test
  void testGetLeadEmail_ValidEmailChange() {
    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(RedisService.class)).thenReturn(redisService);

      String emailChangeJson = JacksonUtils.toJson(Arrays.asList(
          new LeadEmailChange("<EMAIL>", "<EMAIL>")
      ));

      when(redisService.exists(anyString(), anyString())).thenReturn(false);
      when(redisService.get(anyString(), anyString())).thenReturn(emailChangeJson);

      String result = testHandler.getLeadEmail(testLeadUserBo, testZillowLead);

      assertNotNull(result);
      assertEquals("<EMAIL>", result);
      verify(redisService, times(1)).del(anyString(), anyString());
    }
  }

  @Test
  void testGetLeadEmail_NoEmailChange() {
    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(RedisService.class)).thenReturn(redisService);

      when(redisService.exists(anyString(), anyString())).thenReturn(false);
      when(redisService.get(anyString(), anyString())).thenReturn(null);

      String result = testHandler.getLeadEmail(testLeadUserBo, testZillowLead);

      assertNull(result);
    }
  }

  @Test
  void testGetLeadEmail_InvalidJson() {
    try (MockedStatic<ContextUtil> contextUtilMock = mockStatic(ContextUtil.class)) {
      contextUtilMock.when(() -> ContextUtil.getBean(RedisService.class)).thenReturn(redisService);

      when(redisService.exists(anyString(), anyString())).thenReturn(false);
      when(redisService.get(anyString(), anyString())).thenReturn("invalid json");

      String result = testHandler.getLeadEmail(testLeadUserBo, testZillowLead);

      assertNull(result);
    }
  }

  @Test
  void testCheckLeadPrivacyChange_ValidPrivacyChange() {
    testTimeline.setTimelineContent(
        "Privacy was changed from \"Team Lead\" to \\\"Private Lead\\\"");

    boolean result = testHandler.checkLeadPrivacyChange(testTimeline);

    assertTrue(result);
  }

  @Test
  void testCheckLeadPrivacyChange_NoPrivacyChange() {
    testTimeline.setTimelineContent("Other change");

    boolean result = testHandler.checkLeadPrivacyChange(testTimeline);

    assertFalse(result);
  }

  @Test
  void testCheckLeadPrivacyChange_NullTimeline() {
    boolean result = testHandler.checkLeadPrivacyChange(null);

    assertFalse(result);
  }

  @Test
  void testCheckLeadPrivacyChange_EmptyContent() {
    testTimeline.setTimelineContent("");

    boolean result = testHandler.checkLeadPrivacyChange(testTimeline);

    assertFalse(result);
  }

  @Test
  void testIsValidTimeline_NullTimeline() {
    boolean result = testHandler.isValidTimeline(null);

    assertFalse(result);
  }

  @Test
  void testIsValidTimeline_AlwaysValidType() {
    testTimeline.setTimelineType(26); // Always valid type

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertTrue(result);
  }

  @Test
  void testIsValidTimeline_LeadDetailChange() {
    testTimeline.setTimelineType(21);
    testTimeline.setTimelineContent("Lead Type was changed");

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertTrue(result);
  }

  @Test
  void testIsValidTimeline_ZapierUpdate() {
    testTimeline.setTimelineType(93);

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertTrue(result);
  }

  @Test
  void testIsValidTimeline_SystemLog() {
    testTimeline.setTimelineType(80);
    testTimeline.setContentMap("The system auto-updated lead info");

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertTrue(result);
  }

  @Test
  void testIsValidTimeline_AgentAssign() {
    testTimeline.setTimelineType(31);
    testTimeline.setTimelineContent("{\"role\":\"Agent\"}");
    testTimeline.setToType(1);

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertTrue(result);
  }

  @Test
  void testIsValidTimeline_InvalidType() {
    testTimeline.setTimelineType(999);

    boolean result = testHandler.isValidTimeline(testTimeline);

    assertFalse(result);
  }

  @Test
  void testCheckLeadDetailChange_ValidChange() {
    testTimeline.setTimelineContent("Lead Type was changed");

    boolean result = testHandler.checkLeadDetailChange(testTimeline, "Lead Type was changed");

    assertTrue(result);
  }

  @Test
  void testCheckLeadDetailChange_NoChange() {
    testTimeline.setTimelineContent("Other content");

    boolean result = testHandler.checkLeadDetailChange(testTimeline, "Other content");

    assertFalse(result);
  }

  @Test
  void testCheckLeadDetailChange_EmptyContent() {
    boolean result = testHandler.checkLeadDetailChange(testTimeline, "");

    assertFalse(result);
  }

  @Test
  void testCheckLeadAttributeChangeBySystemLog_ValidChange() {
    testTimeline.setContentMap("The system auto-updated lead info");

    boolean result = testHandler.checkLeadAttributeChangeBySystemLog(testTimeline);

    assertTrue(result);
  }

  @Test
  void testCheckLeadAttributeChangeBySystemLog_NoChange() {
    testTimeline.setContentMap("Other content");

    boolean result = testHandler.checkLeadAttributeChangeBySystemLog(testTimeline);

    assertFalse(result);
  }

  @Test
  void testCheckLeadAttributeChangeBySystemLog_NullTimeline() {
    boolean result = testHandler.checkLeadAttributeChangeBySystemLog(null);

    assertFalse(result);
  }

  @Test
  void testAgentAssignChange_ValidAssign() {
    testTimeline.setTimelineContent("{\"role\":\"Agent\"}");
    testTimeline.setToType(1);

    boolean result = testHandler.agentAssignChange(testTimeline);

    assertTrue(result);
  }

  @Test
  void testAgentAssignChange_NoAgentRole() {
    testTimeline.setTimelineContent("{\"role\":\"Other\"}");
    testTimeline.setToType(1);

    boolean result = testHandler.agentAssignChange(testTimeline);

    assertFalse(result);
  }

  @Test
  void testAgentAssignChange_AssignToPond() {
    testTimeline.setTimelineContent("{\"role\":\"Agent\"}");
    testTimeline.setToType(6);

    boolean result = testHandler.agentAssignChange(testTimeline);

    assertFalse(result);
  }

  @Test
  void testAgentAssignChange_NullTimeline() {
    boolean result = testHandler.agentAssignChange(null);

    assertFalse(result);
  }

  // Test implementation class for AbstractLeadSyncHandler
  private static class TestAbstractLeadSyncHandler extends AbstractLeadSyncHandler<ZillowLead> {

    private boolean assignLeadCalled = false;
    private boolean communicationLogsCalled = false;
    private boolean disconnectLeadsCalled = false;
    private boolean pushLeadToPlatformCalled = false;
    private CommunicationEnum lastCommunicationType = null;

    @Override
    protected void doDisconnectLeads(List<Long> leadIds) {
      disconnectLeadsCalled = true;
    }

    @Override
    protected ZillowLead getPlatformLead(Long leadId) {
      if (leadId == 100L) {
        ZillowLead lead = new ZillowLead();
        lead.setLeadId(leadId);
        return lead;
      }
      return null;
    }

    @Override
    protected void pushLeadToPlatform(LeadBo leadBo, ZillowLead platformLead) {

    }


    @Override
    protected void assignLead(List<KafkaTimeline> list, Map<Long, ZillowLead> leadMap) {
      assignLeadCalled = true;
    }

    @Override
    protected void communicationLogs(List<KafkaTimeline> list, Map<Long, ZillowLead> leadMap,
        CommunicationEnum type) {
      communicationLogsCalled = true;
      lastCommunicationType = type;
    }

    @Override
    public boolean supports(KafkaTimeline timeline) {
      return timeline != null && timeline.getTimelineType() == 21;
    }

    // Getters for testing
    public boolean isAssignLeadCalled() {
      return assignLeadCalled;
    }

    public boolean isCommunicationLogsCalled() {
      return communicationLogsCalled;
    }

    public boolean isDisconnectLeadsCalled() {
      return disconnectLeadsCalled;
    }

    public boolean isPushLeadToPlatformCalled() {
      return pushLeadToPlatformCalled;
    }

    public CommunicationEnum getLastCommunicationType() {
      return lastCommunicationType;
    }

    // Reset methods for testing
    public void reset() {
      assignLeadCalled = false;
      communicationLogsCalled = false;
      disconnectLeadsCalled = false;
      pushLeadToPlatformCalled = false;
      lastCommunicationType = null;
    }
  }
} 
package com.homethy.leadsync.business;

import com.homethy.fub.dao.FubTimelineRefDao;
import com.homethy.fub.dao.LeadNoteDao;
import com.homethy.fub.service.FubLeadSyncService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadUserBo;
import com.homethy.zillow.model.CommunicationEnum;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubTimelineRefBo;
import com.homethy.zillow.model.constant.fub.LeadNoteBo;
import com.homethy.zillow.model.po.KafkaTimeline;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link FubLeadSyncHandler}.
 * 
 * <AUTHOR> Automation
 */
@ExtendWith(MockitoExtension.class)
public class FubLeadSyncHandlerTest {

    @Mock
    private FubLeadSyncService fubLeadSyncService;
    @Mock
    private FubTimelineRefDao fubTimelineRefDao;
    @Mock
    private LeadNoteDao leadNoteDao;

    @InjectMocks
    private FubLeadSyncHandler fubLeadSyncHandler;

    private FubLead mockFubLead;
    private KafkaTimeline mockTimeline;
    private LeadBo mockLeadBo;
    private LeadUserBo mockLeadUserBo;
    private LeadPropertyVoBo mockMailingProperty;

    @BeforeEach
    void setUp() {
        mockFubLead = new FubLead();
        mockTimeline = new KafkaTimeline();
        mockTimeline.setLeadId(12345L);
        mockTimeline.setTimelineType(21);
        mockTimeline.setTimelineContent("Test content");
        
        mockLeadBo = new LeadBo();
        mockLeadBo.setId(12345L);
        mockLeadBo.setLeadUserId(67890L);
        
        mockLeadUserBo = new LeadUserBo();
        mockLeadUserBo.setId(67890L);
        
        mockMailingProperty = new LeadPropertyVoBo();
    }

    // ==================== supports Tests ====================

    @Test
    void supports_WhenNullTimeline_ShouldReturnFalse() {
        // Given
        KafkaTimeline timeline = null;
        
        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertFalse(result);
    }

    @Test
    void supports_WhenTimelineContainsFubSource_ShouldReturnFalse() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setTimelineContent("{\"originalSource\":\"FUB\"}");
        
        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertFalse(result);
    }

    @Test
    void supports_WhenTimelineContainsFubOAuthSource_ShouldReturnFalse() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setContentMap("{\"source\":\"FUB_OAUTH\"}");
        
        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertFalse(result);
    }

    @Test
    void supports_WhenTimelineDoesNotContainFubSource_ShouldReturnTrue() {
        // Given - type 26 (always valid) doesn't contain FUB source
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setTimelineContent("{\"source\":\"OTHER_SOURCE\"}");
        timeline.setTimelineType(26);

        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertTrue(result);
    }

    @Test
    void supports_WhenTimelineType21WithoutFubSourceAndValidChange_ShouldReturnTrue() {
        // Given - type 21 with valid change content and no FUB source
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setTimelineContent("{\"leadType\": \"A to B\", \"Lead Type was changed\": \"Yes\"}");
        timeline.setTimelineType(21);

        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertTrue(result);
    }

    @Test
    void supports_WhenTimelineType21WithFubSpecificChanges_ShouldReturnTrue() {
        // Given - type 21 with FUB-specific changes and no FUB source
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setTimelineContent("{\"Family Member\": \"updated\"}");
        timeline.setTimelineType(21);

        // When
        boolean result = fubLeadSyncHandler.supports(timeline);
        
        // Then
        assertTrue(result);
    }

    // ==================== containsFubSource Tests ====================

    @Test
    void containsFubSource_WhenContentContainsOriginalSourceFub_ShouldReturnTrue() {
        // When
        boolean result = invokeContainsFubSource("{\"originalSource\":\"FUB\"}");
        
        // Then
        assertTrue(result);
    }

    @Test
    void containsFubSource_WhenContentContainsSourceFubOauth_ShouldReturnTrue() {
        // When
        boolean result = invokeContainsFubSource("{\"source\":\"FUB_OAUTH\"}");
        
        // Then
        assertTrue(result);
    }

    @Test
    void containsFubSource_WhenContentIsNull_ShouldReturnFalse() {
        // When
        boolean result = invokeContainsFubSource(null);
        
        // Then
        assertFalse(result);
    }

    @Test
    void containsFubSource_WhenContentIsEmpty_ShouldReturnFalse() {
        // When
        boolean result = invokeContainsFubSource("");
        
        // Then
        assertFalse(result);
    }

    @Test
    void containsFubSource_WhenContentDoesNotContainFub_ShouldReturnFalse() {
        // When
        boolean result = invokeContainsFubSource("{\"source\":\"ZILLOW\"}");
        
        // Then
        assertFalse(result);
    }

    // ==================== Timeline Filtering Tests ====================

    @Test
    void getTimelineTypes_ShouldExcludeTypes7_25_28() {
        // When
        List<Integer> timelineTypes = fubLeadSyncHandler.getTimelineTypes();
        
        // Then
        assertNotNull(timelineTypes);
        assertFalse(timelineTypes.contains(7));
        assertFalse(timelineTypes.contains(25));
        assertFalse(timelineTypes.contains(28));
        
        // Verify it has some expected values
        assertTrue(timelineTypes.contains(21)); // Included in parent
        assertTrue(timelineTypes.contains(31)); // Included in parent
    }

    @Test
    void getCommunicationTimelineTypes_ShouldExcludeTypes7_25_28() {
        // When
        List<Integer> communicationTypes = fubLeadSyncHandler.getCommunicationTimelineTypes();
        
        // Then
        assertNotNull(communicationTypes);
        assertFalse(communicationTypes.contains(7));
        assertFalse(communicationTypes.contains(25));
        assertFalse(communicationTypes.contains(28));
        
        // Verify it has some expected values
        assertTrue(communicationTypes.contains(26)); // Included in parent
        assertTrue(communicationTypes.contains(96)); // Included in parent
    }

    // ==================== Business Logic Tests ====================

    @Test
    void getPlatformLead_ShouldDelegateToFubLeadSyncService() {
        // Given
        Long leadId = 12345L;
        doReturn(mockFubLead).when(fubLeadSyncService).getFubLead(leadId);
        
        // When
        FubLead result = fubLeadSyncHandler.getPlatformLead(leadId);
        
        // Then
        assertEquals(mockFubLead, result);
        verify(fubLeadSyncService, times(1)).getFubLead(leadId);
    }

    @Test
    void pushLeadToPlatform_ShouldDelegateToFubLeadSyncService() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setLeadId(12345L);
        
        // When
        fubLeadSyncHandler.pushLeadToPlatform(mockLeadBo, mockFubLead);
        
        // Then
        verify(fubLeadSyncService, times(1)).pushLeadToPlatform(any(KafkaTimeline.class), eq(mockFubLead));
    }

    @Test
    void assignLead_ShouldDelegateToFubLeadSyncService() {
        // Given
        List<KafkaTimeline> timelines = Arrays.asList(mockTimeline, mockTimeline);
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        
        // When
        fubLeadSyncHandler.assignLead(timelines, leadMap);
        
        // Then
        verify(fubLeadSyncService, times(1)).assignLead(timelines, leadMap);
    }

    @Test
    void communicationLogs_ShouldDelegateToFubLeadSyncService_ForValidLeads() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setLeadId(12345L);
        
        List<KafkaTimeline> timelines = Collections.singletonList(timeline);
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        FubTimelineRefBo fubTimelineRefBo = new FubTimelineRefBo();
        List<FubTimelineRefBo> timelineRefBos =  Collections.singletonList(fubTimelineRefBo);

        when(fubTimelineRefDao.getTimelineRefs(anyList())).thenReturn(timelineRefBos);

        // When
        fubLeadSyncHandler.communicationLogs(timelines, leadMap, CommunicationEnum.Email);
        
        // Then
        verify(fubLeadSyncService, times(1)).communicationLogs(timeline, mockFubLead, CommunicationEnum.Email);
    }



    @Test
    void communicationLogs() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setLeadId(12345L);
        timeline.setId(1);

        List<KafkaTimeline> timelines = Collections.singletonList(timeline);
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        FubTimelineRefBo fubTimelineRefBo = new FubTimelineRefBo();
        fubTimelineRefBo.setTimelineId(1);
        List<FubTimelineRefBo> timelineRefBos =  Collections.singletonList(fubTimelineRefBo);

        when(fubTimelineRefDao.getTimelineRefs(anyList())).thenReturn(timelineRefBos);

        // When
        fubLeadSyncHandler.communicationLogs(timelines, leadMap, CommunicationEnum.Email);

        // Then
        verify(fubLeadSyncService, never()).communicationLogs(timeline, mockFubLead,
            CommunicationEnum.Email);
    }

    @Test
    void communicationLogs_ShouldSkipNonExistingLeads() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setLeadId(99999L); // Non-existent lead
        
        List<KafkaTimeline> timelines = Collections.singletonList(timeline);
        Map<Long, FubLead> leadMap = new HashMap<>();
        // Don't add the 99999L lead to leadMap
        
        // When
        fubLeadSyncHandler.communicationLogs(timelines, leadMap, CommunicationEnum.Email);
        
        // Then
        verify(fubLeadSyncService, never()).communicationLogs(any(), any(), any());
    }

    // ==================== FUB Specific Change Tests ====================

    @Test
    void checkLeadDetailChange_WithFubSpecificChanges_ShouldReturnTrue() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        String contentMap = "{\"Family Member\":\"updated\"}";
        
        // When
        boolean result = fubLeadSyncHandler.checkLeadDetailChange(timeline, contentMap);
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsFamilyMember_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("{\"Family Member\":\"John\"}");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsMailingAddress_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("Mailing Address updated");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsPropertyAddress_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("Property Address changed");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsCRITERIA_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("CRITERIA updated");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsTimeFrame_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("Time Frame updated");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentContainsTag_ShouldReturnTrue() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("Tag added");
        
        // Then
        assertTrue(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentIsNull_ShouldReturnFalse() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges(null);
        
        // Then
        assertFalse(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentIsEmpty_ShouldReturnFalse() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("");
        
        // Then
        assertFalse(result);
    }

    @Test
    void hasFubSpecificChanges_WhenContentHasNoFubSpecificChanges_ShouldReturnFalse() {
        // When
        boolean result = fubLeadSyncHandler.hasFubSpecificChanges("Generic content without specific keywords");
        
        // Then
        assertFalse(result);
    }

    // ==================== filterValidLeads Tests ====================

    @Test
    void filterValidLeads_WhenLeadMapIsEmpty_ShouldRecordAllTimelinesAndReturnEmptyList() {
        // Given
        KafkaTimeline timeline1 = new KafkaTimeline();
        timeline1.setId(1L);
        timeline1.setLeadId(12345L);
        timeline1.setTimelineType(8);
        
        KafkaTimeline timeline2 = new KafkaTimeline();
        timeline2.setId(2L);
        timeline2.setLeadId(67890L);
        timeline2.setTimelineType(16);
        
        List<KafkaTimeline> timelines = Arrays.asList(timeline1, timeline2);
        Map<Long, FubLead> emptyLeadMap = new HashMap<>();
        
        // When
        List<KafkaTimeline> result = fubLeadSyncHandler.filterValidLeads(timelines, emptyLeadMap);
        
        // Then
        assertTrue(result.isEmpty());
        verify(leadNoteDao, times(2)).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void filterValidLeads_WhenAllTimelinesAreValid_ShouldReturnAllTimelines() {
        // Given
        KafkaTimeline timeline1 = new KafkaTimeline();
        timeline1.setId(1L);
        timeline1.setLeadId(12345L);
        timeline1.setTimelineType(26);
        
        KafkaTimeline timeline2 = new KafkaTimeline();
        timeline2.setId(2L);
        timeline2.setLeadId(67890L);
        timeline2.setTimelineType(16);
        
        List<KafkaTimeline> timelines = Arrays.asList(timeline1, timeline2);
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        leadMap.put(67890L, mockFubLead);
        
        // When
        List<KafkaTimeline> result = fubLeadSyncHandler.filterValidLeads(timelines, leadMap);
        
        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(timeline1));
        assertTrue(result.contains(timeline2));
        verify(leadNoteDao, never()).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void filterValidLeads_WhenSomeTimelinesAreInvalid_ShouldRecordInvalidAndReturnValid() {
        // Given
        KafkaTimeline validTimeline = new KafkaTimeline();
        validTimeline.setId(1L);
        validTimeline.setLeadId(12345L);
        validTimeline.setTimelineType(8);
        
        KafkaTimeline invalidTimeline = new KafkaTimeline();
        invalidTimeline.setId(2L);
        invalidTimeline.setLeadId(99999L);
        invalidTimeline.setTimelineType(16);
        
        List<KafkaTimeline> timelines = Arrays.asList(validTimeline, invalidTimeline);
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        // Don't add 99999L to leadMap
        
        // When
        List<KafkaTimeline> result = fubLeadSyncHandler.filterValidLeads(timelines, leadMap);
        
        // Then
        assertEquals(1, result.size());
        assertTrue(result.contains(validTimeline));
        assertFalse(result.contains(invalidTimeline));
        verify(leadNoteDao, times(1)).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void filterValidLeads_WhenNoTimelinesAreValid_ShouldRecordAllAndReturnEmpty() {
        // Given
        KafkaTimeline timeline1 = new KafkaTimeline();
        timeline1.setId(1L);
        timeline1.setLeadId(99999L);
        timeline1.setTimelineType(8);
        
        KafkaTimeline timeline2 = new KafkaTimeline();
        timeline2.setId(2L);
        timeline2.setLeadId(88888L);
        timeline2.setTimelineType(16);
        
        List<KafkaTimeline> timelines = Arrays.asList(timeline1, timeline2);
        Map<Long, FubLead> leadMap = new HashMap<>();
        // Don't add any leads to leadMap
        
        // When
        List<KafkaTimeline> result = fubLeadSyncHandler.filterValidLeads(timelines, leadMap);
        
        // Then
        assertTrue(result.isEmpty());
        verify(leadNoteDao, times(2)).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void filterValidLeads_WhenTimelineListIsEmpty_ShouldReturnEmptyList() {
        // Given
        List<KafkaTimeline> emptyTimelines = Collections.emptyList();
        Map<Long, FubLead> leadMap = new HashMap<>();
        leadMap.put(12345L, mockFubLead);
        
        // When
        List<KafkaTimeline> result = fubLeadSyncHandler.filterValidLeads(emptyTimelines, leadMap);
        
        // Then
        assertTrue(result.isEmpty());
        verify(leadNoteDao, never()).insertLeadNote(any(LeadNoteBo.class));
    }

    // ==================== recordTimeline Tests ====================

    @Test
    void recordTimeline_WhenListIsEmpty_ShouldDoNothing() {
        // Given
        List<KafkaTimeline> emptyList = Collections.emptyList();
        
        // When
        fubLeadSyncHandler.recordTimeline(emptyList);
        
        // Then
        verify(leadNoteDao, never()).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void recordTimeline_WhenListIsNull_ShouldDoNothing() {
        // Given
        List<KafkaTimeline> nullList = null;
        
        // When
        fubLeadSyncHandler.recordTimeline(nullList);
        
        // Then
        verify(leadNoteDao, never()).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void recordTimeline_WhenTimelineTypeIsInNoteTypes_ShouldInsertLeadNote() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setId(1L);
        timeline.setLeadId(12345L);
        timeline.setTimelineType(8); // Type 26 is in getNoteTimelineTypes()
        timeline.setRefId(100L);
        
        List<KafkaTimeline> timelines = Collections.singletonList(timeline);
        
        // When
        fubLeadSyncHandler.recordTimeline(timelines);
        
        // Then
        verify(leadNoteDao, times(1)).insertLeadNote(argThat(leadNoteBo -> 
            leadNoteBo.getTimelineId() == 1L &&
            leadNoteBo.getLeadId() == 12345L &&
            leadNoteBo.getTimelineType() == 8 &&
            leadNoteBo.getRefId() == 100L
        ));
    }

    @Test
    void recordTimeline_WhenTimelineTypeIsNotInNoteTypes_ShouldNotInsertLeadNote() {
        // Given
        KafkaTimeline timeline = new KafkaTimeline();
        timeline.setId(1L);
        timeline.setLeadId(12345L);
        timeline.setTimelineType(999); // Type 999 is not in getNoteTimelineTypes()
        timeline.setRefId(100L);
        
        List<KafkaTimeline> timelines = Collections.singletonList(timeline);
        
        // When
        fubLeadSyncHandler.recordTimeline(timelines);
        
        // Then
        verify(leadNoteDao, never()).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void recordTimeline_WhenMultipleTimelinesWithMixedTypes_ShouldInsertOnlyNoteTypes() {
        // Given
        KafkaTimeline noteTimeline = new KafkaTimeline();
        noteTimeline.setId(1L);
        noteTimeline.setLeadId(12345L);
        noteTimeline.setTimelineType(16); // Note type
        noteTimeline.setRefId(100L);
        
        KafkaTimeline nonNoteTimeline = new KafkaTimeline();
        nonNoteTimeline.setId(2L);
        nonNoteTimeline.setLeadId(67890L);
        nonNoteTimeline.setTimelineType(999); // Non-note type
        nonNoteTimeline.setRefId(200L);
        
        List<KafkaTimeline> timelines = Arrays.asList(noteTimeline, nonNoteTimeline);
        
        // When
        fubLeadSyncHandler.recordTimeline(timelines);
        
        // Then
        verify(leadNoteDao, times(1)).insertLeadNote(argThat(leadNoteBo -> 
            leadNoteBo.getTimelineId() == 1L &&
            leadNoteBo.getTimelineType() == 16
        ));
    }

    @Test
    void recordTimeline_WhenInsertLeadNoteThrowsException_ShouldContinueProcessing() {
        // Given
        KafkaTimeline timeline1 = new KafkaTimeline();
        timeline1.setId(1L);
        timeline1.setLeadId(12345L);
        timeline1.setTimelineType(26);
        timeline1.setRefId(100L);
        
        KafkaTimeline timeline2 = new KafkaTimeline();
        timeline2.setId(2L);
        timeline2.setLeadId(67890L);
        timeline2.setTimelineType(16);
        timeline2.setRefId(200L);
        
        List<KafkaTimeline> timelines = Arrays.asList(timeline1, timeline2);
        
        // Mock first insert to throw exception, second to succeed
        doThrow(new RuntimeException("Database error")).when(leadNoteDao).insertLeadNote(argThat(bo -> bo.getTimelineId() == 1L));
        doReturn(1).when(leadNoteDao).insertLeadNote(argThat(bo -> bo.getTimelineId() == 2L));
        
        // When
        fubLeadSyncHandler.recordTimeline(timelines);
        
        // Then
        verify(leadNoteDao, times(2)).insertLeadNote(any(LeadNoteBo.class));
    }

    @Test
    void recordTimeline_WhenAllInsertsSucceed_ShouldInsertAllNoteTypes() {
        // Given
        KafkaTimeline timeline1 = new KafkaTimeline();
        timeline1.setId(1L);
        timeline1.setLeadId(12345L);
        timeline1.setTimelineType(8);
        timeline1.setRefId(100L);
        
        KafkaTimeline timeline2 = new KafkaTimeline();
        timeline2.setId(2L);
        timeline2.setLeadId(67890L);
        timeline2.setTimelineType(16);
        timeline2.setRefId(200L);
        
        List<KafkaTimeline> timelines = Arrays.asList(timeline1, timeline2);
        
        doReturn(1).when(leadNoteDao).insertLeadNote(any(LeadNoteBo.class));
        
        // When
        fubLeadSyncHandler.recordTimeline(timelines);
        
        // Then
        verify(leadNoteDao, times(2)).insertLeadNote(any(LeadNoteBo.class));
    }

    // ==================== Edge Case Tests ====================

    @Test
    void doDisconnectLeads_ShouldHaveEmptyMethodBody() {
        // Given
        List<Long> leadIds = Arrays.asList(12345L, 67890L);
        
        // When & Then
        assertDoesNotThrow(() -> fubLeadSyncHandler.doDisconnectLeads(leadIds));
    }

    // ==================== Helper Methods ====================

    /**
     * Helper method to call private method containsFubSource via reflection
     */
    private boolean invokeContainsFubSource(String content) {
        try {
            java.lang.reflect.Method method = FubLeadSyncHandler.class.getDeclaredMethod("containsFubSource", String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(fubLeadSyncHandler, content);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke private method", e);
        }
    }
}
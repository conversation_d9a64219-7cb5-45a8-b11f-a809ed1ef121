package com.homethy.fub.service.impl;

import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.zillow.model.constant.fub.FubUserPhone;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@ExtendWith(MockitoExtension.class)
class FubFamilyMemberServiceImplTest3 {
@InjectMocks
  FubFamilyMemberServiceImpl serviceImpl;
  @Test
  void testToUserPhoneWithValidPhone() {
    // Create test FubUserPhone with valid status
    FubUserPhone fubPhone = new FubUserPhone();
    fubPhone.setValue("+1234567890");
    fubPhone.setStatus("valid");  // This will make isValid() return true
    fubPhone.setIsPrimary(1);     // Mark as primary phone

    int testConsentState = 2;     // Example consent state value

    // Call the method to test
    UserPhoneBo result = serviceImpl.toUserPhone(fubPhone, testConsentState);

    // Verify all fields are correctly mapped
    assertEquals("+1234567890", result.getPhone(), "Phone number should match");
    assertEquals(UserPhoneBo.PhoneStateBo.VALID.toIntValue(), result.getState(),
        "State should be VALID for valid phone");
    assertEquals(testConsentState, result.getConsentState(),
        "Consent state should be set correctly");
    assertTrue(result.getIsPrimary(), "Phone should be marked as primary");
  }

  @Test
  void testToUserPhoneWithInvalidPhone() {
    // Create test FubUserPhone with invalid status
    FubUserPhone fubPhone = new FubUserPhone();
    fubPhone.setValue("+1234567890");
    fubPhone.setStatus("invalid");  // This will make isValid() return false
    fubPhone.setIsPrimary(0);      // Not a primary phone

    int testConsentState = 1;      // Example consent state value

    // Call the method to test
    UserPhoneBo result = serviceImpl.toUserPhone(fubPhone, testConsentState);

    // Verify state is set to BAD for invalid phone
    assertEquals(UserPhoneBo.PhoneStateBo.BAD.toIntValue(), result.getState(),
        "State should be BAD for invalid phone");
    assertFalse(result.getIsPrimary(), "Phone should not be marked as primary");
  }

  @Test
  void testToUserPhoneWithEdgeCases() {
    // Test with null phone number
    FubUserPhone nullPhone = new FubUserPhone();
    nullPhone.setValue(null);
    nullPhone.setStatus("valid");

    UserPhoneBo result = serviceImpl.toUserPhone(nullPhone, 0);
    assertNull(result.getPhone(), "Should handle null phone number");

    // Test with empty phone number
    FubUserPhone emptyPhone = new FubUserPhone();
    emptyPhone.setValue("");
    emptyPhone.setStatus("valid");

    result = serviceImpl.toUserPhone(emptyPhone, 0);
    assertEquals("", result.getPhone(), "Should handle empty phone number");

    // Test with different consent states
    FubUserPhone phone = new FubUserPhone();
    phone.setValue("+1234567890");
    phone.setStatus("valid");

    for (int i = 0; i < 5; i++) {
      result = serviceImpl.toUserPhone(phone, i);
      assertEquals(i, result.getConsentState(),
          "Consent state should be set to provided value");
    }
  }

  @Test
  void testPhoneStateMapping() {
    // Verify all possible state mappings
    FubUserPhone phone = new FubUserPhone();
    phone.setValue("+1234567890");

    // Test valid state
    phone.setStatus("valid");
    UserPhoneBo validResult = serviceImpl.toUserPhone(phone, 0);
    assertEquals(UserPhoneBo.PhoneStateBo.VALID.toIntValue(), validResult.getState());

    // Test invalid state
    phone.setStatus("invalid");
    UserPhoneBo invalidResult = serviceImpl.toUserPhone(phone, 0);
    assertEquals(UserPhoneBo.PhoneStateBo.BAD.toIntValue(), invalidResult.getState());

    // Test case-insensitive status
    phone.setStatus("VALID");
    validResult = serviceImpl.toUserPhone(phone, 0);
    assertEquals(UserPhoneBo.PhoneStateBo.VALID.toIntValue(), validResult.getState());
  }

  @Test
  void testHandleFamilyPhoneWithNewPhones() {
    // Create test data - new phones to add
    List<FubUserPhone> newPhones = new ArrayList<>();
    FubUserPhone phone1 = new FubUserPhone();
    phone1.setValue("+1234567890");
    phone1.setStatus("valid");
    phone1.setIsPrimary(1);
    newPhones.add(phone1);

    FubUserPhone phone2 = new FubUserPhone();
    phone2.setValue("+1987654321");
    phone2.setStatus("invalid");
    phone2.setIsPrimary(0);
    newPhones.add(phone2);

    // Create test data - existing phones
    List<UserPhoneBo> oldPhones = new ArrayList<>();
    UserPhoneBo existingPhone = new UserPhoneBo();
    existingPhone.setPhone("+1122334455"); // Different from new phones
    oldPhones.add(existingPhone);

    int testConsentState = 2; // Example consent state

    // Call method under test
    serviceImpl.handleFamilyPhone(newPhones, oldPhones, testConsentState);

    // Verify results
    assertEquals(3, oldPhones.size(), "Should add 2 new phones to existing 1");

    // Verify first added phone
    UserPhoneBo addedPhone1 = oldPhones.get(1);
    assertEquals("+1234567890", addedPhone1.getPhone());
    assertEquals(UserPhoneBo.PhoneStateBo.VALID.toIntValue(), addedPhone1.getState());
    assertTrue(addedPhone1.getIsPrimary());
    assertEquals(testConsentState, addedPhone1.getConsentState());

    // Verify second added phone
    UserPhoneBo addedPhone2 = oldPhones.get(2);
    assertEquals("+1987654321", addedPhone2.getPhone());
    assertEquals(UserPhoneBo.PhoneStateBo.BAD.toIntValue(), addedPhone2.getState());
    assertFalse(addedPhone2.getIsPrimary());
  }

  @Test
  void testHandleFamilyPhoneWithDuplicatePhones() {
    // Create test data with duplicate phone
    List<FubUserPhone> newPhones = new ArrayList<>();
    FubUserPhone duplicatePhone = new FubUserPhone();
    duplicatePhone.setValue("+1122334455"); // Same as existing
    newPhones.add(duplicatePhone);

    List<UserPhoneBo> oldPhones = new ArrayList<>();
    UserPhoneBo existingPhone = new UserPhoneBo();
    existingPhone.setPhone("+1122334455"); // Duplicate value
    oldPhones.add(existingPhone);

    // Call method under test
    serviceImpl.handleFamilyPhone(newPhones, oldPhones, 1);

    // Verify no new phone was added
    assertEquals(1, oldPhones.size(), "Should not add duplicate phone");
  }

  @Test
  void testHandleFamilyPhoneWithEmptyLists() {
    // Test with empty new phones list
    List<FubUserPhone> emptyNewPhones = new ArrayList<>();
    List<UserPhoneBo> oldPhones = new ArrayList<>();
    oldPhones.add(new UserPhoneBo());

    serviceImpl.handleFamilyPhone(emptyNewPhones, oldPhones, 1);
    assertEquals(1, oldPhones.size(), "Should not modify when new phones list is empty");

    // Test with empty old phones list
    List<FubUserPhone> newPhones = new ArrayList<>();
    FubUserPhone phone = new FubUserPhone();
    phone.setValue("+1234567890");
    newPhones.add(phone);

    List<UserPhoneBo> emptyOldPhones = new ArrayList<>();
    serviceImpl.handleFamilyPhone(newPhones, emptyOldPhones, 1);
    assertEquals(0, emptyOldPhones.size(), "Should add phone to empty list");
  }

  @Test
  void testHandleFamilyPhoneMultipleExistingPhones() {
    // Test with multiple existing phones
    List<FubUserPhone> newPhones = new ArrayList<>();
    FubUserPhone newPhone = new FubUserPhone();
    newPhone.setValue("+1234567890");
    newPhones.add(newPhone);

    List<UserPhoneBo> oldPhones = new ArrayList<>();
    oldPhones.add(createPhoneBo("+1111111111"));
    oldPhones.add(createPhoneBo("+2222222222"));

    serviceImpl.handleFamilyPhone(newPhones, oldPhones, 1);
    assertEquals(4, oldPhones.size(), "Should add one new phone");
    assertEquals("+1234567890", oldPhones.get(2).getPhone());
  }

  private UserPhoneBo createPhoneBo(String number) {
    UserPhoneBo phone = new UserPhoneBo();
    phone.setPhone(number);
    return phone;
  }

}
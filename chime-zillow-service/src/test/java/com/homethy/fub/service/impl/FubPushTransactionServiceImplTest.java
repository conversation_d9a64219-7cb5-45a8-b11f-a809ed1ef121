package com.homethy.fub.service.impl;

import com.homethy.microservice.BO.Commission;
import com.homethy.microservice.BO.Commission.PayType;
import com.homethy.microservice.BO.Commissions;
import com.homethy.microservice.BO.LeadTransactionDetailBO;
import com.homethy.zillow.model.constant.fub.FubDealRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FubPushTransactionServiceImplTest {

  @InjectMocks
  private FubPushTransactionServiceImpl fubPushTransactionService;

  private LeadTransactionDetailBO leadTransaction;
  private FubDealRequest request;

  @BeforeEach
  void setUp() {
    leadTransaction = new LeadTransactionDetailBO();
    leadTransaction.setId(12345L);
    leadTransaction.setOwnerId(1001L);

    request = new FubDealRequest();
  }

  @Test
  void handleCommission_WithValidAgentCommission_ShouldSetAgentCommission() {
    // Given
    Commission agentCommission = new Commission();
    agentCommission.setPayType(PayType.AGENT);
    agentCommission.setPayTo(1001L);
    agentCommission.setValue(5000L); // $50.00 in cents

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(agentCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertEquals(new BigDecimal("50.00"), request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithValidTeamCommission_ShouldSetTeamCommission() {
    // Given
    Commission teamCommission = new Commission();
    teamCommission.setPayType(PayType.TEAM);
    teamCommission.setValue(3000L); // $30.00 in cents

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(teamCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertEquals(new BigDecimal("30.00"), request.getTeamCommission());
    assertNull(request.getAgentCommission());
  }

  @Test
  void handleCommission_WithBothAgentAndTeamCommission_ShouldSetBoth() {
    // Given
    Commission agentCommission = new Commission();
    agentCommission.setPayType(PayType.AGENT);
    agentCommission.setPayTo(1001L);
    agentCommission.setValue(5000L); // $50.00 in cents

    Commission teamCommission = new Commission();
    teamCommission.setPayType(PayType.TEAM);
    teamCommission.setValue(3000L); // $30.00 in cents

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(agentCommission, teamCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertEquals(new BigDecimal("50.00"), request.getAgentCommission());
    assertEquals(new BigDecimal("30.00"), request.getTeamCommission());
  }

  @Test
  void handleCommission_WithAgentCommissionForDifferentOwner_ShouldNotSetAgentCommission() {
    // Given
    Commission agentCommission = new Commission();
    agentCommission.setPayType(PayType.AGENT);
    agentCommission.setPayTo(9999L); // Different owner ID
    agentCommission.setValue(5000L);

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(agentCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithNullCommissions_ShouldNotSetAnyCommission() {
    // Given
    leadTransaction.setCommissions(null);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithEmptyPreSplit_ShouldNotSetAnyCommission() {
    // Given
    Commissions commissions = new Commissions();
    commissions.setPreSplit(Collections.emptyList());
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithNullPreSplit_ShouldNotSetAnyCommission() {
    // Given
    Commissions commissions = new Commissions();
    commissions.setPreSplit(null);
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithNullLeadTransaction_ShouldNotSetAnyCommission() {
    // Given
    LeadTransactionDetailBO nullTransaction = null;

    // When
    fubPushTransactionService.handleCommission(nullTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithNullRequest_ShouldNotThrowException() {
    // Given
    Commission agentCommission = new Commission();
    agentCommission.setPayType(PayType.AGENT);
    agentCommission.setPayTo(1001L);
    agentCommission.setValue(5000L);

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(agentCommission));
    leadTransaction.setCommissions(commissions);

    FubDealRequest nullRequest = null;

    // When & Then
    assertDoesNotThrow(
        () -> fubPushTransactionService.handleCommission(leadTransaction, nullRequest));
  }

  @Test
  void handleCommission_WithNullCommissionInList_ShouldSkipNullCommission() {
    // Given
    Commission validCommission = new Commission();
    validCommission.setPayType(PayType.AGENT);
    validCommission.setPayTo(1001L);
    validCommission.setValue(5000L);

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(null, validCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertEquals(new BigDecimal("50.00"), request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithNullPayType_ShouldSkipCommission() {
    // Given
    Commission commissionWithNullPayType = new Commission();
    commissionWithNullPayType.setPayType(null);
    commissionWithNullPayType.setPayTo(1001L);
    commissionWithNullPayType.setValue(5000L);

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(commissionWithNullPayType));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertNull(request.getAgentCommission());
    assertNull(request.getTeamCommission());
  }

  @Test
  void handleCommission_WithDecimalValues_ShouldHandleRoundingCorrectly() {
    // Given
    Commission agentCommission = new Commission();
    agentCommission.setPayType(PayType.AGENT);
    agentCommission.setPayTo(1001L);
    agentCommission.setValue(5055L); // $50.55 in cents

    Commissions commissions = new Commissions();
    commissions.setPreSplit(Arrays.asList(agentCommission));
    leadTransaction.setCommissions(commissions);

    // When
    fubPushTransactionService.handleCommission(leadTransaction, request);

    // Then
    assertEquals(new BigDecimal("50.55"), request.getAgentCommission());
  }

     @Test
   void handleCommission_WithZeroValue_ShouldSetZeroCommission() {
     // Given
     Commission zeroCommission = new Commission();
     zeroCommission.setPayType(PayType.AGENT);
     zeroCommission.setPayTo(1001L);
     zeroCommission.setValue(0L);

     Commissions commissions = new Commissions();
     commissions.setPreSplit(Arrays.asList(zeroCommission));
     leadTransaction.setCommissions(commissions);

     // When
     fubPushTransactionService.handleCommission(leadTransaction, request);

     // Then
     assertEquals(new BigDecimal("0.00"), request.getAgentCommission());
   }
}

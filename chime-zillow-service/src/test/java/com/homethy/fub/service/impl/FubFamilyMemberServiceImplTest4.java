package com.homethy.fub.service.impl;

import com.homethy.microservice.client.model.LeadFamilyMemberVoBo;
import com.homethy.microservice.client.model.UserEmailBo;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.zillow.model.constant.fub.FubEmail;
import com.homethy.zillow.model.constant.fub.FubFamilyMember;
import com.homethy.zillow.model.constant.fub.FubUserPhone;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@ExtendWith(MockitoExtension.class)
class FubFamilyMemberServiceImplTest4 {

  @InjectMocks
  FubFamilyMemberServiceImpl serviceImpl;
  @Test
  void testAddNewFamilyMemberWithContacts() {
    // Setup test data
    List<FubFamilyMember> newMembers = new ArrayList<>();
    FubFamilyMember member = new FubFamilyMember();
    member.setFirstName("John");
    member.setLastName("Doe");
    member.setType("Spouse");

    // Add emails
    List<FubEmail> emails = new ArrayList<>();
    emails.add(createFubEmail("<EMAIL>", 1));
    emails.add(createFubEmail("<EMAIL>", 0));
    member.setEmails(emails);

    // Add phones
    List<FubUserPhone> phones = new ArrayList<>();
    phones.add(createFubPhone("+1234567890", "valid", 1));
    phones.add(createFubPhone("+1987654321", "invalid", 0));
    member.setPhones(phones);

    newMembers.add(member);

    List<LeadFamilyMemberVoBo> existingMembers = new ArrayList<>();
    int testConsentState = 2; // Example consent state

    // Execute method
    serviceImpl.addNewFamilyMember(newMembers, existingMembers, testConsentState);

    // Verify results
    assertEquals(1, existingMembers.size(), "Should add one new member");
    LeadFamilyMemberVoBo addedMember = existingMembers.get(0);

    // Verify basic info
    assertEquals("John", addedMember.getFirstName());
    assertEquals("Doe", addedMember.getLastName());
    assertEquals("Spouse", addedMember.getRelation());

    // Verify emails
    assertEquals(2, addedMember.getUserEmailList().size());
    assertEmailExists(addedMember.getUserEmailList(), "<EMAIL>", true);
    assertEmailExists(addedMember.getUserEmailList(), "<EMAIL>", false);

    // Verify phones
    assertEquals(2, addedMember.getUserPhoneList().size());
    assertPhoneExists(addedMember.getUserPhoneList(), "+1234567890", true);
    assertPhoneExists(addedMember.getUserPhoneList(), "+1987654321", false);
  }

  /**
   * Tests adding multiple new family members
   */
  @Test
  void testAddMultipleFamilyMembers() {
    List<FubFamilyMember> newMembers = new ArrayList<>();

    // First member
    FubFamilyMember member1 = new FubFamilyMember();
    member1.setFirstName("Alice");
    member1.setLastName("Smith");
    member1.setType("Child");
    member1.setEmails(List.of(createFubEmail("<EMAIL>", 1)));
    newMembers.add(member1);

    // Second member
    FubFamilyMember member2 = new FubFamilyMember();
    member2.setFirstName("Bob");
    member2.setLastName("Smith");
    member2.setType("Child");
    member2.setPhones(List.of(createFubPhone("+1122334455", "valid", 0)));
    newMembers.add(member2);

    List<LeadFamilyMemberVoBo> existingMembers = new ArrayList<>();

    // Execute method
    serviceImpl.addNewFamilyMember(newMembers, existingMembers, 1);

    // Verify both members were added
    assertEquals(2, existingMembers.size());
    assertMemberExists(existingMembers, "Alice", "Smith");
    assertMemberExists(existingMembers, "Bob", "Smith");
  }

  /**
   * Tests adding a family member with no contact information
   */
  @Test
  void testAddFamilyMemberWithoutContacts() {
    FubFamilyMember member = new FubFamilyMember();
    member.setFirstName("No");
    member.setLastName("Contacts");
    member.setType("Other");

    List<LeadFamilyMemberVoBo> existingMembers = new ArrayList<>();

    serviceImpl.addNewFamilyMember(List.of(member), existingMembers, 1);

    assertEquals(1, existingMembers.size());
    LeadFamilyMemberVoBo addedMember = existingMembers.get(0);
    assertNull(addedMember.getUserEmailList(), "Email list should be null");
    assertNull(addedMember.getUserPhoneList(), "Phone list should be null");
  }

  // Helper methods
  private FubEmail createFubEmail(String address, int isPrimary) {
    FubEmail email = new FubEmail();
    email.setValue(address);
    email.setIsPrimary(isPrimary);
    return email;
  }

  private FubUserPhone createFubPhone(String number, String status, int isPrimary) {
    FubUserPhone phone = new FubUserPhone();
    phone.setValue(number);
    phone.setStatus(status);
    phone.setIsPrimary(isPrimary);
    return phone;
  }

  private void assertEmailExists(List<UserEmailBo> emails, String address, boolean shouldBePrimary) {
    UserEmailBo found = emails.stream()
        .filter(e -> e.getEmail().equals(address))
        .findFirst()
        .orElse(null);
    assertNotNull(found, "Email " + address + " should exist");
    assertEquals(shouldBePrimary, found.getIsPrimary(), "Primary flag mismatch");
  }

  private void assertPhoneExists(List<UserPhoneBo> phones, String number, boolean shouldBePrimary) {
    UserPhoneBo found = phones.stream()
        .filter(p -> p.getPhone().equals(number))
        .findFirst()
        .orElse(null);
    assertNotNull(found, "Phone " + number + " should exist");
    assertEquals(shouldBePrimary, found.getIsPrimary(), "Primary flag mismatch");
  }

  private void assertMemberExists(List<LeadFamilyMemberVoBo> members, String firstName, String lastName) {
    boolean exists = members.stream()
        .anyMatch(m -> m.getFirstName().equals(firstName) && m.getLastName().equals(lastName));
    assertTrue(exists, "Member " + firstName + " " + lastName + " should exist");
  }

}
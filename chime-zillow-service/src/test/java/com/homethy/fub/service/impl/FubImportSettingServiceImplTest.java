package com.homethy.fub.service.impl;

import com.homethy.fub.dao.FubImportSettingDao;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.team.TeamSource;
import com.homethy.microservice.client.model.ConsentStateEnumBo;
import com.homethy.microservice.client.source.TeamSourceService;
import com.homethy.i18n.util.MsgException;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubImportSetting;
import com.homethy.zillow.model.constant.fub.FubSettingChangeInfo;
import com.homethy.zillow.model.constant.fub.FubSettingType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FubImportSettingServiceImplTest {

    @InjectMocks
    private FubImportSettingServiceImpl fubImportSettingService;

    @Mock
    private FubImportSettingDao fubImportSettingDao;

    @Mock
    private UserManager userManager;

    @Mock
    private TeamSourceService teamSourceService;

    private User testUser;
    private FubImportSetting testSetting;
    private FubSettingChangeInfo testChangeInfo;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(456L);
        testUser.setTeamId(789L);

        testSetting = new FubImportSetting();
        testSetting.setOwnershipId(123L);
        testSetting.setOwnershipScope(OwnershipScope.PERSONAL);
        testSetting.setInstanceUserId(456L);
        testSetting.setTeamId(789L);
        testSetting.setNumberConsent(ConsentStateEnumBo.UNKNOWN_CONSENT.getCode());
        testSetting.setSourceId(999);

        testChangeInfo = new FubSettingChangeInfo();
        testChangeInfo.setOwnershipId(123L);
        testChangeInfo.setOwnershipScope(OwnershipScope.PERSONAL);
    }

    @Test
    void testGetImportSetting_WhenSettingExists_ShouldReturnExistingSetting() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.PERSONAL;
        long ownershipId = 123L;

        when(fubImportSettingDao.selectByOwnership(ownershipId, ownershipScope.name())).thenReturn(testSetting);

        // When
        FubImportSetting result = fubImportSettingService.getImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        assertEquals(testSetting, result);
        verify(fubImportSettingDao).selectByOwnership(ownershipId, ownershipScope.name());
        verify(fubImportSettingDao, never()).insert(any(FubImportSetting.class));
    }

    @Test
    void testGetImportSetting_WhenSettingDoesNotExist_PersonalScope_ShouldCreateAndReturnDefaultSetting() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.PERSONAL;
        long ownershipId = 123L;

        TeamSource teamSource = new TeamSource();
        teamSource.setLeadSource(999);

        when(fubImportSettingDao.selectByOwnership(ownershipId, ownershipScope.name())).thenReturn(null);
        when(teamSourceService.getByName(testUser.getTeamId(), "Follow Up Boss")).thenReturn(teamSource);

        ArgumentCaptor<FubImportSetting> settingCaptor = ArgumentCaptor.forClass(FubImportSetting.class);

        // When
        FubImportSetting result = fubImportSettingService.getImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        verify(fubImportSettingDao).selectByOwnership(ownershipId, ownershipScope.name());
        verify(fubImportSettingDao).insert(settingCaptor.capture());

        FubImportSetting capturedSetting = settingCaptor.getValue();
        assertEquals(ownershipId, capturedSetting.getOwnershipId());
        assertEquals(ownershipScope, capturedSetting.getOwnershipScope());
        assertEquals(testUser.getId(), capturedSetting.getInstanceUserId());
        assertEquals(testUser.getTeamId(), capturedSetting.getTeamId());
        assertEquals(ConsentStateEnumBo.UNKNOWN_CONSENT.getCode(), capturedSetting.getNumberConsent());
        assertEquals(teamSource.getLeadSource(), capturedSetting.getSourceId());
    }

    @Test
    void testGetImportSetting_WhenSettingDoesNotExist_OfficeScope_ShouldCreateAndReturnDefaultSetting() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.OFFICE;
        long ownershipId = 123L;
        long expectedTeamId = 999L;

        TeamSource teamSource = new TeamSource();
        teamSource.setLeadSource(888);

        when(fubImportSettingDao.selectByOwnership(ownershipId, ownershipScope.name())).thenReturn(null);
        when(userManager.getTeamIdNotPersonal(ownershipScope, ownershipId)).thenReturn(expectedTeamId);
        when(teamSourceService.getByName(expectedTeamId, "Follow Up Boss")).thenReturn(teamSource);

        ArgumentCaptor<FubImportSetting> settingCaptor = ArgumentCaptor.forClass(FubImportSetting.class);

        // When
        FubImportSetting result = fubImportSettingService.getImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        verify(fubImportSettingDao).selectByOwnership(ownershipId, ownershipScope.name());
        verify(userManager).getTeamIdNotPersonal(ownershipScope, ownershipId);
        verify(fubImportSettingDao).insert(settingCaptor.capture());

        FubImportSetting capturedSetting = settingCaptor.getValue();
        assertEquals(ownershipId, capturedSetting.getOwnershipId());
        assertEquals(ownershipScope, capturedSetting.getOwnershipScope());
        assertEquals(0, capturedSetting.getInstanceUserId());
        assertEquals(expectedTeamId, capturedSetting.getTeamId());
        assertEquals(ConsentStateEnumBo.UNKNOWN_CONSENT.getCode(), capturedSetting.getNumberConsent());
        assertEquals(teamSource.getLeadSource(), capturedSetting.getSourceId());
    }

    @Test
    void testGetImportSetting_WhenTeamSourceNotFound_ShouldSetSourceIdToZero() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.PERSONAL;
        long ownershipId = 123L;

        when(fubImportSettingDao.selectByOwnership(ownershipId, ownershipScope.name())).thenReturn(null);
        when(teamSourceService.getByName(testUser.getTeamId(), "Follow Up Boss")).thenReturn(null);

        ArgumentCaptor<FubImportSetting> settingCaptor = ArgumentCaptor.forClass(FubImportSetting.class);

        // When
        FubImportSetting result = fubImportSettingService.getImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        verify(fubImportSettingDao).insert(settingCaptor.capture());

        FubImportSetting capturedSetting = settingCaptor.getValue();
        assertEquals(0, capturedSetting.getSourceId());
    }

    @Test
    void testGetImportSetting_WhenTeamIdNotFound_ShouldThrowException() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.OFFICE;
        long ownershipId = 123L;

        when(fubImportSettingDao.selectByOwnership(ownershipId, ownershipScope.name())).thenReturn(null);
        when(userManager.getTeamIdNotPersonal(ownershipScope, ownershipId)).thenReturn(0L);

        // When & Then
        MsgException exception = assertThrows(MsgException.class, () ->
                fubImportSettingService.getImportSetting(ownershipScope, ownershipId, testUser));
        assertEquals(ZillowErrorCodeEnum.UNKNOWN_USER, exception.getKey());
    }

    @Test
    void testUpdateImportSetting_ConsentType_ShouldUpdateConsent() {
        // Given
        testChangeInfo.setNumberConsent(1); // 使用数字而不是枚举

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.consent, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateConsent(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.getNumberConsent()
        );
    }

    @Test
    void testUpdateImportSetting_InstanceType_ShouldUpdateInstance() {
        // Given
        testChangeInfo.setInstanceUserId(789L);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.instance, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateInstance(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.getInstanceUserId()
        );
    }

    @Test
    void testUpdateImportSetting_SourceType_ShouldUpdateSource() {
        // Given
        testChangeInfo.setSourceId(555L);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.source, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateSource(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.getSourceId()
        );
    }

    @Test
    void testUpdateImportSetting_InitType_ShouldUpdateInitInfo() {
        // When
        fubImportSettingService.updateImportSetting(FubSettingType.init, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateInitInfo(testChangeInfo);
    }

    @Test
    void testUpdateImportSetting_SwitchesType_Call_ShouldUpdateCall() {
        // Given
        testChangeInfo.setType(FubSettingChangeInfo.SwitchType.CALL);
        testChangeInfo.setOpen(true);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.switches, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateCall(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.isOpen()
        );
    }

    @Test
    void testUpdateImportSetting_SwitchesType_Text_ShouldUpdateText() {
        // Given
        testChangeInfo.setType(FubSettingChangeInfo.SwitchType.TEXT);
        testChangeInfo.setOpen(false);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.switches, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateText(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.isOpen()
        );
    }

    @Test
    void testUpdateImportSetting_SwitchesType_Email_Open_ShouldUpdateEmail() {
        // Given
        testChangeInfo.setType(FubSettingChangeInfo.SwitchType.EMAIL);
        testChangeInfo.setOpen(true);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.switches, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateEmail(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                true
        );
        verify(fubImportSettingDao, never()).updateWelcome(anyLong(), any(), anyBoolean());
    }

    @Test
    void testUpdateImportSetting_SwitchesType_Email_Closed_ShouldUpdateEmailAndWelcome() {
        // Given
        testChangeInfo.setType(FubSettingChangeInfo.SwitchType.EMAIL);
        testChangeInfo.setOpen(false);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.switches, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateEmail(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                false
        );
        verify(fubImportSettingDao).updateWelcome(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                false
        );
    }

    @Test
    void testUpdateImportSetting_SwitchesType_WelcomeEmail_ShouldUpdateWelcome() {
        // Given
        testChangeInfo.setType(FubSettingChangeInfo.SwitchType.WELCOME_EMAIL);
        testChangeInfo.setOpen(true);

        // When
        fubImportSettingService.updateImportSetting(FubSettingType.switches, testChangeInfo);

        // Then
        verify(fubImportSettingDao).updateWelcome(
                testChangeInfo.getOwnershipId(),
                testChangeInfo.getOwnershipScope(),
                testChangeInfo.isOpen()
        );
    }

    @Test
    void testGetDefaultImportSetting_PersonalScope_ShouldSetInstanceUserId() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.PERSONAL;
        long ownershipId = 123L;

        TeamSource teamSource = new TeamSource();
        teamSource.setLeadSource(999);

        when(teamSourceService.getByName(testUser.getTeamId(), "Follow Up Boss")).thenReturn(teamSource);

        // When
        FubImportSetting result = fubImportSettingService.getDefaultImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        assertEquals(ownershipId, result.getOwnershipId());
        assertEquals(ownershipScope, result.getOwnershipScope());
        assertEquals(testUser.getId(), result.getInstanceUserId());
        assertEquals(testUser.getTeamId(), result.getTeamId());
        assertEquals(ConsentStateEnumBo.UNKNOWN_CONSENT.getCode(), result.getNumberConsent());
        assertEquals(teamSource.getLeadSource(), result.getSourceId());
    }

    @Test
    void testGetDefaultImportSetting_OfficeScope_ShouldSetInstanceUserIdToZero() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.OFFICE;
        long ownershipId = 123L;
        long expectedTeamId = 999L;

        TeamSource teamSource = new TeamSource();
        teamSource.setLeadSource(888);

        when(userManager.getTeamIdNotPersonal(ownershipScope, ownershipId)).thenReturn(expectedTeamId);
        when(teamSourceService.getByName(expectedTeamId, "Follow Up Boss")).thenReturn(teamSource);

        // When
        FubImportSetting result = fubImportSettingService.getDefaultImportSetting(ownershipScope, ownershipId, testUser);

        // Then
        assertNotNull(result);
        assertEquals(ownershipId, result.getOwnershipId());
        assertEquals(ownershipScope, result.getOwnershipScope());
        assertEquals(0, result.getInstanceUserId());
        assertEquals(expectedTeamId, result.getTeamId());
        assertEquals(ConsentStateEnumBo.UNKNOWN_CONSENT.getCode(), result.getNumberConsent());
        assertEquals(teamSource.getLeadSource(), result.getSourceId());
    }

    @Test
    void testGetDefaultImportSetting_WhenTeamIdNotFound_ShouldThrowException() {
        // Given
        OwnershipScope ownershipScope = OwnershipScope.OFFICE;
        long ownershipId = 123L;

        when(userManager.getTeamIdNotPersonal(ownershipScope, ownershipId)).thenReturn(0L);

        // When & Then
        MsgException exception = assertThrows(MsgException.class, () ->
                fubImportSettingService.getDefaultImportSetting(ownershipScope, ownershipId, testUser));
        assertEquals(ZillowErrorCodeEnum.UNKNOWN_USER, exception.getKey());
    }
} 
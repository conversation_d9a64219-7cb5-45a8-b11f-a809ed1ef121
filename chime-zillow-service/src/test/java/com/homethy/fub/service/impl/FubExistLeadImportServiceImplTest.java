package com.homethy.fub.service.impl;

import com.homethy.MockitoBase;
import com.homethy.persistence.service.base.RedisService;
import com.homethy.zillow.client.NotificationManager;
import com.homethy.zillow.model.constant.ZillowConstant;
import com.homethy.zillow.model.constant.fub.FubPeople;
import com.homethy.zillow.model.constant.fub.FubSimplePeople;
import com.homethy.zillow.model.constant.fub.FubStateInfo;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import com.homethy.zillow.model.po.FubLeadImportTask;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static com.homethy.zillow.client.NotificationManager.FUB_IMPORT_CHANNEL;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class FubExistLeadImportServiceImplTest extends MockitoBase {

    @InjectMocks
    private FubExistLeadImportServiceImpl fubExistLeadImportService;

    @Mock
    private RedisService redisService;

    @Mock
    private NotificationManager notificationManager;

    private final long TEST_AGENT_ID = 123L;
    private final String TEST_REDIS_KEY = "FUB_SYNC_LEAD_" + TEST_AGENT_ID;

    @BeforeEach
    void setUp() {
    }

    @Test
    void handleNotification_WhenTaskIsNull_ShouldReturnEarly() {
        // When
        fubExistLeadImportService.handleNotification(null);

        // Then
        verifyNoInteractions(redisService, notificationManager);
    }

    @Test
    void handleNotification_WhenTaskIsNotHistorySync_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(false, false);

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verifyNoInteractions(redisService, notificationManager);
    }

    @Test
    void handleNotification_WhenTaskIsNotLastOne_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(true, false);

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verifyNoInteractions(redisService, notificationManager);
    }

    @Test
    void handleNotification_WhenLeadCountIsZero_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(true, true);

        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn("0");

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
        verifyNoInteractions(notificationManager);
        verifyNoMoreInteractions(redisService);
    }

    @Test
    void handleNotification_WhenLeadCountIsNegative_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(true, true);

        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn("-1");

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
        verifyNoInteractions(notificationManager);
        verifyNoMoreInteractions(redisService);
    }

    @Test
    void handleNotification_WhenLeadCountIsPositive_ShouldAddNotificationAndDeleteRedisKey() {
        // Given
        FubLeadImportTask task = createTestTask(true, true);
        int leadCount = 5;

        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn(String.valueOf(leadCount));

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
        verify(notificationManager).addNotification(TEST_AGENT_ID, leadCount, FUB_IMPORT_CHANNEL);
        verify(redisService).del(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void handleNotification_WhenRedisReturnsNull_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(true, true);

        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn(null);

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
        verifyNoInteractions(notificationManager);
        verifyNoMoreInteractions(redisService);
    }

    @Test
    void handleNotification_WhenRedisReturnsInvalidNumber_ShouldReturnEarly() {
        // Given
        FubLeadImportTask task = createTestTask(true, true);

        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn("invalid");

        // When
        fubExistLeadImportService.handleNotification(task);

        // Then
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
        verifyNoInteractions(notificationManager);
        verifyNoMoreInteractions(redisService);
    }

    @Test
    void addLeadCount_ShouldIncrementRedisValue() {
        // When
        fubExistLeadImportService.addLeadCount(TEST_AGENT_ID);

        // Then
        verify(redisService).incr(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void getLeadCount_WhenRedisReturnsValidNumber_ShouldReturnParsedValue() {
        // Given
        int expectedCount = 10;
        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn(String.valueOf(expectedCount));

        // When
        int result = fubExistLeadImportService.getLeadCount(TEST_AGENT_ID);

        // Then
        assertEquals(expectedCount, result);
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void getLeadCount_WhenRedisReturnsNull_ShouldReturnZero() {
        // Given
        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn(null);

        // When
        int result = fubExistLeadImportService.getLeadCount(TEST_AGENT_ID);

        // Then
        assertEquals(0, result);
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void getLeadCount_WhenRedisReturnsInvalidNumber_ShouldReturnZero() {
        // Given
        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn("invalid");

        // When
        int result = fubExistLeadImportService.getLeadCount(TEST_AGENT_ID);

        // Then
        assertEquals(0, result);
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void getLeadCount_WhenRedisReturnsEmptyString_ShouldReturnZero() {
        // Given
        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn("");

        // When
        int result = fubExistLeadImportService.getLeadCount(TEST_AGENT_ID);

        // Then
        assertEquals(0, result);
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    @Test
    void getLeadCount_WhenRedisReturnsNegativeNumber_ShouldReturnParsedValue() {
        // Given
        int expectedCount = -5;
        when(redisService.get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY)).thenReturn(String.valueOf(expectedCount));

        // When
        int result = fubExistLeadImportService.getLeadCount(TEST_AGENT_ID);

        // Then
        assertEquals(expectedCount, result);
        verify(redisService).get(ZillowConstant.ZILLOW_AREA, TEST_REDIS_KEY);
    }

    private FubLeadImportTask createTestTask(boolean isHistorySync, boolean isLastOne) {
        FubLeadImportTask task = new FubLeadImportTask();
        task.setHistorySync(isHistorySync);

        FubSimplePeople fubPeople = new FubSimplePeople();
        fubPeople.setLastOne(isLastOne);
        task.setFubPeople(fubPeople);

        FubStateInfo fubInfo = new FubStateInfo();
        fubInfo.setBindingAgentId(TEST_AGENT_ID);
        task.setFubInfo(fubInfo);
        
        return task;
    }
}

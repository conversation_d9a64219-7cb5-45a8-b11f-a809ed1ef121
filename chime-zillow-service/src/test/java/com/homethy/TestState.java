package com.homethy;

import com.homethy.microservice.client.model.LeadFamilyMemberVoBo;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.client.NoSubLeadFamilyMemberVoBo;
import org.junit.jupiter.api.Test;

public class TestState {
@Test
  public void test() {
  LeadFamilyMemberVoBo leadFamilyMemberVoBo = new LeadFamilyMemberVoBo();
  System.out.println(JacksonUtils.toJson(leadFamilyMemberVoBo));

  LeadFamilyMemberVoBo build = new  NoSubLeadFamilyMemberVoBo();
  System.out.println(JacksonUtils.toJson(build));
}
}

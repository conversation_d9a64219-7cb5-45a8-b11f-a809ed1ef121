package com.homethy.zillow.service.impl;

import com.homethy.microservice.client.leadtask.AppointmentClientService;
import com.homethy.microservice.client.model.Appointment;
import com.homethy.microservice.client.model.DeleteAppointmentRequest;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubStateInfo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FubAppointmentDeleteWebhookServiceTest {
  @InjectMocks
  private FubAppointmentDeleteWebhookService service;
  @Mock
  private AppointmentClientService appointmentClientService;

  @Test
  void testGetImportType() {
    assertEquals(FubImportTypeEnum.APPOINTMENTS_DELETED, service.getImportType());
  }

  @Test
  void testHandle_exist() {
    // Setup
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    FubAppointmentRef ref = mock(FubAppointmentRef.class);
    Appointment existingAppointment = mock(Appointment.class);

    // Mock state info
    FubStateInfo stateInfo = mock(FubStateInfo.class);
    when(fubRecord.getStateInfo()).thenReturn(stateInfo);
    when(stateInfo.getBindingAgentId()).thenReturn(123L);

    // Mock appointment reference and client service
    when(ref.getAppId()).thenReturn(456L);
    when(appointmentClientService.get(456L)).thenReturn(existingAppointment);

    // Execute
    Appointment result = service.handle(fubRecord, ref);

    // Verify
    assertEquals(existingAppointment, result);
    verify(appointmentClientService).delete(eq(456L), any(DeleteAppointmentRequest.class));
  }

  @Test
  void testHandle_notExist() {
    // Setup
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    FubAppointmentRef ref = mock(FubAppointmentRef.class);

    // Mock state info
    com.homethy.zillow.model.constant.fub.FubStateInfo stateInfo =
        mock(com.homethy.zillow.model.constant.fub.FubStateInfo.class);
    when(fubRecord.getStateInfo()).thenReturn(stateInfo);
    when(stateInfo.getBindingAgentId()).thenReturn(123L);

    // Mock appointment reference and client service
    when(ref.getAppId()).thenReturn(456L);
    when(appointmentClientService.get(456L)).thenReturn(null);

    // Execute
    Appointment result = service.handle(fubRecord, ref);

    // Verify
    assertNull(result);
    verify(appointmentClientService, never()).delete(anyLong(),
        any(DeleteAppointmentRequest.class));
  }
} 

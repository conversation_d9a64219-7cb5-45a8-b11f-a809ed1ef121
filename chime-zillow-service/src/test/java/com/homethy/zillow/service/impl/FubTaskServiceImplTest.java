package com.homethy.zillow.service.impl;

import com.homethy.fub.dao.FubNotifyLogDao;
import com.homethy.fub.dao.FubTaskRefDao;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadTask;
import com.homethy.microservice.exception.HomethyBusinessException;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.client.LeadManager;
import com.homethy.zillow.manager.FubQueryManager;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;
import com.homethy.zillow.model.constant.fub.FubTask;
import com.homethy.zillow.model.constant.fub.FubTaskRef;
import com.homethy.zillow.model.constant.fub.FubTaskRequest;
import com.homethy.zillow.model.constant.fub.FubToken;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FubTaskServiceImplTest {


  @Spy
  @InjectMocks
  private SyncCreateToFubTaskService syncCreateToFubTaskService;
  @Spy
  @InjectMocks
  private SyncUpdateToFubTaskService syncUpdateToFubTaskService;
  @Spy
  @InjectMocks
  private SyncDeleteToFubTaskService syncDeleteToFubTaskService;

  @Mock
  private FubClient fubClient;

  @Mock
  private FubTaskRefDao fubTaskRefDao;

  @Mock
  private FubNotifyLogDao fubNotifyLogDao;

  @Mock
  private FubTokenService fubTokenService;

  @Mock
  private LeadManager leadManager;

  @Mock
  private FubQueryManager fubQueryManager;

  private static final String CALL_TOKEN = "Bearer test-token";
  private static final FubToken TOKEN = new FubToken();

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    TOKEN.setAccessToken("test-token");
  }

  @Test
  public void handCreateTask_WithValidData_ShouldCreateSuccessfully() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubTask fubTask = createTestFubTask();
    FubTask createdFubTask = createTestFubTask();
    createdFubTask.setId(123L);

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubQueryManager.getFubPersonId(leadTask.getLeadId())).thenReturn(456L);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(TOKEN);
    when(fubQueryManager.getFubUserId(leadTask.getAssignToUid(), CALL_TOKEN)).thenReturn(789L);
    when(fubClient.createTask(eq(CALL_TOKEN), any(FubTaskRequest.class))).thenReturn(createdFubTask);

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verify(leadManager).getLeadById(leadTask.getLeadId());
    verify(leadManager).getOwnershipFromLead(lead);
    verify(fubTokenService).getTokenWithRefresh(ownership);
    verify(fubClient).createTask(eq(CALL_TOKEN), any(FubTaskRequest.class));
    verify(fubTaskRefDao).insert(any(FubTaskRef.class));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handCreateTask_WithNoLead_ShouldLogFailureAndReturn() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(null);

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verify(leadManager).getLeadById(leadTask.getLeadId());
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubTaskRefDao);
  }

  @Test
  public void handCreateTask_WithFUBSource_ShouldReturn() {
    LeadTask leadTask = createTestLeadTask();
    leadTask.setOriginalSource("FUB");
    verifyNoInteractions(fubNotifyLogDao);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubTaskRefDao);
  }

  @Test
  public void handCreateTask_WithNoToken_ShouldLogFailureAndReturn() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(null);

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verify(fubTokenService).getTokenWithRefresh(ownership);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubTaskRefDao);
  }

  @Test
  public void handCreateTask_WithFubRateLimitException_ShouldLogRateLimitStatus() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    HomethyBusinessException rateLimitException = mock(HomethyBusinessException.class);
    when(rateLimitException.getErrorCode()).thenReturn(ZillowErrorCodeEnum.FUB_CALL_LIMIT_EXCEEDED.getErrorCode());

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubQueryManager.getFubPersonId(leadTask.getLeadId())).thenReturn(456L);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(TOKEN);
    when(fubQueryManager.getFubUserId(leadTask.getAssignToUid(), CALL_TOKEN)).thenReturn(789L);
    when(fubClient.createTask(eq(CALL_TOKEN), any(FubTask.class))).thenThrow(rateLimitException);

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
    verifyNoInteractions(fubTaskRefDao);
  }

  @Test
  public void handCreateTask_WithUnsupportedTaskType_ShouldIgnoreAndReturn() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    leadTask.setWay(999); // Unsupported task type

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verifyNoInteractions(leadManager);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubTaskRefDao);
    verifyNoInteractions(fubNotifyLogDao);
  }

  @Test
  public void handUpdateTask_WithValidData_ShouldUpdateSuccessfully() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubTaskRef existingRef = createTestFubTaskRef();
    FubTask updatedFubTask = createTestFubTask();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTaskRefDao.selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()))).thenReturn(existingRef);
    when(fubQueryManager.getFubPersonId(leadTask.getLeadId())).thenReturn(456L);
    when(fubQueryManager.getFubUserId(leadTask.getAssignToUid(), CALL_TOKEN)).thenReturn(789L);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubClient.updateTask(eq(CALL_TOKEN), eq(existingRef.getFubTaskId()),
        any(FubTask.class)))
        .thenReturn(updatedFubTask);

    // Act
    syncUpdateToFubTaskService.handle(leadTask);

    // Assert
    verify(fubTaskRefDao).selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()));
    verify(fubClient).updateTask(eq(CALL_TOKEN), eq(existingRef.getFubTaskId()),
        any(FubTaskRequest.class));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handUpdateTask_WithNoExistingMapping_ShouldLogAndReturn() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubQueryManager.getFubPersonId(leadTask.getLeadId())).thenReturn(456L);
    when(fubQueryManager.getFubUserId(leadTask.getAssignToUid(), CALL_TOKEN)).thenReturn(789L);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(TOKEN);
    when(fubTaskRefDao.selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()))).thenReturn(null);

    // Act
    syncUpdateToFubTaskService.handle(leadTask);

    // Assert
    verify(fubTaskRefDao).selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()));
    verifyNoInteractions(fubClient);
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handDeleteTask_WithValidData_ShouldDeleteSuccessfully() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubTaskRef existingRef = createTestFubTaskRef();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTaskRefDao.selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()))).thenReturn(existingRef);
    when(fubTokenService.getTokenWithRefresh(ownership)).thenReturn(TOKEN);
    // Note: Delete operations should not require FUB person/user ID mocking since conversion is
    // skipped

    // Act
    syncDeleteToFubTaskService.handle(leadTask);

    // Assert
    verify(fubTaskRefDao).selectByTeamIdAndTaskId(eq(lead.getTeamId()), eq(leadTask.getId()));
    verify(fubClient).deleteTask(eq(CALL_TOKEN), eq(existingRef.getFubTaskId()));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handCreateTask_WithNoOwnership_ShouldLogFailureAndReturn() {
    // Arrange
    LeadTask leadTask = createTestLeadTask();
    LeadBo lead = createTestLead();

    when(leadManager.getLeadById(leadTask.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(null);

    // Act
    syncCreateToFubTaskService.handle(leadTask);

    // Assert
    verify(leadManager).getOwnershipFromLead(lead);
    verifyNoInteractions(fubTaskRefDao);
  }

  // Helper methods to create test data
  private LeadTask createTestLeadTask() {
    LeadTask leadTask = new LeadTask();
    leadTask.setId(12345L);
    leadTask.setLeadId(456L);
    leadTask.setAssignToUid(789L);
    leadTask.setTeamId(123L);
    leadTask.setContent("Test Task Content");
    leadTask.setWay(LeadTask.LeadTaskWayEnum.CALL.getCode()); // Supported task type
    leadTask.setPipelineId(111L);
    leadTask.setCreaterId(999L);
    leadTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
    leadTask.setLastUpdate(new Timestamp(System.currentTimeMillis()));
    leadTask.setDeadline(System.currentTimeMillis() + 3600000); // 1 hour later
    return leadTask;
  }

  private LeadBo createTestLead() {
    LeadBo lead = new LeadBo();
    lead.setId(456L);
    lead.setTeamId(999L);
    return lead;
  }

  private OwnershipInfo createTestOwnership() {
    return OwnershipInfo.builder()
        .ownershipScope(OwnershipScope.TEAM)
        .ownershipId(999L)
        .build();
  }

  private FubTask createTestFubTask() {
    FubTask fubTask = new FubTask();
    fubTask.setId(123L);
    fubTask.setName("Test Task Content");
    fubTask.setAssignedUserId(789L);
    fubTask.setType("call");
    fubTask.setPersonId(456L);
    fubTask.setDueDateTime("2024-01-01T12:00:00 -05:00");
    return fubTask;
  }

  private FubTaskRef createTestFubTaskRef() {
    return FubTaskRef.builder()
        .id(1L)
        .taskId(12345L)
        .leadId(456L)
        .fubTaskId(123L)
        .teamId(999L)
        .createTime(new Timestamp(System.currentTimeMillis()))
        .updateTime(new Timestamp(System.currentTimeMillis()))
        .build();
  }
}

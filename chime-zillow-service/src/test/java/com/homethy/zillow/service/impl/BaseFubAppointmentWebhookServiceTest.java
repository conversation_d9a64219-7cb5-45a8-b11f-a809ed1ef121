package com.homethy.zillow.service.impl;

import com.homethy.fub.dao.FubImportLogDao;
import com.homethy.fub.service.FubAppointmentRefService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.agent.model.AgentEsBo;
import com.homethy.microservice.client.leadtask.AppointmentClientService;
import com.homethy.zillow.client.AgentSearchManager;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubStateInfo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.homethy.zillow.model.constant.FubImportTypeEnum.APPOINTMENTS_CREATED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BaseFubAppointmentWebhookServiceTest {
    static class Impl extends BaseFubAppointmentWebhookService<Object, Object> {
        @Override
        void handleWebhookEvent(FubImportMsg fubRecord, List<Long> fubAppointmentIds, Map<Long, FubAppointmentRef> refMap) {

        }
        @Override
        public Object handle(FubImportMsg fubRecord, Object input) { return null; }
        @Override
        public Object getInput(FubImportLog log) { return null; }
        @Override
        public com.homethy.zillow.model.constant.FubImportTypeEnum getImportType() { return null; }
        public void callRemoveEmails(List<String> emails, Map<Long, String> idEmailMap) { removeEmails(emails, idEmailMap); }
    }

    @InjectMocks
    private Impl service;
    @Mock private FubImportLogDao fubImportLogDao;
    @Mock private FubTokenService fubTokenService;
    @Mock private FubClient fubClient;
    @Mock private com.homethy.fub.dao.FubLeadDao fubLeadDao;
    @Mock private UserManager userManager;
    @Mock private AgentSearchManager agentSearchManager;
    @Mock private FubAppointmentRefService fubAppointmentRefService;
    @Mock private AppointmentClientService appointmentClientService;

    @Test
    void testRemoveEmails() {
        List<String> emails = new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>"));
        Map<Long, String> idEmailMap = Map.of(1L, "<EMAIL>");
        service.callRemoveEmails(emails, idEmailMap);
        assertEquals(1, emails.size());
        assertFalse(emails.contains("<EMAIL>"));
    }

    @Test
    void testPutUsers() {
        Map<String, List<String>> participants = new HashMap<>();
        FubImportMsg fubRecord = mock(FubImportMsg.class);
        FubStateInfo stateInfo = mock(FubStateInfo.class);
        when(fubRecord.getStateInfo()).thenReturn(stateInfo);
        when(stateInfo.getTeamId()).thenReturn(1L);
        Map<String, String> userIdEmailMap = new HashMap<>();
        userIdEmailMap.put("100", "<EMAIL>");
        List<AgentEsBo> agents = new ArrayList<>();
        AgentEsBo agent = mock(AgentEsBo.class);
        when(agent.getUserId()).thenReturn(100L);
        when(agent.getAccount()).thenReturn("<EMAIL>");
        agents.add(agent);
        when(agentSearchManager.searchByEmails(eq(1L), anyList())).thenReturn(agents);
        when(userManager.getLenders(eq(1L), anyList())).thenReturn(Collections.emptyList());
        when(userManager.getVendors(eq(1L), anyList())).thenReturn(Collections.emptyList());
        service.putUsers(participants, fubRecord, userIdEmailMap);
        assertTrue(participants.containsKey(FubAppointmentServiceImpl.AGENT));
        assertEquals(List.of("100"), participants.get(FubAppointmentServiceImpl.AGENT));
    }

    @Test
    void testSaveImportLog() {
        FubImportMsg fubRecord = mock(FubImportMsg.class);
        FubStateInfo stateInfo = mock(FubStateInfo.class);
        when(fubRecord.getStateInfo()).thenReturn(stateInfo);
        when(stateInfo.getOwnershipScope()).thenReturn(com.homethy.zillow.model.constant.OwnershipScope.TEAM);
        when(stateInfo.getOwnershipId()).thenReturn(1L);
        when(stateInfo.getBindingAgentId()).thenReturn(2L);

        ArgumentCaptor<FubImportLog> logCaptor = ArgumentCaptor.forClass(FubImportLog.class);

        service.saveImportLog("input", fubRecord,
            APPOINTMENTS_CREATED,
            null, com.homethy.zillow.model.constant.fub.SyncStatus.SUCCESS);

        verify(fubImportLogDao).insert(logCaptor.capture());

        FubImportLog capturedLog = logCaptor.getValue();
        assertNotNull(capturedLog);
        assertEquals("TEAM", capturedLog.getOwnershipScope());
        assertEquals(1L, capturedLog.getOwnershipId());
        assertEquals(2L, capturedLog.getImportUserId());
        assertEquals(APPOINTMENTS_CREATED, capturedLog.getType());
        assertEquals(0L, capturedLog.getLoftyId()); // 因为result为null
        assertEquals(com.homethy.zillow.model.constant.fub.SyncStatus.SUCCESS.getStatus(), capturedLog.getStatus());
        assertEquals("SUCCESS", capturedLog.getResult());
        assertTrue(capturedLog.getImportContent().contains("input"));
    }

    @Test
    void testGetFubContext() {
        FubImportLog log = mock(FubImportLog.class);
        when(log.getImportContent()).thenReturn("{\"context\":{\"foo\":\"bar\"}}\n");
        FubImportMsg msg = service.getFubContext(log);
        assertNotNull(msg);
    }
} 
package com.homethy.zillow.service.impl;

import com.homethy.fub.dao.FubAppointmentRefDao;
import com.homethy.fub.dao.FubNotifyLogDao;
import com.homethy.fub.service.FubAppointmentRefService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.model.Appointment;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.client.LeadManager;
import com.homethy.zillow.manager.FubQueryManager;
import com.homethy.zillow.manager.UserManager;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubAppointment;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubLead;
import com.homethy.zillow.model.constant.fub.FubNotifyLog;
import com.homethy.zillow.model.constant.fub.FubToken;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.team.vendor.Vendor;
import com.homethy.microservice.exception.HomethyBusinessException;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FubAppointmentServiceImplTest {

  @Spy
  @InjectMocks
  private FubAppointmentServiceImpl zillowAppointmentService;

  @Mock
  private FubClient fubClient;

  @Mock
  private FubAppointmentRefDao fubAppointmentRefDao;

  @Mock
  private FubNotifyLogDao fubNotifyLogDao;

  @Mock
  private FubTokenService fubTokenService;

  @Mock
  private LeadManager leadManager;

  @Mock
  private FubQueryManager fubQueryManager;

  @Mock
  private UserManager userManager;

  @Mock
  private FubAppointmentRefService fubAppointmentRefService;

  private static final String CALL_TOKEN = "test-token";
  private static final FubToken TOKEN = new FubToken();

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void handCreateAppointment_WithEmptyParticipants_ShouldExitEarlyWithoutLogging() {
    // Arrange
    Appointment appointment = createTestAppointment();
    appointment.setParticipants(new HashMap<>()); // Empty participants will cause conversion to
    // return null
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(123L);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(leadManager).getLeadById(appointment.getLeadId());
    verify(leadManager).getOwnershipFromLead(lead);
    verify(fubTokenService).getCallToken(ownership);
    verify(fubQueryManager).getFubUserId(appointment.getCreaterId(), CALL_TOKEN);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubAppointmentRefDao);
    verify(fubNotifyLogDao, never()).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handCreateAppointment_WithNoLead_ShouldLogFailureAndReturn() {
    // Arrange
    Appointment appointment = createTestAppointment();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(null);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(leadManager).getLeadById(appointment.getLeadId());
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubAppointmentRefDao);
  }

  @Test
  public void handCreateAppointment_WithNoToken_ShouldLogFailureAndReturn() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(null);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubTokenService).getCallToken(ownership);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubAppointmentRefDao);
  }

  @Test
  public void handCreateAppointment_WithNoFubUserId_ShouldReturnEarlyWithoutLogging() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(0L); //
    // Return 0 to trigger early return

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubQueryManager).getFubUserId(appointment.getCreaterId(), CALL_TOKEN);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubAppointmentRefDao);
    verify(fubNotifyLogDao, never()).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handDeleteAppointment_WithNoExistingRef_ShouldLogAndReturn() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubAppointmentRefDao.selectByTeamIdAndAppId(eq(lead.getTeamId()),
        eq(appointment.getId()))).thenReturn(null);

    // Act
    zillowAppointmentService.handDeleteAppointment(appointment);

    // Assert
    verify(fubAppointmentRefDao).selectByTeamIdAndAppId(eq(lead.getTeamId()),
        eq(appointment.getId()));
    verifyNoInteractions(fubClient);
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }


  @Test
  public void handDeleteAppointment_WithValidData_ShouldDeleteSuccessfully() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubAppointmentRef existingRef = createTestFubAppointmentRef();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubAppointmentRefDao.selectByTeamIdAndAppId(eq(lead.getTeamId()),
        eq(appointment.getId()))).thenReturn(existingRef);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);

    // Act
    zillowAppointmentService.handDeleteAppointment(appointment);

    // Assert
    verify(fubAppointmentRefDao).selectByTeamIdAndAppId(eq(lead.getTeamId()),
        eq(appointment.getId()));
    verify(fubClient).deleteTask(eq(CALL_TOKEN), eq(existingRef.getFubAppId()));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void handCreateAppointment_WithNoFubUserId_ShouldNotCallFubClient() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(0L);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubQueryManager).getFubUserId(appointment.getCreaterId(), CALL_TOKEN);
    verifyNoInteractions(fubClient);
    verifyNoInteractions(fubAppointmentRefDao);
    verify(fubNotifyLogDao, never()).insert(any(FubNotifyLog.class));
  }


  @Test
  public void getLeadId_WithNullInput_ShouldReturnZero() {
    // Act
    long result = FubAppointmentServiceImpl.getLeadId(null);

    // Assert
    assertEquals(0L, result);
  }

  @Test
  void mergeInvitees_shouldMergeOnlyNewInvitees() {
    Map<String, Object> orig1 = new HashMap<>();
    orig1.put("userId", "1");
    orig1.put("name", "Tom Customer");
    Map<String, Object> orig2 = new HashMap<>();
    orig2.put("personId", "123");
    orig2.put("name", "John Agent");
    List<Object> origInvitees = Arrays.asList(orig1, orig2);

    Map<String, Object> new1 = new HashMap<>();
    new1.put("userId", 1);
    new1.put("name", "Tom Customer");
    Map<String, Object> new2 = new HashMap<>();
    new2.put("personId", "123");
    new2.put("name", "John Agent");
    Map<String, Object> new3 = new HashMap<>();
    new3.put("userId", "2");
    new3.put("name", "Jerry Customer");
    Map<String, Object> new4 = new HashMap<>();
    new4.put("personId", "456");
    new4.put("name", "Alice Agent");
    List<Object> newInvitees = Arrays.asList(new1, new2, new3, new4);

    FubAppointment fubObj = new FubAppointment();
    fubObj.setInvitees(origInvitees);
    FubAppointment fubAppointment = new FubAppointment();
    fubAppointment.setInvitees(newInvitees);

    zillowAppointmentService.mergeInvitees(fubObj, fubAppointment);
    List<Object> merged = fubAppointment.getInvitees();
    assertEquals(4, merged.size());
    boolean hasUser2 =
        merged.stream().anyMatch(i -> (i instanceof Map) && "2".equals(((Map) i).get("userId")));
    boolean hasPerson456 =
        merged.stream().anyMatch(i -> (i instanceof Map) && "456".equals(((Map) i).get("personId")));
    assertEquals(true, hasUser2);
    assertEquals(true, hasPerson456);
  }

  @Test
  void mergeInvitees_newInviteesEmpty_shouldCopyOrigInvitees() {
    Map<String, Object> orig1 = new HashMap<>();
    orig1.put("userId", 1);
    orig1.put("name", "Tom Customer");
    List<Object> origInvitees = Arrays.asList(orig1);
    FubAppointment fubObj = new FubAppointment();
    fubObj.setInvitees(origInvitees);
    FubAppointment fubAppointment = new FubAppointment();
    fubAppointment.setInvitees(Arrays.asList());
    zillowAppointmentService.mergeInvitees(fubObj, fubAppointment);
    List<Object> merged = fubAppointment.getInvitees();
    assertEquals(1, merged.size());
    assertEquals("1", ((Map) merged.get(0)).get("userId") + "");
  }

  @Test
  void mergeInvitees_origInviteesEmpty_shouldReturnEarly() {
    FubAppointment fubObj = new FubAppointment();
    fubObj.setInvitees(Arrays.asList());
    FubAppointment fubAppointment = new FubAppointment();
    fubAppointment.setInvitees(Arrays.asList(
        new HashMap<String, Object>() {{
          put("userId", 2);
          put("name", "Jerry Customer");
        }}
    ));
    zillowAppointmentService.mergeInvitees(fubObj, fubAppointment);
    // mergeInvitees should not change fubAppointment's invitees
    assertEquals(1, fubAppointment.getInvitees().size());
    assertEquals("2", ((Map) fubAppointment.getInvitees().get(0)).get("userId") + "");
  }


  // ========== Convert Method Tests (via handCreateAppointment) ==========

  @Test
  public void handCreateAppointment_WithValidConversion_ShouldCreateSuccessfully() {
    // Arrange
    Appointment appointment = createTestAppointment();
    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubAppointment createdFubAppointment = createTestFubAppointment();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(123L);
    when(fubQueryManager.getFubLeadsByLeadIds(any())).thenReturn(Map.of(456L, createTestFubLead(), 999L, createTestFubLead()));
    when(fubClient.createAppointment(eq(CALL_TOKEN), any(FubAppointment.class)))
        .thenReturn(createdFubAppointment);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubQueryManager).getFubUserId(appointment.getCreaterId(), CALL_TOKEN);
    verify(fubQueryManager).getFubLeadsByLeadIds(any());
    verify(fubClient).createAppointment(eq(CALL_TOKEN), any(FubAppointment.class));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  // ========== ConvertInvitees Related Tests ==========

  @Test
  public void handCreateAppointment_WithDifferentParticipantTypes_ShouldProcessAllTypes() {
    // Arrange
    Appointment appointment = createTestAppointment();
    // Add different participant types
    Map<String, List<String>> participants = new HashMap<>();
    participants.put("lead", Arrays.asList("456"));
    participants.put("lead_member", Arrays.asList("lead_member_999_suffix"));
    participants.put("agent", Arrays.asList("789"));
    participants.put("lender", Arrays.asList("890"));
    participants.put("vendor", Arrays.asList("901"));
    appointment.setParticipants(participants);

    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubAppointment createdFubAppointment = createTestFubAppointment();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(123L);
    when(fubQueryManager.getFubLeadsByLeadIds(any())).thenReturn(Map.of(456L, createTestFubLead(), 999L, createTestFubLead()));
    when(userManager.getUserMap(any())).thenReturn(createTestUserMap());
    when(userManager.getVendorMap(any())).thenReturn(createTestVendorMap());
    when(fubQueryManager.queryFubUser(eq(CALL_TOKEN), any())).thenReturn(createTestFubUserInfo());
    when(fubClient.createAppointment(eq(CALL_TOKEN), any(FubAppointment.class)))
        .thenReturn(createdFubAppointment);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubQueryManager).getFubLeadsByLeadIds(any());
    verify(userManager).getUserMap(any());
    verify(userManager).getVendorMap(any());
    verify(fubQueryManager, atLeast(1)).queryFubUser(eq(CALL_TOKEN), any());
    verify(fubClient).createAppointment(eq(CALL_TOKEN), any(FubAppointment.class));
  }


  // ========== Simple Validation Tests ==========

  @Test
  public void handCreateAppointment_WithMinimalValidData_ShouldWork() {
    // Arrange
    Appointment appointment = new Appointment();
    appointment.setId(12345L);
    appointment.setLeadId(456L);
    appointment.setCreaterId(789L);
    appointment.setDescr("Test");
    appointment.setAddress("Test Address");
    appointment.setAllDay(false);
    appointment.setCreateTime(new Date());
    appointment.setLastUpdate(new Date());
    appointment.setDeadline(System.currentTimeMillis() + 3600000);

    // Set simple participants that will result in non-empty invitees
    Map<String, List<String>> participants = new HashMap<>();
    participants.put("lead", Arrays.asList("456"));
    appointment.setParticipants(participants);

    LeadBo lead = createTestLead();
    OwnershipInfo ownership = createTestOwnership();
    FubAppointment createdFubAppointment = createTestFubAppointment();

    when(leadManager.getLeadById(appointment.getLeadId())).thenReturn(lead);
    when(leadManager.getOwnershipFromLead(lead)).thenReturn(ownership);
    when(fubTokenService.getTokenWithoutCallToken(ownership)).thenReturn(TOKEN);
    when(fubTokenService.getCallToken(ownership)).thenReturn(CALL_TOKEN);
    when(fubQueryManager.getFubUserId(appointment.getCreaterId(), CALL_TOKEN)).thenReturn(123L);
    when(fubQueryManager.getFubLeadsByLeadIds(any())).thenReturn(Map.of(456L, createTestFubLead()));
    when(fubClient.createAppointment(eq(CALL_TOKEN), any(FubAppointment.class)))
        .thenReturn(createdFubAppointment);

    // Act
    zillowAppointmentService.handCreateAppointment(appointment);

    // Assert
    verify(fubClient).createAppointment(eq(CALL_TOKEN), any(FubAppointment.class));
    verify(fubAppointmentRefService).saveRelation(eq(appointment), any(FubAppointment.class));
    verify(fubNotifyLogDao).insert(any(FubNotifyLog.class));
  }

  @Test
  public void getLeadId_WithInvalidString_ShouldReturnZero() {
    // Act & Assert
    assertEquals(0L, FubAppointmentServiceImpl.getLeadId("no_numbers_here"));
    assertEquals(0L, FubAppointmentServiceImpl.getLeadId(""));
    assertEquals(0L, FubAppointmentServiceImpl.getLeadId("lead_member_"));
  }


  // Helper methods to create test data
  private Appointment createTestAppointment() {
    Appointment appointment = new Appointment();
    appointment.setId(12345L);
    appointment.setLeadId(456L);
    appointment.setCreaterId(789L);
    appointment.setDescr("Test Appointment");
    appointment.setAddress("123 Test Street");
    appointment.setAllDay(false);
    appointment.setPipelineId(111L);
    appointment.setCreateTime(new Date());
    appointment.setLastUpdate(new Date());
    appointment.setDeadline(System.currentTimeMillis() + 3600000); // 1 hour later

    Map<String, List<String>> participants = new HashMap<>();
    participants.put("lead", Arrays.asList("456"));
    participants.put("lead_member", Arrays.asList("lead_member_999_suffix"));
    appointment.setParticipants(participants);

    return appointment;
  }

  private LeadBo createTestLead() {
    LeadBo lead = new LeadBo();
    lead.setId(456L);
    lead.setTeamId(999L);
    return lead;
  }

  private OwnershipInfo createTestOwnership() {
    return OwnershipInfo.builder()
        .ownershipScope(OwnershipScope.TEAM)
        .ownershipId(999L)
        .build();
  }

  private FubAppointment createTestFubAppointment() {
    FubAppointment fubAppointment = new FubAppointment();
    fubAppointment.setId(123L);
    fubAppointment.setTitle("Test Appointment");
    fubAppointment.setDescription("Test Appointment");
    fubAppointment.setAllDay(false);
    fubAppointment.setLocation("123 Test Street");
    fubAppointment.setCreatedById(123L);
    fubAppointment.setInvitees(Arrays.asList(createAgentInvitee(), createLeadInvitee()));
    return fubAppointment;
  }

  private FubAppointmentRef createTestFubAppointmentRef() {
    return FubAppointmentRef.builder()
        .id(1L)
        .appId(12345L)
        .leadId(456L)
        .fubAppId(123L)
        .teamId(999L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }

  private Object createAgentInvitee() {
    Map<String, Object> agent = new HashMap<>();
    agent.put("personId", "123");
    agent.put("name", "John Agent");
    agent.put("email", "<EMAIL>");
    return agent;
  }

  private Object createLeadInvitee() {
    Map<String, Object> lead = new HashMap<>();
    lead.put("userId", 1);
    lead.put("name", "Tom Customer");
    return lead;
  }

  private FubLead createTestFubLead() {
    return FubLead.builder()
        .id(1L)
        .leadId(456L)
        .peopleId(123L)
        .fubStatus(1)
        .fubStageId(1)
        .fubStageName("New")
        .ownershipId(999L)
        .teamId(999L)
        .fubTeamId(888L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }

  private Map<Long, User> createTestUserMap() {
    User user = new User();
    user.setId(789L);
    user.setAccount("<EMAIL>");
    // User class may not have setName method, using available fields
    return Map.of(789L, user);
  }

  private Map<Long, Vendor> createTestVendorMap() {
    Vendor vendor = new Vendor();
    vendor.setId(901L);
    vendor.setEmail("<EMAIL>");
    // Vendor class may not have setName method, using available fields
    return Map.of(901L, vendor);
  }

  private FubUserInfo createTestFubUserInfo() {
    FubUserInfo fubUserInfo = new FubUserInfo();
    fubUserInfo.setId(123L);
    fubUserInfo.setEmail("<EMAIL>");
    fubUserInfo.setName("Test User");
    fubUserInfo.setRole("agent");
    return fubUserInfo;
  }
}
package com.homethy.zillow.service;

import com.homethy.microservice.client.bo.p.User;
import com.homethy.zillow.client.FubClientManager;
import com.homethy.zillow.configuration.ZillowConfig;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubConnect;
import com.homethy.fub.service.impl.FubTokenServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * JUnit test for FubTokenServiceImpl.getConnect()
 * Input: {"ownershipScope":"PERSONAL","ownershipId":502583629718835}, 502583629718835
 */

@ExtendWith(MockitoExtension.class)
class FubTokenServiceImplTest {

    @Spy
    @InjectMocks
    private FubTokenServiceImpl fubTokenService;

    @Mock
    private FubClientManager fubClientManager;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void getConnect_WithPersonalOwnership_ShouldReturnValidUrl() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(502583629718835L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);
            System.out.println(result);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            assertTrue(url.contains("https://app.followupboss.com/oauth/authorize"), 
                    "URL should contain the correct authorization endpoint");
            assertTrue(url.contains("response_type=auth_code"), 
                    "URL should contain response_type parameter");
            assertTrue(url.contains("client_id=test_client_id"), 
                    "URL should contain client_id parameter");
            assertTrue(url.contains("redirect_uri=https://test.example.com/callback"), 
                    "URL should contain redirect_uri parameter");
            assertTrue(url.contains("prompt=login"), 
                    "URL should contain prompt parameter");

            // Verify state parameter
            String stateParam = extractStateFromUrl(url);
            assertNotNull(stateParam, "state parameter should not be null");
            
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            assertTrue(decodedState.contains("\"ownershipId\":502583629718835"), 
                    "state should contain the correct ownershipId");
            assertTrue(decodedState.contains("\"ownershipScope\":\"PERSONAL\""), 
                    "state should contain the correct ownershipScope");
            assertTrue(decodedState.contains("\"operUserId\":502583629718835"), 
                    "state should contain the correct operUserId");
            assertTrue(decodedState.contains("\"teamId\":123456789"), 
                    "state should contain the correct teamId");

            // Verify ChimeZillowConfigCenter calls
            mockedConfigCenter.verify(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""));
            mockedConfigCenter.verify(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""));
        }
    }

    @Test
    public void getConnect_WithOfficeOwnership_ShouldReturnValidUrl() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.OFFICE)
                .ownershipId(123456789L)
                .build();
        User user = new User();
        user.setId(987654321L);
        user.setTeamId(555666777L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            String stateParam = extractStateFromUrl(url);
            assertNotNull(stateParam, "state parameter should not be null");
            
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            assertTrue(decodedState.contains("\"ownershipId\":123456789"), 
                    "state should contain the correct ownershipId");
            assertTrue(decodedState.contains("\"ownershipScope\":\"OFFICE\""), 
                    "state should contain the correct ownershipScope");
            assertTrue(decodedState.contains("\"operUserId\":987654321"), 
                    "state should contain the correct operUserId");
            assertTrue(decodedState.contains("\"teamId\":555666777"), 
                    "state should contain the correct teamId");
        }
    }

    @Test
    public void getConnect_WithTeamOwnership_ShouldReturnValidUrl() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.TEAM)
                .ownershipId(555666777L)
                .build();
        User user = new User();
        user.setId(111222333L);
        user.setTeamId(999888777L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            String stateParam = extractStateFromUrl(url);
            assertNotNull(stateParam, "state parameter should not be null");
            
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            assertTrue(decodedState.contains("\"ownershipId\":555666777"), 
                    "state should contain the correct ownershipId");
            assertTrue(decodedState.contains("\"ownershipScope\":\"TEAM\""), 
                    "state should contain the correct ownershipScope");
            assertTrue(decodedState.contains("\"operUserId\":111222333"), 
                    "state should contain the correct operUserId");
            assertTrue(decodedState.contains("\"teamId\":999888777"), 
                    "state should contain the correct teamId");
        }
    }

    @Test
    public void getConnect_WithNullOwnershipInfo_ShouldReturnUrlWithNullState() {
        // Arrange
        User user = new User();
        user.setId(123L);
        user.setTeamId(456L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(null, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            assertTrue(url.contains("https://app.followupboss.com/oauth/authorize"), 
                    "URL should contain the correct authorization endpoint");
            assertTrue(url.contains("response_type=auth_code"), 
                    "URL should contain response_type parameter");
            assertTrue(url.contains("client_id=test_client_id"), 
                    "URL should contain client_id parameter");
            assertTrue(url.contains("redirect_uri=https://test.example.com/callback"), 
                    "URL should contain redirect_uri parameter");
            assertTrue(url.contains("prompt=login"), 
                    "URL should contain prompt parameter");
            assertTrue(url.contains("state=null"), 
                    "URL should contain state=null when ownershipInfo is null");
        }
    }

    @Test
    public void getConnect_WithInvalidUserData_ShouldReturnUrlWithNullState() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(0L); // Invalid userId (<= 0)
        user.setTeamId(0L); // Invalid teamId (<= 0)

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            assertTrue(url.contains("state=null"), 
                    "URL should contain state=null when user data is invalid");
        }
    }

    @Test
    public void getConnect_WithZeroUserId_ShouldReturnUrlWithNullState() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(0L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            assertTrue(url.contains("state=null"), 
                    "URL should contain state=null when userId is 0");
        }
    }

    @Test
    public void getConnect_WithEmptyConfigValues_ShouldReturnUrlWithEmptyValues() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(502583629718835L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods with empty values
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            assertNotNull(result, "FubConnect result should not be null");
            assertNotNull(result.getUrl(), "URL should not be null");
            
            String url = result.getUrl();
            assertTrue(url.contains("client_id="), "URL should contain client_id parameter");
            assertTrue(url.contains("redirect_uri="), "URL should contain redirect_uri parameter");
            
            // Verify state parameter still works correctly
            String stateParam = extractStateFromUrl(url);
            assertNotNull(stateParam, "state parameter should not be null");
            
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            assertTrue(decodedState.contains("\"ownershipId\":502583629718835"), 
                    "state should contain the correct ownershipId");
            assertTrue(decodedState.contains("\"teamId\":123456789"), 
                    "state should contain the correct teamId");
        }
    }

    @Test
    public void getConnect_StateEncoding_ShouldBeValidBase64() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(502583629718835L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            String url = result.getUrl();
            String stateParam = extractStateFromUrl(url);
            
            // Verify state is valid Base64
            assertDoesNotThrow(() -> {
                Base64.getDecoder().decode(stateParam);
            }, "state parameter should be valid Base64");

            // Verify decoded content is valid JSON
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            assertTrue(decodedState.startsWith("{"), "Decoded state should be JSON format");
            assertTrue(decodedState.endsWith("}"), "Decoded state should be JSON format");
        }
    }

    @Test
    public void getConnect_ExpectedStateValue_ShouldMatchExactly() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(502583629718835L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            String url = result.getUrl();
            String stateParam = extractStateFromUrl(url);
            
            // Decode the actual state parameter
            String decodedState = new String(Base64.getDecoder().decode(stateParam), StandardCharsets.UTF_8);
            
            // Verify the decoded JSON contains all expected fields with correct values
            assertTrue(decodedState.contains("\"ownershipScope\":\"PERSONAL\""), 
                    "state should contain the correct ownershipScope");
            assertTrue(decodedState.contains("\"ownershipId\":502583629718835"), 
                    "state should contain the correct ownershipId");
            assertTrue(decodedState.contains("\"operUserId\":502583629718835"), 
                    "state should contain the correct operUserId");
            assertTrue(decodedState.contains("\"teamId\":123456789"), 
                    "state should contain the correct teamId");
            
            // Verify the JSON structure is valid
            assertTrue(decodedState.startsWith("{"), "Decoded state should be JSON format");
            assertTrue(decodedState.endsWith("}"), "Decoded state should be JSON format");
        }
    }

    @Test
    public void getConnect_UrlStructure_ShouldBeComplete() {
        // Arrange
        OwnershipInfo ownershipInfo = OwnershipInfo.builder()
                .ownershipScope(OwnershipScope.PERSONAL)
                .ownershipId(502583629718835L)
                .build();
        User user = new User();
        user.setId(502583629718835L);
        user.setTeamId(123456789L);

        // Mock ChimeZillowConfigCenter static methods
        try (MockedStatic<ZillowConfig> mockedConfigCenter = mockStatic(ZillowConfig.class)) {
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.clientId", ""))
                    .thenReturn("test_client_id");
            mockedConfigCenter.when(() -> ZillowConfig.getProperties("chime-zillow.fub.redirect_url", ""))
                    .thenReturn("https://test.example.com/callback");

            // Act
            FubConnect result = fubTokenService.getConnect(ownershipInfo, user);

            // Assert
            String url = result.getUrl();
            
            // Verify URL contains all necessary parameters
            assertTrue(url.contains("response_type=auth_code"), "URL should contain response_type parameter");
            assertTrue(url.contains("client_id="), "URL should contain client_id parameter");
            assertTrue(url.contains("redirect_uri="), "URL should contain redirect_uri parameter");
            assertTrue(url.contains("state="), "URL should contain state parameter");
            assertTrue(url.contains("prompt=login"), "URL should contain prompt parameter");
            
            // Verify parameter format
            String[] params = url.split("\\?")[1].split("&");
            assertTrue(params.length >= 5, "URL should contain at least 5 parameters");
            
            // Verify each parameter has a value
            for (String param : params) {
                assertTrue(param.contains("="), "Each parameter should contain '='");
                String[] keyValue = param.split("=");
                assertEquals(2, keyValue.length, "Parameter format should be key=value");
                assertNotNull(keyValue[1], "Parameter value should not be null");
            }
        }
    }

    /**
     * Extract state parameter from URL
     */
    private String extractStateFromUrl(String url) {
        if (url == null || !url.contains("state=")) {
            return null;
        }
        
        String[] parts = url.split("state=");
        if (parts.length < 2) {
            return null;
        }
        
        String statePart = parts[1];
        // If there are other parameters after state, remove them
        if (statePart.contains("&")) {
            statePart = statePart.split("&")[0];
        }
        
        return statePart;
    }
} 
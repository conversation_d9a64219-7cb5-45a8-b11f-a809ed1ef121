package com.homethy.zillow.service.impl;

import com.homethy.fub.dao.FubNotifyLogDao;
import com.homethy.fub.dao.FubTaskRefDao;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.model.LeadTask;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.client.LeadManager;
import com.homethy.zillow.manager.FubQueryManager;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubTask;
import com.homethy.zillow.model.constant.fub.FubTaskRef;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class BaseSyncToFubLeadTaskServiceTest {

  @InjectMocks
  private BaseSyncToFubLeadTaskService fubLeadTaskService;

  @Mock
  private FubClient fubClient;

  @Mock
  private FubTaskRefDao fubTaskRefDao;

  @Mock
  private FubNotifyLogDao fubNotifyLogDao;

  @Mock
  private FubTokenService fubTokenService;

  @Mock
  private LeadManager leadManager;

  @Mock
  private FubQueryManager fubQueryManager;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  // ========== saveRelation Method Tests ==========

  @Test
  public void saveRelation_WithValidData_ShouldSaveSuccessfully() {
    // Arrange
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 888L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(task.getId(), savedRef.getId());
    assertEquals(task.getId(), savedRef.getTaskId());
    assertEquals(task.getLeadId(), savedRef.getLeadId());
    assertEquals(fubTask.getId(), savedRef.getFubTaskId());
    assertEquals(fubTeamId, savedRef.getFubTeamId());
    assertEquals(task.getTeamId(), savedRef.getTeamId());
    assertEquals(ownershipInfo.getOwnershipId(), savedRef.getOwnershipId());
    assertEquals(ownershipInfo.getOwnershipScope(), savedRef.getOwnershipScope());
    assertEquals(task.getCreateTime(), savedRef.getCreateTime());
    assertEquals(task.getLastUpdate(), savedRef.getUpdateTime());
  }

  @Test
  public void saveRelation_WithNullTask_ShouldThrowException() {
    // Arrange
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 888L;

    // Act & Assert
    assertThrows(NullPointerException.class, () -> {
      fubLeadTaskService.saveRelation(null, fubTask, ownershipInfo, fubTeamId);
    });
  }

  @Test
  public void saveRelation_WithNullFubTask_ShouldThrowException() {
    // Arrange
    LeadTask task = createTestLeadTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 888L;

    // Act & Assert
    assertThrows(NullPointerException.class, () -> {
      fubLeadTaskService.saveRelation(task, null, ownershipInfo, fubTeamId);
    });
  }

  @Test
  public void saveRelation_WithNullOwnershipInfo_ShouldThrowException() {
    // Arrange
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    long fubTeamId = 888L;

    // Act & Assert
    assertThrows(NullPointerException.class, () -> {
      fubLeadTaskService.saveRelation(task, fubTask, null, fubTeamId);
    });
  }

  @Test
  public void saveRelation_WithZeroFubTeamId_ShouldSaveWithZeroValue() {
    // Arrange
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 0L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(0L, savedRef.getFubTeamId());
  }

  @Test
  public void saveRelation_WithNegativeFubTeamId_ShouldSaveWithNegativeValue() {
    // Arrange
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = -1L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(-1L, savedRef.getFubTeamId());
  }

  @Test
  public void saveRelation_WithDaoException_ShouldPropagateException() {
    // Arrange
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 888L;

    RuntimeException daoException = new RuntimeException("Database error");
    doThrow(daoException).when(fubTaskRefDao).insert(any(FubTaskRef.class));

    // Act & Assert
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);
    });

    assertEquals("Database error", thrown.getMessage());
    verify(fubTaskRefDao).insert(any(FubTaskRef.class));
  }

  @Test
  public void saveRelation_WithNullTimeFields_ShouldSaveWithNullValues() {
    // Arrange
    LeadTask task = createTestLeadTask();
    task.setCreateTime(null);
    task.setLastUpdate(null);
    
    FubTask fubTask = createTestFubTask();
    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 888L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(null, savedRef.getCreateTime());
    assertEquals(null, savedRef.getUpdateTime());
  }

  @Test
  public void saveRelation_WithDifferentOwnershipScopes_ShouldSaveCorrectly() {
    // Test with PERSONAL scope
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo personalOwnership = OwnershipInfo.builder()
        .ownershipId(111L)
        .ownershipScope(OwnershipScope.PERSONAL)
        .build();
    long fubTeamId = 888L;

    fubLeadTaskService.saveRelation(task, fubTask, personalOwnership, fubTeamId);

    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(OwnershipScope.PERSONAL, savedRef.getOwnershipScope());
    assertEquals(111L, savedRef.getOwnershipId());
  }

  @Test
  public void saveRelation_WithOfficeOwnershipScope_ShouldSaveCorrectly() {
    // Test with OFFICE scope
    LeadTask task = createTestLeadTask();
    FubTask fubTask = createTestFubTask();
    OwnershipInfo officeOwnership = OwnershipInfo.builder()
        .ownershipId(222L)
        .ownershipScope(OwnershipScope.OFFICE)
        .build();
    long fubTeamId = 888L;

    fubLeadTaskService.saveRelation(task, fubTask, officeOwnership, fubTeamId);

    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(OwnershipScope.OFFICE, savedRef.getOwnershipScope());
    assertEquals(222L, savedRef.getOwnershipId());
  }

  @Test
  public void saveRelation_WithZeroIds_ShouldSaveWithZeroValues() {
    // Arrange
    LeadTask task = createTestLeadTask();
    task.setId(0L);
    task.setLeadId(0L);
    task.setCreaterId(0L);

    FubTask fubTask = createTestFubTask();
    fubTask.setId(0L);

    OwnershipInfo ownershipInfo = OwnershipInfo.builder()
        .ownershipId(0L)
        .ownershipScope(OwnershipScope.TEAM)
        .build();
    long fubTeamId = 0L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(0L, savedRef.getId());
    assertEquals(0L, savedRef.getTaskId());
    assertEquals(0L, savedRef.getLeadId());
    assertEquals(0L, savedRef.getFubTaskId());
    assertEquals(0L, savedRef.getFubTeamId());
    assertEquals(0L, savedRef.getOwnershipId());
  }

  @Test
  public void saveRelation_WithMaxLongValues_ShouldSaveCorrectly() {
    // Arrange
    LeadTask task = createTestLeadTask();
    task.setId(Long.MAX_VALUE);
    task.setLeadId(Long.MAX_VALUE - 1);
    task.setTeamId(Long.MAX_VALUE - 2);

    FubTask fubTask = createTestFubTask();
    fubTask.setId(Long.MAX_VALUE - 3);

    OwnershipInfo ownershipInfo = OwnershipInfo.builder()
        .ownershipId(Long.MAX_VALUE - 4)
        .ownershipScope(OwnershipScope.TEAM)
        .build();
    long fubTeamId = Long.MAX_VALUE - 5;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(Long.MAX_VALUE, savedRef.getId());
    assertEquals(Long.MAX_VALUE, savedRef.getTaskId());
    assertEquals(Long.MAX_VALUE - 1, savedRef.getLeadId());
    assertEquals(Long.MAX_VALUE - 3, savedRef.getFubTaskId());
    assertEquals(Long.MAX_VALUE - 5, savedRef.getFubTeamId());
    assertEquals(Long.MAX_VALUE - 2, savedRef.getTeamId());
    assertEquals(Long.MAX_VALUE - 4, savedRef.getOwnershipId());
  }

  @Test
  public void saveRelation_WithMinLongValues_ShouldSaveCorrectly() {
    // Arrange
    LeadTask task = createTestLeadTask();
    task.setId(Long.MIN_VALUE);
    task.setLeadId(Long.MIN_VALUE + 1);
    task.setTeamId(Long.MIN_VALUE + 2);

    FubTask fubTask = createTestFubTask();
    fubTask.setId(Long.MIN_VALUE + 3);

    OwnershipInfo ownershipInfo = OwnershipInfo.builder()
        .ownershipId(Long.MIN_VALUE + 4)
        .ownershipScope(OwnershipScope.TEAM)
        .build();
    long fubTeamId = Long.MIN_VALUE + 5;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertEquals(Long.MIN_VALUE, savedRef.getId());
    assertEquals(Long.MIN_VALUE, savedRef.getTaskId());
    assertEquals(Long.MIN_VALUE + 1, savedRef.getLeadId());
    assertEquals(Long.MIN_VALUE + 3, savedRef.getFubTaskId());
    assertEquals(Long.MIN_VALUE + 5, savedRef.getFubTeamId());
    assertEquals(Long.MIN_VALUE + 2, savedRef.getTeamId());
    assertEquals(Long.MIN_VALUE + 4, savedRef.getOwnershipId());
  }

  @Test
  public void saveRelation_WithComplexScenario_ShouldLogCorrectly() {
    // This test verifies that the method logs the relationship correctly
    // We can't directly test the log output, but we can verify the method completes successfully

    LeadTask task = createTestLeadTask();
    task.setId(99999L);

    FubTask fubTask = createTestFubTask();
    fubTask.setId(88888L);

    OwnershipInfo ownershipInfo = createTestOwnershipInfo();
    long fubTeamId = 77777L;

    // Act
    fubLeadTaskService.saveRelation(task, fubTask, ownershipInfo, fubTeamId);

    // Assert
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao).insert(refCaptor.capture());

    FubTaskRef savedRef = refCaptor.getValue();
    assertNotNull(savedRef);
    assertEquals(99999L, savedRef.getTaskId());
    assertEquals(88888L, savedRef.getFubTaskId());
  }

  @Test
  public void saveRelation_MultipleCallsWithDifferentData_ShouldSaveEachCorrectly() {
    // Test multiple calls to ensure method is stateless

    // First call
    LeadTask task1 = createTestLeadTask();
    task1.setId(1111L);
    FubTask fubTask1 = createTestFubTask();
    fubTask1.setId(2222L);
    OwnershipInfo ownership1 = createTestOwnershipInfo();

    fubLeadTaskService.saveRelation(task1, fubTask1, ownership1, 3333L);

    // Second call with different data
    LeadTask task2 = createTestLeadTask();
    task2.setId(4444L);
    FubTask fubTask2 = createTestFubTask();
    fubTask2.setId(5555L);
    OwnershipInfo ownership2 = OwnershipInfo.builder()
        .ownershipId(6666L)
        .ownershipScope(OwnershipScope.PERSONAL)
        .build();

    fubLeadTaskService.saveRelation(task2, fubTask2, ownership2, 7777L);

    // Assert both calls were made
    ArgumentCaptor<FubTaskRef> refCaptor = ArgumentCaptor.forClass(FubTaskRef.class);
    verify(fubTaskRefDao, times(2)).insert(refCaptor.capture());

    List<FubTaskRef> savedRefs = refCaptor.getAllValues();
    assertEquals(2, savedRefs.size());

    // Verify first call
    assertEquals(1111L, savedRefs.get(0).getTaskId());
    assertEquals(2222L, savedRefs.get(0).getFubTaskId());
    assertEquals(3333L, savedRefs.get(0).getFubTeamId());

    // Verify second call
    assertEquals(4444L, savedRefs.get(1).getTaskId());
    assertEquals(5555L, savedRefs.get(1).getFubTaskId());
    assertEquals(7777L, savedRefs.get(1).getFubTeamId());
    assertEquals(OwnershipScope.PERSONAL, savedRefs.get(1).getOwnershipScope());
  }

  // ========== Helper Methods ==========

  private LeadTask createTestLeadTask() {
    LeadTask task = new LeadTask();
    task.setId(12345L);
    task.setLeadId(456L);
    task.setCreaterId(789L);
    task.setTeamId(123L);
    task.setContent("Test task content");
    task.setWay(LeadTask.LeadTaskWayEnum.CALL.getCode());
    task.setAssignToUid(999L);
    task.setDeadline(System.currentTimeMillis() + 3600000);
    task.setFinishFlag(false);
    task.setCreateTime(new Timestamp(System.currentTimeMillis()));
    task.setLastUpdate(new Timestamp(System.currentTimeMillis()));
    return task;
  }

  private FubTask createTestFubTask() {
    FubTask fubTask = new FubTask();
    fubTask.setId(123L);
    fubTask.setName("Test FUB task");
    fubTask.setPersonId(456L);
    fubTask.setAssignedUserId(789L);
    fubTask.setType("Call");
    fubTask.setIsCompleted(false);
    return fubTask;
  }

  private OwnershipInfo createTestOwnershipInfo() {
    return OwnershipInfo.builder()
        .ownershipId(999L)
        .ownershipScope(OwnershipScope.TEAM)
        .build();
  }
}

package com.homethy.zillow.service.impl;

import com.homethy.fub.dao.FubAppointmentRefDao;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubStateInfo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class FubAppointmentWebhookServiceFactoryTest {

  @InjectMocks
  private FubAppointmentWebhookServiceFactory factory;

  @Mock
  private FubAppointmentCreateWebhookService createWebhookService;

  @Mock
  private FubAppointmentUpdateWebhookService updateWebhookService;

  @Mock
  private FubAppointmentDeleteWebhookService deleteWebhookService;

  @Mock
  private FubAppointmentRefDao fubAppointmentRefDao;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  // ========== getService Method Tests (via handWebhookEvent) ==========
  // Note: getService is private, so we test it indirectly through handWebhookEvent

  @Test
  public void handWebhookEvent_WithDifferentServiceTypes_ShouldCallCorrectServices() {
    // This test verifies the getService method indirectly by testing different webhook types
    List<FubAppointmentRef> refs = Arrays.asList(createTestFubAppointmentRef());
    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Test appointmentsCreated
    FubImportMsg createMsg = createValidFubImportMsg();
    createMsg.setType("appointmentsCreated");
    factory.handWebhookEvent(createMsg);
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());

    // Test appointmentsUpdated
    FubImportMsg updateMsg = createValidFubImportMsg();
    updateMsg.setType("appointmentsUpdated");
    factory.handWebhookEvent(updateMsg);
    verify(updateWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());

    // Test appointmentsDeleted
    FubImportMsg deleteMsg = createValidFubImportMsg();
    deleteMsg.setType("appointmentsDeleted");
    factory.handWebhookEvent(deleteMsg);
    verify(deleteWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  // ========== handWebhookEvent Method Tests ==========

  @Test
  public void handWebhookEvent_WithValidData_ShouldProcessSuccessfully() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    List<FubAppointmentRef> refs = Arrays.asList(createTestFubAppointmentRef());

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  @Test
  public void handWebhookEvent_WithBlankData_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("");
    fubRecord.setType("appointmentsCreated");

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verifyNoInteractions(fubAppointmentRefDao);
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithNullData_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData(null);
    fubRecord.setType("appointmentsCreated");

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verifyNoInteractions(fubAppointmentRefDao);
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithInvalidJson_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("invalid json");
    fubRecord.setType("appointmentsCreated");
    fubRecord.setStateInfo(createTestFubStateInfo());

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verifyNoInteractions(fubAppointmentRefDao);
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithNullStateInfo_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("{\"resourceIds\":[\"123\",\"456\"]}");
    fubRecord.setType("appointmentsCreated");
    fubRecord.setStateInfo(null);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verifyNoInteractions(fubAppointmentRefDao);
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithMissingResourceIds_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("{\"otherField\":\"value\"}");
    fubRecord.setType("appointmentsCreated");
    fubRecord.setStateInfo(createTestFubStateInfo());

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verifyNoInteractions(fubAppointmentRefDao);
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithUnknownServiceType_ShouldReturnEarly() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    fubRecord.setType("unknownType");

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verifyNoInteractions(createWebhookService);
    verifyNoInteractions(updateWebhookService);
    verifyNoInteractions(deleteWebhookService);
  }

  @Test
  public void handWebhookEvent_WithUpdateType_ShouldCallUpdateService() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    fubRecord.setType("appointmentsUpdated");
    List<FubAppointmentRef> refs = Arrays.asList(createTestFubAppointmentRef());

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verify(updateWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
    verifyNoInteractions(createWebhookService);
    verifyNoInteractions(deleteWebhookService);
  }

  @Test
  public void handWebhookEvent_WithDeleteType_ShouldCallDeleteService() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    fubRecord.setType("appointmentsDeleted");
    List<FubAppointmentRef> refs = Arrays.asList(createTestFubAppointmentRef());

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verify(deleteWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
    verifyNoInteractions(createWebhookService);
    verifyNoInteractions(updateWebhookService);
  }


  @Test
  public void handWebhookEvent_WithMultipleResourceIds_ShouldProcessAll() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("{\"resourceIds\":[\"123\",\"456\",\"789\"]}");
    fubRecord.setType("appointmentsCreated");
    fubRecord.setStateInfo(createTestFubStateInfo());

    List<FubAppointmentRef> refs = Arrays.asList(
        createTestFubAppointmentRef(),
        createTestFubAppointmentRefWithId(2L, 789L)
    );

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(888L, Arrays.asList("123", "456", "789"));
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  @Test
  public void handWebhookEvent_WithEmptyRefsList_ShouldStillCallService() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    List<FubAppointmentRef> emptyRefs = Arrays.asList();

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(emptyRefs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  @Test
  public void handWebhookEvent_WithNullRefsList_ShouldStillCallService() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(null);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  @Test
  public void handWebhookEvent_WithDaoException_ShouldPropagateException() {
    // Arrange
    FubImportMsg fubRecord = createValidFubImportMsg();
    RuntimeException daoException = new RuntimeException("Database error");

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenThrow(daoException);

    // Act & Assert
    try {
      factory.handWebhookEvent(fubRecord);
    } catch (RuntimeException e) {
      assertEquals("Database error", e.getMessage());
    }

    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(any(Long.class), anyList());
    verifyNoInteractions(createWebhookService);
  }

  @Test
  public void handWebhookEvent_WithComplexJsonData_ShouldParseCorrectly() {
    // Arrange
    FubImportMsg fubRecord = new FubImportMsg();
    fubRecord.setData("{\"resourceIds\":[\"123\",\"456\"],\"otherField\":\"value\",\"nested\":{\"key\":\"value\"}}");
    fubRecord.setType("appointmentsCreated");
    fubRecord.setStateInfo(createTestFubStateInfo());

    List<FubAppointmentRef> refs = Arrays.asList(createTestFubAppointmentRef());

    when(fubAppointmentRefDao.selectByFubTeamIdAndFubAppIds(any(Long.class), anyList()))
        .thenReturn(refs);

    // Act
    factory.handWebhookEvent(fubRecord);

    // Assert
    verify(fubAppointmentRefDao).selectByFubTeamIdAndFubAppIds(888L, Arrays.asList("123", "456"));
    verify(createWebhookService).handleWebhookEvent(any(FubImportMsg.class), anyList(), anyMap());
  }

  // ========== Helper Methods ==========

  private FubImportMsg createValidFubImportMsg() {
    FubImportMsg msg = new FubImportMsg();
    msg.setData("{\"resourceIds\":[\"123\",\"456\"]}");
    msg.setType("appointmentsCreated");
    msg.setStateInfo(createTestFubStateInfo());
    return msg;
  }

  private FubStateInfo createTestFubStateInfo() {
    return FubStateInfo.builder()
        .ownershipId(999L)
        .ownershipScope(OwnershipScope.TEAM)
        .bindingAgentId(789L)
        .teamId(999L)
        .fubTeamId(888L)
        .build();
  }

  private FubAppointmentRef createTestFubAppointmentRef() {
    return FubAppointmentRef.builder()
        .id(1L)
        .appId(12345L)
        .leadId(456L)
        .fubAppId(123L)
        .teamId(999L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }

  private FubAppointmentRef createTestFubAppointmentRefWithId(Long id, Long fubAppId) {
    return FubAppointmentRef.builder()
        .id(id)
        .appId(12345L)
        .leadId(456L)
        .fubAppId(fubAppId)
        .teamId(999L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }
}

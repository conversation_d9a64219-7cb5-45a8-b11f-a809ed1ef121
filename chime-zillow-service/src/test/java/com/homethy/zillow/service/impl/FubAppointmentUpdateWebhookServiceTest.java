package com.homethy.zillow.service.impl;

import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.leadtask.AppointmentClientService;
import com.homethy.microservice.client.model.Appointment;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubImportMsg;
import com.homethy.zillow.model.constant.fub.FubStateInfo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FubAppointmentUpdateWebhookServiceTest {
  @InjectMocks
  private FubAppointmentUpdateWebhookService service;
  @Mock
  private FubTokenService fubTokenService;
  @Mock
  private FubClient fubClient;
  @Mock
  private AppointmentClientService appointmentClientService;

  @Test
  void testGetImportType() {
    assertEquals(FubImportTypeEnum.APPOINTMENTS_UPDATED, service.getImportType());
  }

  @Test
  void testMergeParticipants_onlyAdd() {
    Map<String, List<String>> existMap = new HashMap<>();
    existMap.put("lead", new ArrayList<>(Arrays.asList("1", "2")));
    Map<String, List<String>> updateMap = new HashMap<>();
    updateMap.put("lead", new ArrayList<>(Arrays.asList("2", "3")));
    com.homethy.microservice.client.model.Appointment exist =
        mock(com.homethy.microservice.client.model.Appointment.class);
    com.homethy.microservice.client.model.Appointment toUpdate =
        mock(com.homethy.microservice.client.model.Appointment.class);
    when(exist.getParticipants()).thenReturn(existMap);
    when(toUpdate.getParticipants()).thenReturn(updateMap);
    doNothing().when(toUpdate).setParticipants(anyMap());
    service.mergeParticipants(exist, toUpdate);
    verify(toUpdate, times(1)).setParticipants(anyMap());
  }

  @Test
  void testGetInput() {
    FubImportLog log = mock(FubImportLog.class);
    when(log.getImportContent()).thenReturn("{\"context\":{\"fubAppId\":123}}");
    FubAppointmentRef ref = service.getInput(log);
    assertNotNull(ref);
  }


  @Test
  void testHandle_fubAppointmentNotFound() {
    // Setup
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    FubStateInfo stateInfo = mock(FubStateInfo.class);
    FubAppointmentRef ref = mock(FubAppointmentRef.class);

    // Mock state info
    when(fubRecord.getStateInfo()).thenReturn(stateInfo);
    when(stateInfo.toOwnership()).thenReturn(new OwnershipInfo(1L, OwnershipScope.TEAM));

    // Mock token and appointment retrieval
    when(fubTokenService.getCallToken(any())).thenReturn("token123");
    when(ref.getFubAppId()).thenReturn(123L);
    when(fubClient.getAppointment(eq("token123"),eq(123L))).thenReturn(null);

    // Execute
    Appointment result = service.handle(fubRecord, ref);

    // Verify
    assertNull(result);
    verify(appointmentClientService, never()).update(anyLong(), any(Appointment.class));
  }


  @Test
  void testHandleWebhookEvent_noneExist() {
    // Setup
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    List<Long> fubAppointmentIds = Arrays.asList(123L, 456L);
    Map<Long, FubAppointmentRef> refMap = new HashMap<>();

    // Execute
    service.handleWebhookEvent(fubRecord, fubAppointmentIds, refMap);

    // Verify - should not call handle
    verify(fubTokenService, never()).getCallToken(any());
  }
} 

package com.homethy.zillow.service.impl;

import com.homethy.fub.service.FubAppointmentRefService;
import com.homethy.fub.service.FubTokenService;
import com.homethy.microservice.client.leadtask.AppointmentClientService;
import com.homethy.zillow.client.FubClient;
import com.homethy.zillow.model.constant.FubImportTypeEnum;
import com.homethy.zillow.model.constant.fub.FubAppointmentRef;
import com.homethy.zillow.model.constant.fub.FubImportLog;
import com.homethy.zillow.model.constant.fub.FubImportMsg;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FubAppointmentCreateWebhookServiceTest {
  @InjectMocks
  private FubAppointmentCreateWebhookService service;
  @Mock
  private FubTokenService fubTokenService;
  @Mock
  private FubClient fubClient;
  @Mock
  private AppointmentClientService appointmentClientService;
  @Mock
  private FubAppointmentRefService fubAppointmentRefService;

  @Test
  void testGetImportType() {
    assertEquals(FubImportTypeEnum.APPOINTMENTS_CREATED, service.getImportType());
  }

  @Test
  void testHandleWebhookEvent_allExist() {
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    List<Long> fubAppointmentIds = Arrays.asList(123L, 345L);
    Map<Long, FubAppointmentRef> refMap = new HashMap<>();
    refMap.put(123L, mock(FubAppointmentRef.class));
    refMap.put(345L, mock(FubAppointmentRef.class));
    service.handleWebhookEvent(fubRecord, fubAppointmentIds, refMap);
    // Should not throw and should not call create
  }

  @Test
  void testHandle_fubAppointmentNull_throws() {
    FubImportMsg fubRecord = mock(FubImportMsg.class);
    when(fubRecord.getStateInfo()).thenReturn(mock(com.homethy.zillow.model.constant.fub.FubStateInfo.class));
    when(fubTokenService.getCallToken(any())).thenReturn("token");
    when(fubClient.getAppointment(anyString(), anyLong())).thenReturn(null);
    assertThrows(com.homethy.i18n.util.MsgException.class,
        () -> service.handle(fubRecord, 123L));
  }

  @Test
  void testGetInput() {
    FubImportLog log = mock(FubImportLog.class);
    when(log.getImportContent()).thenReturn("{\"input\":123}");
    Long input = service.getInput(log);
    assertNotNull(input);
  }
} 
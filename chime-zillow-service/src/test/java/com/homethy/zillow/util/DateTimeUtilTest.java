package com.homethy.zillow.util;

import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class DateTimeUtilTest {

  @Test
  void testFormatDateWithUTC() {
    Date date = new Date();

    String result = DateTimeUtil.toFubFormat(date);

    assertNotNull(result);
    System.out.println(result);
  }

  @Test
  void parseToTimestamp() {
//    System.out.println(DateTimeUtil.parseFubFormat("2016-12-07T15:50:47 Z", "yyyy-MM-dd'T'HH:mm:ssX"));
//    System.out.println(DateTimeUtil.parseFubFormat("2016-12-07T15:50:47Z", "yyyy-MM-dd'T'HH:mm:ssXXX"));
    System.out.println(DateTimeUtil.parseToTimestamp("2016-12-07T15:50:47Z"));
    System.out.println(DateTimeUtil.parseToTimestamp("2025-07-17T06:04:57 -05:00"));
  }
}
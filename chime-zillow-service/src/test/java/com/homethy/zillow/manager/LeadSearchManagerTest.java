package com.homethy.zillow.manager;

import com.homethy.MockitoBase;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.leadsearch.api.LeadSearchService;
import com.homethy.microservice.client.leadsearch.lead.FilterRoleAssigneeBo;
import com.homethy.microservice.client.leadsearch.lead.LeadQueryBo;
import com.homethy.microservice.client.leadsearch.lead.SearchResultBo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

class LeadSearchManagerTest extends MockitoBase {
  @InjectMocks
  LeadSearchManager test;

  @Mock
  LeadSearchService leadSearchService;
  @Captor
  ArgumentCaptor<LeadQueryBo> captor;
  @Test
  void leadIdsByAssign() {
    SearchResultBo result = new SearchResultBo();
    Mockito.when(leadSearchService.search(captor.capture())).thenReturn(result);
    User agent = new User();
    long agentId = 234324234L;
    agent.setId(agentId);
    long teamId = 234234234L;
    agent.setTeamId(teamId);
    Assertions.assertSame(result,test.leadIdsByAssign(agent,10));
    LeadQueryBo query = captor.getValue();
    FilterRoleAssigneeBo fra = query.getRoleAssigneeList().get(0);
    Assertions.assertEquals(10, fra.getRoleId());
    Assertions.assertEquals(List.of(agentId), fra.getAssigneeToIds());
    Assertions.assertEquals(teamId, query.getTeamId());
  }

  @Test
  void scroll() {
    String scrollId = "324234234L";
    SearchResultBo result = new SearchResultBo();
    Mockito.when(leadSearchService.search(captor.capture())).thenReturn(result);
    Assertions.assertSame(result, test.scroll(scrollId));
    Assertions.assertEquals(scrollId, captor.getValue().getScrollId());
  }
}
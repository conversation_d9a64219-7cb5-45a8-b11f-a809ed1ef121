package com.homethy.zillow.client;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import com.homethy.microservice.client.NotificationService;
import com.homethy.util.jackson.JacksonUtils;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotificationManagerTest {

  @InjectMocks
  private NotificationManager notificationManager;

  @Mock
  private NotificationService notificationService;

  private static final long TEST_USER_ID = 12345L;
  private static final long TEST_LEAD_NUMBER = 67890L;
  private static final String TEST_FUB_CHANNEL = "Follow Up Boss";
  private static final String TEST_ZILLOW_CHANNEL = "Zillow";
  private static final int EXPECTED_LEAD_NOTIFY_TYPE_ID = 141;

  @BeforeEach
  void setUp() {
  }

  @Test
  void addNotification_WithFubChannel_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    String expectedJson = createExpectedJson(TEST_LEAD_NUMBER, TEST_FUB_CHANNEL);

    // Act
    notificationManager.addNotification(TEST_USER_ID, TEST_LEAD_NUMBER, TEST_FUB_CHANNEL);

    // Assert
    verify(notificationService).addNotification(
        eq(TEST_USER_ID),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithZillowChannel_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    String expectedJson = createExpectedJson(TEST_LEAD_NUMBER, TEST_ZILLOW_CHANNEL);

    // Act
    notificationManager.addNotification(TEST_USER_ID, TEST_LEAD_NUMBER, TEST_ZILLOW_CHANNEL);

    // Assert
    verify(notificationService).addNotification(
        eq(TEST_USER_ID),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithCustomChannel_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    String customChannel = "Custom Channel";
    String expectedJson = createExpectedJson(TEST_LEAD_NUMBER, customChannel);

    // Act
    notificationManager.addNotification(TEST_USER_ID, TEST_LEAD_NUMBER, customChannel);

    // Assert
    verify(notificationService).addNotification(
        eq(TEST_USER_ID),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithZeroValues_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    long zeroUserId = 0L;
    long zeroLeadNumber = 0L;
    String expectedJson = createExpectedJson(zeroLeadNumber, TEST_FUB_CHANNEL);

    // Act
    notificationManager.addNotification(zeroUserId, zeroLeadNumber, TEST_FUB_CHANNEL);

    // Assert
    verify(notificationService).addNotification(
        eq(zeroUserId),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithNullChannel_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    String nullChannel = null;
    String expectedJson = createExpectedJson(TEST_LEAD_NUMBER, nullChannel);

    // Act
    notificationManager.addNotification(TEST_USER_ID, TEST_LEAD_NUMBER, nullChannel);

    // Assert
    verify(notificationService).addNotification(
        eq(TEST_USER_ID),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithEmptyChannel_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    String emptyChannel = "";
    String expectedJson = createExpectedJson(TEST_LEAD_NUMBER, emptyChannel);

    // Act
    notificationManager.addNotification(TEST_USER_ID, TEST_LEAD_NUMBER, emptyChannel);

    // Assert
    verify(notificationService).addNotification(
        eq(TEST_USER_ID),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }

  @Test
  void addNotification_WithLargeNumbers_ShouldCallNotificationServiceCorrectly() {
    // Arrange
    long largeUserId = Long.MAX_VALUE;
    long largeLeadNumber = Long.MAX_VALUE;
    String expectedJson = createExpectedJson(largeLeadNumber, TEST_ZILLOW_CHANNEL);

    // Act
    notificationManager.addNotification(largeUserId, largeLeadNumber, TEST_ZILLOW_CHANNEL);

    // Assert
    verify(notificationService).addNotification(
        eq(largeUserId),
        eq(EXPECTED_LEAD_NOTIFY_TYPE_ID),
        eq(expectedJson)
    );
    verifyNoMoreInteractions(notificationService);
  }


  private String createExpectedJson(long leadNumber, String importChannel) {
    Map<String, Object> contentMap = new HashMap<>();
    contentMap.put("leadNumber", leadNumber);
    contentMap.put("importChannel", importChannel);
    return JacksonUtils.toJson(contentMap);
  }
}

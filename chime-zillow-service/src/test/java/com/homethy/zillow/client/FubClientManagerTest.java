package com.homethy.zillow.client;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.homethy.persistence.service.base.RedisService;
import com.homethy.util.jackson.JacksonUtils;
import com.homethy.zillow.model.constant.ZillowErrorCodeEnum;
import com.homethy.zillow.model.constant.fub.FubUserInfo;
import com.homethy.zillow.model.constant.fub.FubUserQueryResult;

@ExtendWith(MockitoExtension.class)
class FubClientManagerTest {

    @InjectMocks
    private FubClientManager fubClientManager;

    @Mock
    private FubClient fubClient;

    @Mock
    private RedisService redisService;

    private static final String TEST_TOKEN = "Bearer test-token";
    private static final long TEST_USER_ID = 12345L;
    private static final String CACHE_KEY = "getUserById-" + TEST_TOKEN + "-" + TEST_USER_ID;
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String INFO_CACHE_KEY = "getUserInfo-" + TEST_TOKEN + "-" + TEST_EMAIL;

    @BeforeEach
    void setUp() {
        //
    }

    @Test
    void getUserById_WithValidCache_ShouldReturnCachedUser() {
        // Arrange
        FubUserInfo expectedUser = createTestFubUserInfo();
        String cachedJson = JacksonUtils.toJson(expectedUser);
        try {
            System.out.println(JacksonUtils.fromJson(cachedJson, FubUserInfo.class));
            System.out.println(JacksonUtils.toJson(JacksonUtils.fromJson(cachedJson, FubUserInfo.class)));
        } catch (IOException e) {
            e.printStackTrace();
        }

        when(redisService.get(CACHE_KEY)).thenReturn(cachedJson);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());
        assertEquals(expectedUser.getEmail(), result.getEmail());
        assertEquals(expectedUser.getName(), result.getName());
        assertEquals(expectedUser.getRole(), result.getRole());

        verify(redisService).get(CACHE_KEY);
        verify(fubClient, never()).getUserById(anyString(), anyLong());
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }

    @Test
    void getUserById_WithNullCache_ShouldCallFubClientAndCacheResult() {
        // Arrange
        FubUserInfo expectedUser = createTestFubUserInfo();

        when(redisService.get(CACHE_KEY)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, TEST_USER_ID)).thenReturn(expectedUser);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());
        assertEquals(expectedUser.getEmail(), result.getEmail());
        assertEquals(expectedUser.getName(), result.getName());
        assertEquals(expectedUser.getRole(), result.getRole());

        verify(redisService).get(CACHE_KEY);
        verify(fubClient).getUserById(TEST_TOKEN, TEST_USER_ID);
        verify(redisService).set(CACHE_KEY, JacksonUtils.toJson(expectedUser), 60 * 60 * 2);
    }

    @Test
    void getUserById_WithEmptyCache_ShouldCallFubClientAndCacheResult() {
        // Arrange
        FubUserInfo expectedUser = createTestFubUserInfo();

        when(redisService.get(CACHE_KEY)).thenReturn("");
        // when(fubClient.getUserById(TEST_TOKEN,
        // TEST_USER_ID)).thenReturn(expectedUser);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);
        // Assert
        assertNull(result);
        verify(redisService).get(CACHE_KEY);
        // verify(fubClient).getUserById(TEST_TOKEN, TEST_USER_ID);
        // verify(redisService).set(CACHE_KEY, JacksonUtils.toJson(expectedUser), 60 *
        // 60 * 2);
    }

    @Test
    void getUserById_WithNullUserFromFubClient_ShouldCacheNullAndReturnNull() {
        // Arrange
        when(redisService.get(CACHE_KEY)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, TEST_USER_ID)).thenReturn(null);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNull(result);

        verify(redisService).get(CACHE_KEY);
        verify(fubClient).getUserById(TEST_TOKEN, TEST_USER_ID);
        verify(redisService).set(CACHE_KEY, "", 60 * 60 * 2);
    }

    @Test
    void getUserById_WithCachedNull_ShouldReturnNull() {
        // Arrange
        when(redisService.get(CACHE_KEY)).thenReturn("");

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNull(result);

        verify(redisService).get(CACHE_KEY);
        verify(fubClient, never()).getUserById(anyString(), anyLong());
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }


    @Test
    void getUserById_WithGeneralException_ShouldReturnNull() {
        // Arrange
        RuntimeException generalException = new RuntimeException("Network error");

        when(redisService.get(CACHE_KEY)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, TEST_USER_ID)).thenThrow(generalException);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNull(result);

        verify(redisService).get(CACHE_KEY);
        verify(fubClient).getUserById(TEST_TOKEN, TEST_USER_ID);
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }

    @Test
    void getUserById_WithInvalidJsonInCache_ShouldReturnNull() {
        // Arrange
        String invalidJson = "invalid json";

        when(redisService.get(CACHE_KEY)).thenReturn(invalidJson);
        when(fubClient.getUserById(TEST_TOKEN, TEST_USER_ID)).thenReturn(null);
        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, TEST_USER_ID);

        // Assert
        assertNull(result);

        verify(redisService).get(CACHE_KEY);
        verify(fubClient).getUserById(anyString(), anyLong());
        verify(redisService).set(anyString(), anyString(), anyInt());
    }

    @Test
    void getUserById_WithDifferentUserId_ShouldUseCorrectCacheKey() {
        // Arrange
        long differentUserId = 67890L;
        String differentCacheKey = "getUserById-" + TEST_TOKEN + "-" + differentUserId;
        FubUserInfo expectedUser = createTestFubUserInfo();

        when(redisService.get(differentCacheKey)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, differentUserId)).thenReturn(expectedUser);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, differentUserId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());

        verify(redisService).get(differentCacheKey);
        verify(fubClient).getUserById(TEST_TOKEN, differentUserId);
        verify(redisService).set(differentCacheKey, JacksonUtils.toJson(expectedUser), 60 * 60 * 2);
    }

    @Test
    void getUserById_WithDifferentToken_ShouldUseCorrectCacheKey() {
        // Arrange
        String differentToken = "Bearer different-token";
        String differentCacheKey = "getUserById-" + differentToken + "-" + TEST_USER_ID;
        FubUserInfo expectedUser = createTestFubUserInfo();

        when(redisService.get(differentCacheKey)).thenReturn(null);
        when(fubClient.getUserById(differentToken, TEST_USER_ID)).thenReturn(expectedUser);

        // Act
        FubUserInfo result = fubClientManager.getUserById(differentToken, TEST_USER_ID);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());

        verify(redisService).get(differentCacheKey);
        verify(fubClient).getUserById(differentToken, TEST_USER_ID);
        verify(redisService).set(differentCacheKey, JacksonUtils.toJson(expectedUser), 60 * 60 * 2);
    }

    @Test
    void getUserById_WithZeroUserId_ShouldHandleCorrectly() {
        // Arrange
        long zeroUserId = 0L;
        String zeroCacheKey = "getUserById-" + TEST_TOKEN + "-" + zeroUserId;
        FubUserInfo expectedUser = createTestFubUserInfo();
        expectedUser.setId(0L);

        when(redisService.get(zeroCacheKey)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, zeroUserId)).thenReturn(expectedUser);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, zeroUserId);

        // Assert
        assertNotNull(result);
        assertEquals(0L, result.getId());

        verify(redisService).get(zeroCacheKey);
        verify(fubClient).getUserById(TEST_TOKEN, zeroUserId);
        verify(redisService).set(zeroCacheKey, JacksonUtils.toJson(expectedUser), 60 * 60 * 2);
    }

    @Test
    void getUserById_WithNegativeUserId_ShouldHandleCorrectly() {
        // Arrange
        long negativeUserId = -1L;
        String negativeCacheKey = "getUserById-" + TEST_TOKEN + "-" + negativeUserId;

        when(redisService.get(negativeCacheKey)).thenReturn(null);
        when(fubClient.getUserById(TEST_TOKEN, negativeUserId)).thenReturn(null);

        // Act
        FubUserInfo result = fubClientManager.getUserById(TEST_TOKEN, negativeUserId);

        // Assert
        assertNull(result);

        verify(redisService).get(negativeCacheKey);
        verify(fubClient).getUserById(TEST_TOKEN, negativeUserId);
        verify(redisService).set(negativeCacheKey, "", 60 * 60 * 2);
    }

    @Test
    void getUserInfo_WithCachedEmptyString_ShouldReturnNullWithoutCallingClient() {
        when(redisService.get(INFO_CACHE_KEY)).thenReturn("");

        FubUserInfo result = fubClientManager.getUserInfo(TEST_TOKEN, TEST_EMAIL);

        assertNull(result);
        verify(redisService).get(INFO_CACHE_KEY);
        verify(fubClient, never()).getUsersByEmail(anyString(), anyString());
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }

    @Test
    void getUserInfo_WithValidCache_ShouldReturnCachedUser() {
        FubUserInfo expected = createTestFubUserInfo();
        String cachedJson = JacksonUtils.toJson(expected);
        when(redisService.get(INFO_CACHE_KEY)).thenReturn(cachedJson);

        FubUserInfo result = fubClientManager.getUserInfo(TEST_TOKEN, TEST_EMAIL);

        assertNotNull(result);
        assertEquals(expected.getId(), result.getId());
        assertEquals(expected.getEmail(), result.getEmail());
        verify(fubClient, never()).getUsersByEmail(anyString(), anyString());
        verify(redisService, never()).set(anyString(), anyString(), anyInt());
    }

    @Test
    void getUserInfo_WithInvalidJsonInCache_ShouldFetchFromClientAndCache() {
        FubUserInfo expected = createTestFubUserInfo();
        FubUserQueryResult queryResult = new FubUserQueryResult();
        queryResult.setUsers(Collections.singletonList(expected));

        when(redisService.get(INFO_CACHE_KEY)).thenReturn("{invalid json}");
        when(fubClient.getUsersByEmail(TEST_TOKEN, TEST_EMAIL)).thenReturn(queryResult);

        FubUserInfo result = fubClientManager.getUserInfo(TEST_TOKEN, TEST_EMAIL);

        assertNotNull(result);
        assertEquals(expected.getId(), result.getId());
        verify(fubClient).getUsersByEmail(TEST_TOKEN, TEST_EMAIL);
        verify(redisService).set(INFO_CACHE_KEY, JacksonUtils.toJson(expected), 60 * 60 * 2);
    }

    @Test
    void getUserInfo_WhenClientReturnsNull_ShouldCacheEmptyAndReturnNull() {
        when(redisService.get(INFO_CACHE_KEY)).thenReturn(null);
        when(fubClient.getUsersByEmail(TEST_TOKEN, TEST_EMAIL)).thenReturn(null);

        FubUserInfo result = fubClientManager.getUserInfo(TEST_TOKEN, TEST_EMAIL);

        assertNull(result);
        verify(fubClient).getUsersByEmail(TEST_TOKEN, TEST_EMAIL);
        verify(redisService).set(INFO_CACHE_KEY, "", 60 * 60 * 2);
    }

    @Test
    void getUserInfo_WhenClientReturnsEmptyList_ShouldCacheEmptyAndReturnNull() {
        FubUserQueryResult queryResult = new FubUserQueryResult();
        queryResult.setUsers(Collections.emptyList());

        when(redisService.get(INFO_CACHE_KEY)).thenReturn(null);
        when(fubClient.getUsersByEmail(TEST_TOKEN, TEST_EMAIL)).thenReturn(queryResult);

        FubUserInfo result = fubClientManager.getUserInfo(TEST_TOKEN, TEST_EMAIL);

        assertNull(result);
        verify(fubClient).getUsersByEmail(TEST_TOKEN, TEST_EMAIL);
        verify(redisService).set(INFO_CACHE_KEY, "", 60 * 60 * 2);
    }

    @Test
    void getUserInfo_WithDifferentTokenOrEmail_ShouldUseCorrectCacheKey() {
        String otherToken = "Bearer-2";
        String otherEmail = "<EMAIL>";
        String otherKey = "getUserInfo-" + otherToken + "-" + otherEmail;

        FubUserInfo expected = createTestFubUserInfo();
        FubUserQueryResult queryResult = new FubUserQueryResult();
        queryResult.setUsers(Collections.singletonList(expected));

        when(redisService.get(otherKey)).thenReturn(null);
        when(fubClient.getUsersByEmail(otherToken, otherEmail)).thenReturn(queryResult);

        FubUserInfo result = fubClientManager.getUserInfo(otherToken, otherEmail);

        assertNotNull(result);
        verify(redisService).get(otherKey);
        verify(fubClient).getUsersByEmail(otherToken, otherEmail);
        verify(redisService).set(otherKey, JacksonUtils.toJson(expected), 60 * 60 * 2);
    }

    private FubUserInfo createTestFubUserInfo() {
        FubUserInfo userInfo = new FubUserInfo();
        userInfo.setId(TEST_USER_ID);
        userInfo.setEmail("<EMAIL>");
        userInfo.setName("Test User");
        userInfo.setRole("agent");
        userInfo.setAccount(999L);
        userInfo.setOwner(true);
        return userInfo;
    }
}

package com.homethy.zillow.client;

import com.homethy.fub.service.FubLeadService;
import com.homethy.microservice.client.lead.LeadKernelService;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.zillow.model.constant.OwnershipInfo;
import com.homethy.zillow.model.constant.OwnershipScope;
import com.homethy.zillow.model.constant.fub.FubLead;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LeadManagerTest {

  @InjectMocks
  private LeadManager leadManager;

  @Mock
  private LeadKernelService leadKernelService;

  @Mock
  private FubLeadService fubLeadService;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  // ========== getLeadById Tests ==========

  @Test
  public void getLeadById_WithValidId_ShouldReturnLead() {
    // Arrange
    long leadId = 12345L;
    LeadBo expectedLead = createTestLeadBo();

    when(leadKernelService.getLeadById(leadId)).thenReturn(expectedLead);

    // Act
    LeadBo result = leadManager.getLeadById(leadId);

    // Assert
    assertEquals(expectedLead, result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithException_ShouldReturnNull() {
    // Arrange
    long leadId = 12345L;
    RuntimeException exception = new RuntimeException("Service unavailable");

    when(leadKernelService.getLeadById(leadId)).thenThrow(exception);

    // Act
    LeadBo result = leadManager.getLeadById(leadId);

    // Assert
    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithZeroId_ShouldCallServiceAndHandleResult() {
    // Arrange
    long leadId = 0L;

    when(leadKernelService.getLeadById(leadId)).thenReturn(null);

    // Act
    LeadBo result = leadManager.getLeadById(leadId);

    // Assert
    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithNegativeId_ShouldCallServiceAndHandleResult() {
    // Arrange
    long leadId = -1L;

    when(leadKernelService.getLeadById(leadId)).thenReturn(null);

    // Act
    LeadBo result = leadManager.getLeadById(leadId);

    // Assert
    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithServiceReturningNull_ShouldReturnNull() {
    // Arrange
    long leadId = 12345L;

    when(leadKernelService.getLeadById(leadId)).thenReturn(null);

    // Act
    LeadBo result = leadManager.getLeadById(leadId);

    // Assert
    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithDifferentExceptionTypes_ShouldReturnNull() {
    // Test with IllegalArgumentException
    long leadId = 12345L;
    IllegalArgumentException illegalArgException = new IllegalArgumentException("Invalid lead ID");

    when(leadKernelService.getLeadById(leadId)).thenThrow(illegalArgException);

    LeadBo result = leadManager.getLeadById(leadId);

    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  @Test
  public void getLeadById_WithNullPointerException_ShouldReturnNull() {
    // Test with NullPointerException
    long leadId = 12345L;
    NullPointerException npe = new NullPointerException("Null pointer in service");

    when(leadKernelService.getLeadById(leadId)).thenThrow(npe);

    LeadBo result = leadManager.getLeadById(leadId);

    assertNull(result);
    verify(leadKernelService).getLeadById(leadId);
  }

  // ========== getOwnershipFromLead Tests ==========

  @Test
  public void getOwnershipFromLead_WithValidLead_ShouldReturnOwnership() {
    // Arrange
    LeadBo lead = createTestLeadBo();
    FubLead fubLead = createTestFubLead();
    OwnershipInfo expectedOwnership = OwnershipInfo.builder()
        .ownershipId(fubLead.getOwnershipId())
        .ownershipScope(fubLead.getOwnershipScope())
        .build();

    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(fubLead);

    // Act
    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    // Assert
    assertEquals(expectedOwnership.getOwnershipId(), result.getOwnershipId());
    assertEquals(expectedOwnership.getOwnershipScope(), result.getOwnershipScope());
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithNoFubLead_ShouldReturnNull() {
    // Arrange
    LeadBo lead = createTestLeadBo();

    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(null);

    // Act
    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    // Assert
    assertNull(result);
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithNullLead_ShouldThrowException() {
    // This test verifies that the method doesn't handle null input gracefully
    // and will throw NullPointerException, which is the expected behavior

    // Act & Assert
    try {
      leadManager.getOwnershipFromLead(null);
    } catch (NullPointerException e) {
      // Expected behavior - method doesn't handle null input
    }
  }

  @Test
  public void getOwnershipFromLead_WithDifferentOwnershipScopes_ShouldReturnCorrectOwnership() {
    // Test with PERSONAL scope
    LeadBo lead = createTestLeadBo();
    FubLead fubLeadPersonal = createTestFubLeadWithScope(OwnershipScope.PERSONAL, 123L);

    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(fubLeadPersonal);

    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    assertEquals(123L, result.getOwnershipId());
    assertEquals(OwnershipScope.PERSONAL, result.getOwnershipScope());
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithOfficeScope_ShouldReturnCorrectOwnership() {
    // Test with OFFICE scope
    LeadBo lead = createTestLeadBo();
    FubLead fubLeadOffice = createTestFubLeadWithScope(OwnershipScope.OFFICE, 456L);

    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(fubLeadOffice);

    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    assertEquals(456L, result.getOwnershipId());
    assertEquals(OwnershipScope.OFFICE, result.getOwnershipScope());
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithFubLeadServiceException_ShouldPropagateException() {
    // Test that exceptions from FubLeadService are propagated
    LeadBo lead = createTestLeadBo();
    RuntimeException serviceException = new RuntimeException("FubLeadService error");

    when(fubLeadService.getLatestFubLead(lead.getId())).thenThrow(serviceException);

    // Act & Assert
    try {
      leadManager.getOwnershipFromLead(lead);
    } catch (RuntimeException e) {
      assertEquals("FubLeadService error", e.getMessage());
    }
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithLeadHavingZeroId_ShouldCallServiceWithZeroId() {
    // Test edge case where lead has ID 0
    LeadBo lead = new LeadBo();
    lead.setId(0L);

    when(fubLeadService.getLatestFubLead(0L)).thenReturn(null);

    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    assertNull(result);
    verify(fubLeadService).getLatestFubLead(0L);
  }

  // ========== Integration-style Tests ==========

  @Test
  public void getLeadById_AndThenGetOwnership_ShouldWorkTogether() {
    // This test verifies that the two methods can work together in a typical workflow
    long leadId = 12345L;
    LeadBo lead = createTestLeadBo();
    FubLead fubLead = createTestFubLead();

    // Setup mocks for both methods
    when(leadKernelService.getLeadById(leadId)).thenReturn(lead);
    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(fubLead);

    // Act - simulate typical usage pattern
    LeadBo retrievedLead = leadManager.getLeadById(leadId);
    OwnershipInfo ownership = leadManager.getOwnershipFromLead(retrievedLead);

    // Assert
    assertNotNull(retrievedLead);
    assertEquals(leadId, retrievedLead.getId());
    assertNotNull(ownership);
    assertEquals(fubLead.getOwnershipId(), ownership.getOwnershipId());
    assertEquals(fubLead.getOwnershipScope(), ownership.getOwnershipScope());

    verify(leadKernelService).getLeadById(leadId);
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  @Test
  public void getOwnershipFromLead_WithCompletelyPopulatedFubLead_ShouldReturnCompleteOwnership() {
    // Test with a fully populated FubLead to ensure all fields are properly mapped
    LeadBo lead = createTestLeadBo();
    FubLead fubLead = FubLead.builder()
        .id(999L)
        .leadId(12345L)
        .peopleId(456L)
        .fubStatus(2)
        .fubStageId(5)
        .fubStageName("Qualified")
        .ownershipScope(OwnershipScope.PERSONAL)
        .ownershipId(789L)
        .teamId(111L)
        .fubTeamId(222L)
        .deleteFlag(false)
        .createTime(new Date())
        .updateTime(new Date())
        .build();

    when(fubLeadService.getLatestFubLead(lead.getId())).thenReturn(fubLead);

    OwnershipInfo result = leadManager.getOwnershipFromLead(lead);

    assertNotNull(result);
    assertEquals(789L, result.getOwnershipId());
    assertEquals(OwnershipScope.PERSONAL, result.getOwnershipScope());
    verify(fubLeadService).getLatestFubLead(lead.getId());
  }

  // ========== Helper Methods ==========

  private LeadBo createTestLeadBo() {
    LeadBo lead = new LeadBo();
    lead.setId(12345L);
    lead.setTeamId(999L);
    lead.setAssignedToId(789L);
    return lead;
  }

  private FubLead createTestFubLead() {
    return FubLead.builder()
        .id(1L)
        .leadId(12345L)
        .peopleId(123L)
        .fubStatus(1)
        .fubStageId(1)
        .fubStageName("New")
        .ownershipScope(OwnershipScope.TEAM)
        .ownershipId(999L)
        .teamId(999L)
        .fubTeamId(888L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }

  private FubLead createTestFubLeadWithScope(OwnershipScope scope, long ownershipId) {
    return FubLead.builder()
        .id(1L)
        .leadId(12345L)
        .peopleId(123L)
        .fubStatus(1)
        .fubStageId(1)
        .fubStageName("New")
        .ownershipScope(scope)
        .ownershipId(ownershipId)
        .teamId(999L)
        .fubTeamId(888L)
        .createTime(new Date())
        .updateTime(new Date())
        .build();
  }
}
